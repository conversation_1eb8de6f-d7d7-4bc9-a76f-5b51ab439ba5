{"buildCommand": "npm run vercel-build", "outputDirectory": "dist", "framework": null, "installCommand": "npm ci", "regions": ["gru1"], "version": 2, "cleanUrls": true, "trailingSlash": false, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}