# 📺 Neko TV - Guia de Configuração do Ticker

Este guia explica como configurar e personalizar o sistema de ticker de mensagens do Neko TV.

## 📋 Estrutura da Configuração

O ticker usa um arquivo JSON com a seguinte estrutura:

```json
{
  "enabled": true,
  "messages": [
    {
      "id": "identificador_unico",
      "text": "Texto da mensagem",
      "link": {
        "text": "Texto do link",
        "url": "https://exemplo.com",
        "target": "_blank"
      },
      "suffix": "Texto após o link (opcional)",
      "duration": 15,
      "priority": 1,
      "enabled": true
    }
  ],
  "defaultDuration": 15
}
```

## 🔧 Propriedades Explicadas

### Configuração Global
- **`enabled`**: `true/false` - Ativa ou desativa o ticker completamente
- **`defaultDuration`**: `number` - Duração padrão em segundos para mensagens sem duração específica

### Propriedades das Mensagens
- **`id`**: `string` - Identificador único da mensagem (obrigatório)
- **`text`**: `string` - Texto principal da mensagem (obrigatório)
- **`link`**: `object` - Link opcional dentro da mensagem
  - **`text`**: `string` - Texto que aparece no botão do link
  - **`url`**: `string` - URL de destino
  - **`target`**: `"_blank" | "_self"` - Como abrir o link
- **`suffix`**: `string` - Texto que aparece após o link (opcional)
- **`duration`**: `number` - Tempo em segundos que a mensagem fica visível
- **`priority`**: `number` - Ordem de exibição (menor número = maior prioridade)
- **`enabled`**: `boolean` - Ativa/desativa esta mensagem específica

## 📁 Localização dos Arquivos

### Desenvolvimento Local
- **Arquivo**: `public/ticker-config.json`
- **URL de acesso**: `http://localhost:5173/ticker-config.json`

### Produção
- **Pastebin**: Configure sua própria URL do Pastebin
- **Backup local**: O sistema usa o arquivo local se a fonte externa falhar

## 🎯 Exemplos de Configuração

### Exemplo Básico
```json
{
  "enabled": true,
  "messages": [
    {
      "id": "welcome",
      "text": "🎉 Bem-vindo ao Neko TV!",
      "duration": 10,
      "priority": 1,
      "enabled": true
    }
  ],
  "defaultDuration": 15
}
```

### Exemplo com Link
```json
{
  "enabled": true,
  "messages": [
    {
      "id": "promocao",
      "text": "🔥 Oferta especial! ",
      "link": {
        "text": "Aproveite agora",
        "url": "https://nekotv.top/promocao",
        "target": "_blank"
      },
      "duration": 20,
      "priority": 1,
      "enabled": true
    }
  ],
  "defaultDuration": 15
}
```

### Exemplo Completo
```json
{
  "enabled": true,
  "messages": [
    {
      "id": "welcome",
      "text": "🎉 Bem-vindo ao Neko TV! ",
      "link": {
        "text": "Site oficial",
        "url": "https://nekotv.vercel.app",
        "target": "_blank"
      },
      "suffix": " - A melhor experiência de streaming!",
      "duration": 15,
      "priority": 2,
      "enabled": true
    },
    {
      "id": "quality",
      "text": "📺 Assista em 4K, Full HD e HD com a melhor qualidade!",
      "duration": 12,
      "priority": 3,
      "enabled": true
    }
  ],
  "defaultDuration": 15
}
```

## 🚀 Como Fazer Alterações

### Para ChatGPT - Prompt Sugerido:
```
Preciso que você crie/modifique a configuração do ticker do Neko TV seguindo estas especificações:

[DESCREVA SUAS NECESSIDADES AQUI]

Use a estrutura JSON do arquivo TICKER_CONFIG_GUIDE.md como base. 
Retorne apenas o JSON válido, sem explicações adicionais.

Configuração atual: [COLE A CONFIGURAÇÃO ATUAL AQUI SE HOUVER]
```

### Processo de Atualização:
1. **Desenvolvimento**: Edite `public/ticker-config.json`
2. **Produção**: Atualize sua fonte de configuração (ex: Pastebin)
3. **Teste**: O sistema carrega automaticamente em ~5 minutos (cache)

## ⚡ Dicas e Boas Práticas

### IDs Únicos
- Use IDs descritivos: `"welcome"`, `"promocao"`, `"manutencao"`
- Evite caracteres especiais nos IDs

### Duração das Mensagens
- **Mensagens curtas**: 8-12 segundos
- **Mensagens médias**: 15-20 segundos  
- **Mensagens longas**: 25-30 segundos

### Prioridades
- **Urgente/Importante**: priority 1-2
- **Normal**: priority 3-5
- **Informativo**: priority 6-10

### Links
- **Externos**: sempre use `"target": "_blank"`
- **Internos**: use `"target": "_self"` e URLs relativas (`"/channels"`)

### Emojis
- Use emojis para chamar atenção: 🎉 🔥 ⭐ 📺 💬 ⚠️
- Mantenha consistência visual

## 🔄 Sistema de Fallback

O sistema possui múltiplos fallbacks:
1. **Fonte externa** (ex: Pastebin)
2. **Arquivo local** (`public/ticker-config.json`)
3. **Cache do navegador**
4. **Configuração padrão** (hardcoded)

## 🎨 Personalização Visual

O ticker se adapta automaticamente ao tema:
- **Dark Mode**: Fundo escuro transparente
- **Light Mode**: Fundo claro transparente
- **Efeitos**: Glassmorphism com blur
- **Animações**: Scroll suave e efeitos sutis

## 📱 Responsividade

- **Desktop**: Altura 60px
- **Mobile**: Altura 56px
- **Texto**: Tamanho adaptável
- **Links**: Touch-friendly

## 📝 Templates Prontos

### Promoção/Oferta
```json
{
  "id": "promocao_[DATA]",
  "text": "🔥 [DESCRIÇÃO DA OFERTA] ",
  "link": {
    "text": "Aproveitar agora",
    "url": "[URL_DA_PROMOCAO]",
    "target": "_blank"
  },
  "duration": 25,
  "priority": 1,
  "enabled": true
}
```

### Manutenção/Aviso
```json
{
  "id": "manutencao_[DATA]",
  "text": "⚠️ Manutenção programada [DATA/HORA] - ",
  "link": {
    "text": "Saiba mais",
    "url": "[URL_DETALHES]",
    "target": "_blank"
  },
  "duration": 20,
  "priority": 1,
  "enabled": true
}
```

### Novo Conteúdo
```json
{
  "id": "novo_conteudo_[ID]",
  "text": "🎬 Novo: [NOME_DO_CONTEUDO] - ",
  "link": {
    "text": "Assistir agora",
    "url": "/[CATEGORIA]/[ID]",
    "target": "_self"
  },
  "duration": 18,
  "priority": 2,
  "enabled": true
}
```

## 🔧 Troubleshooting

### Ticker não aparece
- ✅ Verifique se `"enabled": true`
- ✅ Confirme se há mensagens com `"enabled": true`
- ✅ Verifique o console do navegador para erros

### Mensagem não muda
- ✅ Verifique se há múltiplas mensagens ativas
- ✅ Confirme as durações configuradas
- ✅ Limpe o cache do navegador

### Link não funciona
- ✅ Verifique a URL (deve incluir `http://` ou `https://`)
- ✅ Confirme o `target` (`_blank` para externos, `_self` para internos)
- ✅ Teste a URL separadamente

### Configuração não carrega
- ✅ Valide o JSON em [jsonlint.com](https://jsonlint.com)
- ✅ Verifique vírgulas e aspas
- ✅ Confirme se a fonte externa está acessível

## 🎯 Prompt Otimizado para ChatGPT

```
Atue como um especialista em configuração do Neko TV Ticker.

CONTEXTO: O ticker é um sistema de mensagens rotativas que aparece na parte superior da aplicação.

TAREFA: [DESCREVA SUA NECESSIDADE]

CONFIGURAÇÃO ATUAL:
[COLE O JSON ATUAL AQUI]

REQUISITOS:
- Retorne apenas JSON válido
- Use IDs únicos e descritivos
- Defina durações apropriadas (8-30s)
- Configure prioridades (1=mais importante)
- Use emojis relevantes
- Links externos com target="_blank"
- Links internos com target="_self"

ESTRUTURA BASE:
{
  "enabled": true,
  "messages": [...],
  "defaultDuration": 15
}

Gere a configuração otimizada seguindo as boas práticas do TICKER_CONFIG_GUIDE.md
```

---

**💡 Lembre-se**: Sempre teste as configurações em desenvolvimento antes de aplicar em produção!

**🔗 Links Úteis**:
- [JSON Validator](https://jsonlint.com)
- [Pastebin](https://pastebin.com)
- [Arquivo Local](public/ticker-config.json)
