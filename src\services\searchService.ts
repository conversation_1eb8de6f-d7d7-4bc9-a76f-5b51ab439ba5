/**
 * Serviço de pesquisa avançada para o IPTV Player
 * Implementa algoritmos de busca sofisticados para encontrar conteúdo rapidamente
 */

// Função de normalização de texto para comparações mais eficazes
export const normalizeText = (text: string): string => {
  if (!text) return '';
  
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^\w\s]/g, ' ')        // Substitui pontuação por espaços
    .replace(/\s+/g, ' ')            // Remove múltiplos espaços
    .trim();
};

// Interface para itens pesquisáveis (filmes, séries, etc)
export interface Searchable {
  id: string;
  name: string;
  [key: string]: any;
}

// Pontuação para diferentes tipos de correspondências
const MATCH_SCORES = {
  EXACT: 100,       // Correspondência exata
  PREFIX: 80,       // Correspondência no início do título
  TOKEN_PREFIX: 70, // Correspondência no início de uma palavra/token
  CONTAINS: 60,     // Contém a consulta em qualquer lugar
  TOKEN_PARTIAL: 50 // Correspondência parcial em uma palavra/token
};

/**
 * Calcula a pontuação de relevância de um item para uma consulta de pesquisa
 * @param item Item a ser avaliado
 * @param query Consulta de pesquisa normalizada
 * @returns Pontuação de relevância (0 = sem correspondência, maior = mais relevante)
 */
export const getSearchScore = (item: Searchable, query: string): number => {
  if (!query) return 0;
  
  const normalizedName = normalizeText(item.name);
  const normalizedQuery = normalizeText(query);
  
  // Correspondência exata
  if (normalizedName === normalizedQuery) {
    return MATCH_SCORES.EXACT;
  }
  
  // Correspondência de prefixo (começa com a consulta)
  if (normalizedName.startsWith(normalizedQuery)) {
    return MATCH_SCORES.PREFIX;
  }
  
  // Divida em tokens (palavras) para busca parcial
  const tokens = normalizedName.split(' ');
  
  // Verifique se algum token começa com a consulta
  for (const token of tokens) {
    if (token.startsWith(normalizedQuery)) {
      return MATCH_SCORES.TOKEN_PREFIX;
    }
  }
  
  // Verifica se contém a consulta em qualquer lugar
  if (normalizedName.includes(normalizedQuery)) {
    return MATCH_SCORES.CONTAINS;
  }
  
  // Verifica correspondências parciais em palavras/tokens
  for (const token of tokens) {
    if (token.includes(normalizedQuery)) {
      return MATCH_SCORES.TOKEN_PARTIAL;
    }
  }
  
  // Verifica se todos os caracteres da consulta aparecem na ordem correta, mas não necessariamente adjacentes
  // Útil para acrônimos ou digitação rápida, como "GdT" para "Game of Thrones"
  let lastFoundIndex = -1;
  let allCharsFound = true;
  
  for (const char of normalizedQuery) {
    const foundIndex = normalizedName.indexOf(char, lastFoundIndex + 1);
    if (foundIndex === -1) {
      allCharsFound = false;
      break;
    }
    lastFoundIndex = foundIndex;
  }
  
  if (allCharsFound) {
    return MATCH_SCORES.TOKEN_PARTIAL - 10; // Pontuação um pouco menor
  }
  
  // Nenhuma correspondência encontrada
  return 0;
};

/**
 * Função de busca avançada que suporta correspondências parciais e ordenação por relevância
 * @param items Lista de itens pesquisáveis
 * @param query Consulta de pesquisa
 * @param minScore Pontuação mínima para considerar uma correspondência (0-100)
 * @returns Itens correspondentes ordenados por relevância
 */
export const performAdvancedSearch = <T extends Searchable>(
  items: T[],
  query: string,
  minScore: number = 10
): T[] => {
  if (!query || query.length === 0) return [];
  
  const itemsWithScores = items.map(item => ({
    item,
    score: getSearchScore(item, query)
  }));
  
  // Filtra resultados com pontuação mínima e ordena por pontuação (mais alta primeiro)
  return itemsWithScores
    .filter(({ score }) => score >= minScore)
    .sort((a, b) => b.score - a.score)
    .map(({ item }) => item);
};

/**
 * Gera sugestões de pesquisa com base nos resultados atuais e histórico
 * @param query Consulta atual
 * @param items Itens disponíveis para pesquisa
 * @param searchHistory Histórico de pesquisas (opcional)
 * @returns Lista de sugestões de pesquisa
 */
export const generateSearchSuggestions = (
  query: string,
  items: Searchable[],
  searchHistory: string[] = [],
  maxSuggestions: number = 5
): string[] => {
  if (!query || query.length < 2) return [];
  
  const normalizedQuery = normalizeText(query);
  const suggestions: string[] = [];
  
  // Primeiro, verifique o histórico de pesquisa
  const historySuggestions = searchHistory
    .filter(historyItem => normalizeText(historyItem).startsWith(normalizedQuery))
    .slice(0, 2); // Limitar sugestões do histórico
  
  suggestions.push(...historySuggestions);
  
  // Depois, encontre nomes de itens populares que correspondam
  const matchingItems = performAdvancedSearch(items, query, 60)
    .slice(0, maxSuggestions - suggestions.length);
  
  suggestions.push(...matchingItems.map(item => item.name));
  
  // Remover duplicatas e limitar ao número máximo
  return [...new Set(suggestions)].slice(0, maxSuggestions);
};

/**
 * Armazena consulta de pesquisa no histórico local
 */
export const saveSearchToHistory = (query: string, maxHistory: number = 20): void => {
  if (!query || query.length < 3) return;
  
  try {
    const existingHistory = getSearchHistory();
    // Remover a consulta se já existir (para movê-la para o topo)
    const filteredHistory = existingHistory.filter(item => item !== query);
    // Adicionar a consulta ao início e limitar o tamanho
    const newHistory = [query, ...filteredHistory].slice(0, maxHistory);
    
    localStorage.setItem('searchHistory', JSON.stringify(newHistory));
  } catch (error) {
    console.error('Erro ao salvar histórico de pesquisa:', error);
  }
};

/**
 * Obtém o histórico de pesquisas do armazenamento local
 */
export const getSearchHistory = (): string[] => {
  try {
    const history = localStorage.getItem('searchHistory');
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('Erro ao ler histórico de pesquisa:', error);
    return [];
  }
};

/**
 * Limpa o histórico de pesquisas
 */
export const clearSearchHistory = (): void => {
  try {
    localStorage.removeItem('searchHistory');
  } catch (error) {
    console.error('Erro ao limpar histórico de pesquisa:', error);
  }
}; 