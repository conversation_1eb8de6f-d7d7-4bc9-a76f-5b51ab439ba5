import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Typography, Button, Card, CardContent, Grid, useTheme, CardMedia, Container, Paper, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import MovieIcon from '@mui/icons-material/Movie';
import TvIcon from '@mui/icons-material/Tv';
import WindowControls from '../components/WindowControls';
import BackgroundEffects from '../components/BackgroundEffects';

// Cartão de opção estilizado com animações
const OptionCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: 16,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  overflow: 'hidden',
  background: theme.palette.mode === 'dark' 
    ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.8))'
    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(249, 250, 251, 0.8))',
  backdropFilter: 'blur(8px)',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 30px rgba(0, 0, 0, 0.2)',
    border: `1px solid ${theme.palette.primary.main}`,
  },
}));

// Botão estilizado dentro do cartão
const OptionButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(2),
  borderRadius: 8,
  padding: theme.spacing(1, 3),
  fontWeight: 600,
  textTransform: 'none',
  fontSize: '0.95rem',
  boxShadow: '0 4px 10px rgba(14, 165, 233, 0.3)',
  background: 'linear-gradient(90deg, #0ea5e9, #38bdf8)',
  '&:hover': {
    background: 'linear-gradient(90deg, #0ea5e9, #38bdf8)',
    boxShadow: '0 6px 15px rgba(14, 165, 233, 0.4)',
  },
}));

// Ícone estilizado para o cartão
const OptionIcon = styled(Box)(({ theme }) => ({
  width: 60,
  height: 60,
  borderRadius: 12,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  background: 'linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(56, 189, 248, 0.2))',
  color: theme.palette.primary.main,
  boxShadow: '0 4px 12px rgba(14, 165, 233, 0.15)',
  '& svg': {
    fontSize: 32,
  },
}));

// Estilizando o container principal
const PageContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  padding: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  background: theme.palette.mode === 'dark' 
    ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)' 
    : 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundImage: `radial-gradient(rgba(14, 165, 233, 0.15) 1px, transparent 1px)`,
    backgroundSize: '30px 30px',
    pointerEvents: 'none',
    zIndex: 0,
  }
}));

// Definição das opções de conteúdo
const contentOptions = [
    {
      title: 'TV Ao Vivo',
    icon: <LiveTvIcon />,
      description: 'Assista seus canais de TV favoritos em tempo real com a melhor qualidade',
      path: '/channels',
    color: '#0ea5e9'
    },
    {
      title: 'Filmes',
    icon: <MovieIcon />,
      description: 'Explore nossa biblioteca de filmes com milhares de títulos para todos os gostos',
      path: '/movies',
    color: '#f43f5e'
    },
    {
      title: 'Séries',
    icon: <TvIcon />,
      description: 'Acompanhe suas séries preferidas com episódios atualizados regularmente',
      path: '/series',
    color: '#8b5cf6'
    }
  ];

// Animações para os elementos
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
      staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
  hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: 'spring',
      stiffness: 100,
      damping: 15
    }
  }
};

const logoVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20,
      delay: 0.1
    }
  }
};

const ContentSelection: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        position: 'relative',
        background: 'radial-gradient(ellipse at center, rgba(37, 99, 235, 0.15) 0%, rgba(0, 0, 0, 0.9) 70%)',
        overflow: 'hidden'
      }}
    >
      <BackgroundEffects />
      
      <Container 
        maxWidth="xl" 
        sx={{ 
          py: { xs: 2, md: 4 },
          position: 'relative',
          zIndex: 2
        }}
      >
        {/* Logo */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <LiveTvIcon 
            sx={{ 
              fontSize: { xs: 48, md: 60 }, 
              color: 'primary.main',
              mb: 2
            }} 
          />
          <Typography
            variant="h3"
            component="h1"
            sx={{
              fontWeight: 700,
              fontSize: { xs: '2rem', md: '2.5rem' },
              background: 'linear-gradient(135deg, #0ea5e9, #38bdf8)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 4
            }}
          >
            Neko TV
          </Typography>
        </Box>

        {/* Cards das opções de conteúdo */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={4} justifyContent="center">
            {contentOptions.map((option, index) => (
              <Grid item xs={12} sm={6} md={4} key={option.title}>
                <motion.div variants={itemVariants}>
                  <OptionCard onClick={() => navigate(option.path)}>
                    <Box sx={{ position: 'relative', height: '100%' }}>
                      {/* Imagem de fundo com gradiente de sobreposição */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          background: option.title === 'TV Ao Vivo'
                            ? 'linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(56, 189, 248, 0.1))'
                            : option.title === 'Filmes'
                            ? 'linear-gradient(135deg, rgba(244, 63, 94, 0.2), rgba(251, 113, 133, 0.1))'
                            : 'linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(167, 139, 250, 0.1))',
                          opacity: 0.3
                        }}
                      />
                      <Box sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        background: theme.palette.mode === 'dark'
                          ? `linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.9))`
                          : `linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(249, 250, 251, 0.95))`,
                        zIndex: 1
                      }} />
                      
                      {/* Conteúdo do cartão */}
                      <CardContent sx={{ 
                          position: 'relative', 
                          zIndex: 2, 
                          textAlign: 'center', 
                        p: 4, 
                          height: '100%',
                        display: 'flex', 
                        flexDirection: 'column', 
                        alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <OptionIcon>
                            {option.icon}
                          </OptionIcon>
                          <Typography 
                            variant="h5" 
                            component="h2" 
                            gutterBottom
                            sx={{ 
                              fontWeight: 600,
                              mb: 1,
                              color: theme.palette.mode === 'dark' ? '#fff' : '#000'
                            }}
                          >
                            {option.title}
                          </Typography>
                          <Typography 
                            variant="body1" 
                            color="text.secondary"
                            sx={{ 
                              mb: 3,
                              minHeight: 60
                            }}
                          >
                            {option.description}
                          </Typography>
                          <OptionButton 
                          variant="contained"
                          fullWidth
                            onClick={() => navigate(option.path)}
                          >
                              Acessar
                            </OptionButton>
                        </CardContent>
                      </Box>
                    </OptionCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
          
          {/* Footer */}
          <Box sx={{ mt: 8, textAlign: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              © {new Date().getFullYear()} Neko TV • Todos os direitos reservados
            </Typography>
          </Box>
        </Container>
      </Box>
    );
  };

export default ContentSelection; 