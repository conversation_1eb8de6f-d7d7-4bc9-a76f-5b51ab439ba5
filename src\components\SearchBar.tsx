import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  TextField, 
  InputAdornment, 
  IconButton, 
  Box, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  CircularProgress, 
  Typography, 
  Chip,
  Divider,
  Popper,
  Grow,
  ClickAwayListener,
  useTheme,
  alpha
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { 
  generateSearchSuggestions, 
  getSearchHistory, 
  saveSearchToHistory, 
  clearSearchHistory, 
  Searchable
} from '../services/searchService';

// Configurações para pesquisa
const MIN_QUERY_LENGTH = 1;
const MAX_SUGGESTIONS = 6;
const MAX_RESULTS = 20;

export interface SearchBarProps {
  items: Searchable[];
  placeholder?: string;
  onSearch: (results: Searchable[], query: string) => void;
  onSelectItem?: (item: Searchable) => void;
  fullWidth?: boolean;
  autoFocus?: boolean;
  globalSearchToggle?: boolean;
  onGlobalSearchChange?: (enabled: boolean) => void;
  isGlobalSearch?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  items,
  placeholder = 'Pesquisar',
  onSearch,
  onSelectItem,
  fullWidth = true,
  autoFocus = false,
  globalSearchToggle = false,
  onGlobalSearchChange,
  isGlobalSearch = false
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const anchorRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();

  // Carregar histórico de pesquisa quando o componente montar
  useEffect(() => {
    setSearchHistory(getSearchHistory());
  }, []);

  // Manipuladores de eventos
  const handleQueryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(event.target.value);
    // Não disparamos mais pesquisas automáticas ao digitar
    
    // Se o campo estiver vazio, limpar os resultados
    if (event.target.value.length === 0) {
      onSearch([], '');
    }
  };

  const handleClearQuery = () => {
    setQuery('');
    onSearch([], '');
    inputRef.current?.focus();
  };

  const handleSelectSuggestion = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    
    // Executar a pesquisa imediatamente ao selecionar uma sugestão
    executeSearch(suggestion);
  };

  // Função para executar a pesquisa
  const executeSearch = (query: string) => {
    if (!query.trim()) {
      onSearch([], '');
      return;
    }

    setIsLoading(true);
    
    console.log(`SearchBar: Executando busca com query "${query}" em ${items.length} itens`);
    
    const results = performAdvancedSearch(items, query);
    
    console.log(`SearchBar: Encontrados ${results.length} resultados para "${query}"`);
    
    // Adicionar à história de pesquisa
    saveSearchToHistory(query);
    
    onSearch(results.slice(0, MAX_RESULTS), query);
    setIsLoading(false);
    setShowSuggestions(false);
  };

  // Função para lidar com o envio do formulário
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log(`SearchBar: Formulário enviado com query "${query}"`);
    executeSearch(query);
  };

  const handleClearHistory = () => {
    clearSearchHistory();
    setSearchHistory([]);
  };

  const handleClickAway = () => {
    setShowSuggestions(false);
  };

  const handleFocus = () => {
    // Mostrar histórico de pesquisa ao focar no campo
    if (query.length === 0 && searchHistory.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Gerar sugestões apenas quando o usuário para de digitar
  const updateSuggestions = () => {
    if (query.length >= MIN_QUERY_LENGTH) {
      const newSuggestions = generateSearchSuggestions(
        query, 
        items, 
        searchHistory,
        MAX_SUGGESTIONS
      );
      setSuggestions(newSuggestions);
      
      // Mostrar sugestões apenas se houver resultados
      if (newSuggestions.length > 0) {
        setShowSuggestions(true);
      }
    }
  };

  // Mostrar sugestões ao clicar no campo quando já tem texto
  const handleClick = () => {
    if (query.length >= MIN_QUERY_LENGTH) {
      updateSuggestions();
    } else if (query.length === 0 && searchHistory.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Atualizar sugestões quando o usuário digita (sem debounce)
  useEffect(() => {
    if (query.length >= MIN_QUERY_LENGTH) {
      updateSuggestions();
    } else {
      setSuggestions([]);
    }
  }, [query, items, searchHistory]);

  // Função avançada de pesquisa
  const performAdvancedSearch = (items: Searchable[], searchQuery: string): Searchable[] => {
    if (!searchQuery.trim()) return [];
    
    const query = searchQuery.toLowerCase();
    console.log(`SearchBar performAdvancedSearch: Buscando "${query}" em ${items.length} itens`);
    
    // Verificar tipos de itens para ajudar na depuração
    if (items.length > 0) {
      const firstItem = items[0];
      const sample = {
        hasName: 'name' in firstItem,
        nameType: 'name' in firstItem ? typeof firstItem.name : 'N/A',
        hasTitle: 'title' in firstItem,
        titleType: 'title' in firstItem ? typeof firstItem.title : 'N/A',
        hasId: 'id' in firstItem,
        idType: 'id' in firstItem ? typeof firstItem.id : 'N/A',
        hasGenre: 'genre' in firstItem,
        hasYear: 'year' in firstItem
      };
      console.log('SearchBar: Amostra de item para pesquisa:', sample);
    }
    
    const matchedItems = items.filter((item) => {
      // Verificar nome
      if ('name' in item && typeof item.name === 'string') {
        if (item.name.toLowerCase().includes(query)) return true;
      }
      
      // Verificar título
      if ('title' in item && typeof item.title === 'string') {
        if (item.title.toLowerCase().includes(query)) return true;
      }
      
      // Verificar gêneros se disponível
      if ('genre' in item && typeof item.genre === 'string') {
        if (item.genre.toLowerCase().includes(query)) return true;
      }
      
      // Verificar ano se disponível
      if ('year' in item && item.year) {
        if (String(item.year).includes(query)) return true;
      }
      
      return false;
    });
    
    console.log(`SearchBar: Encontrados ${matchedItems.length} resultados em performAdvancedSearch`);
    if (matchedItems.length > 0) {
      const firstMatch = matchedItems[0];
      const displayInfo: Record<string, any> = { id: 'N/A' };
      
      if ('id' in firstMatch) {
        displayInfo.id = firstMatch.id;
      }
      
      if ('name' in firstMatch && typeof firstMatch.name === 'string') {
        displayInfo.name = firstMatch.name;
      } else if ('title' in firstMatch && typeof firstMatch.title === 'string') {
        displayInfo.title = firstMatch.title;
      }
      
      console.log('SearchBar: Primeiro item encontrado:', displayInfo);
    }
    
    return matchedItems;
  };

  // Função para salvar busca no histórico
  const saveSearchToHistory = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Atualizar histórico de pesquisa local
      setSearchHistory([searchQuery, ...searchHistory.filter(item => item !== searchQuery)].slice(0, 20));
      
      // Salvar no localStorage
      try {
        const updatedHistory = [searchQuery, ...searchHistory.filter(item => item !== searchQuery)].slice(0, 20);
        localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
      } catch (error) {
        console.error('Erro ao salvar histórico de pesquisa:', error);
      }
    }
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box 
        ref={anchorRef}
        sx={{ 
          position: 'relative',
          width: fullWidth ? '100%' : 'auto', 
          maxWidth: fullWidth ? '100%' : 600
        }}
      >
        <form onSubmit={handleSubmit}>
          <TextField
            inputRef={inputRef}
            placeholder={placeholder}
            value={query}
            onChange={handleQueryChange}
            onFocus={handleFocus}
            onClick={handleClick}
            autoFocus={autoFocus}
            variant="outlined"
            fullWidth
            size="medium"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton
                    aria-label="realizar pesquisa"
                    onClick={handleSubmit}
                    edge="start"
                    size="small"
                  >
                    <SearchIcon 
                      color="action" 
                      sx={{ 
                        opacity: 0.7,
                        color: theme.palette.text.secondary
                      }} 
                    />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {isLoading ? (
                    <CircularProgress size={20} />
                  ) : query ? (
                    <IconButton
                      aria-label="limpar pesquisa"
                      onClick={handleClearQuery}
                      edge="end"
                      size="small"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  ) : null}
                  {globalSearchToggle && (
                    <Chip
                      label={isGlobalSearch ? "Global" : "Categoria atual"}
                      color={isGlobalSearch ? "primary" : "default"}
                      size="small"
                      onClick={() => onGlobalSearchChange && onGlobalSearchChange(!isGlobalSearch)}
                      sx={{ ml: 1, cursor: 'pointer' }}
                    />
                  )}
                </InputAdornment>
              ),
              sx: {
                borderRadius: 2,
                bgcolor: theme.palette.mode === 'dark' 
                  ? alpha(theme.palette.common.white, 0.06) 
                  : alpha(theme.palette.common.black, 0.03),
                '&:hover': {
                  bgcolor: theme.palette.mode === 'dark' 
                    ? alpha(theme.palette.common.white, 0.08) 
                    : alpha(theme.palette.common.black, 0.05),
                },
                transition: 'all 0.2s',
                pr: 1,
              }
            }}
            sx={{
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'transparent',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'transparent',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main',
                borderWidth: '1px',
              }
            }}
          />
        </form>
        
        <Popper
          open={showSuggestions}
          anchorEl={anchorRef.current}
          transition
          placement="bottom-start"
          style={{ 
            width: anchorRef.current?.clientWidth,
            zIndex: 1300
          }}
        >
          {({ TransitionProps }) => (
            <Grow {...TransitionProps} timeout={200}>
              <Paper
                elevation={6}
                sx={{
                  mt: 0.5,
                  borderRadius: 2,
                  overflow: 'hidden',
                  maxHeight: 400,
                  overflowY: 'auto'
                }}
              >
                {query.length > 0 && suggestions.length > 0 && (
                  <>
                    <Box sx={{ px: 2, py: 1.5 }}>
                      <Typography variant="subtitle2" color="textSecondary">
                        Sugestões
                      </Typography>
                    </Box>
                    <List dense>
                      {suggestions.map((suggestion, index) => (
                        <ListItem 
                          key={`suggestion-${index}`}
                          onClick={() => handleSelectSuggestion(suggestion)}
                          sx={{
                            px: 2,
                            py: 0.75,
                            '&:hover': {
                              bgcolor: alpha(theme.palette.primary.main, 0.08)
                            },
                            cursor: 'pointer'
                          }}
                        >
                          <ListItemText 
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <SearchIcon 
                                  fontSize="small" 
                                  sx={{ mr: 1, opacity: 0.5, fontSize: '0.9rem' }} 
                                />
                                {suggestion}
                              </Box>
                            }
                          />
                          <ArrowForwardIcon fontSize="small" sx={{ opacity: 0.3 }} />
                        </ListItem>
                      ))}
                    </List>
                    <Divider />
                  </>
                )}
                
                {searchHistory.length > 0 && query.length === 0 && (
                  <>
                    <Box 
                      sx={{ 
                        px: 2, 
                        py: 1.5, 
                        display: 'flex', 
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <Typography variant="subtitle2" color="textSecondary">
                        Pesquisas recentes
                      </Typography>
                      <IconButton 
                        size="small" 
                        onClick={handleClearHistory}
                        title="Limpar histórico"
                        sx={{ opacity: 0.6 }}
                      >
                        <DeleteOutlineIcon fontSize="small" />
                      </IconButton>
                    </Box>
                    <List dense>
                      {searchHistory.slice(0, 5).map((historyItem, index) => (
                        <ListItem 
                          key={`history-${index}`}
                          onClick={() => handleSelectSuggestion(historyItem)}
                          sx={{
                            px: 2,
                            py: 0.75,
                            '&:hover': {
                              bgcolor: alpha(theme.palette.primary.main, 0.08)
                            },
                            cursor: 'pointer'
                          }}
                        >
                          <ListItemText 
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <HistoryIcon 
                                  fontSize="small" 
                                  sx={{ mr: 1, opacity: 0.5, fontSize: '0.9rem' }} 
                                />
                                {historyItem}
                              </Box>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )}
                
                {query.length > 0 && suggestions.length === 0 && (
                  <Box 
                    sx={{ 
                      p: 2, 
                      textAlign: 'center',
                      color: 'text.secondary'
                    }}
                  >
                    <Typography variant="body2">
                      Nenhuma sugestão encontrada para "{query}"
                    </Typography>
                    <Typography variant="caption">
                      Pressione Enter para buscar
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Grow>
          )}
        </Popper>
      </Box>
    </ClickAwayListener>
  );
};

export default SearchBar; 