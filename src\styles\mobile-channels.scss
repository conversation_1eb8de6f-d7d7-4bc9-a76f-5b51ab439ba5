/* Estilos específicos para melhorar a experiência mobile na página de canais */

/* Otimizações para telas pequenas */
@media (max-width: 768px) {
  /* Player de vídeo mobile */
  .mobile-video-player {
    position: relative;
    width: 100%;
    height: 35vh;
    min-height: 200px;
    max-height: 280px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    
    video {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain;
    }
  }

  /* Categorias mobile - layout otimizado */
  .mobile-categories {
    .category-item {
      padding: 12px 16px;
      min-height: 48px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      .category-name {
        font-size: 0.85rem;
        line-height: 1.3;
        word-break: break-word;
        white-space: normal;
        overflow: visible;
      }
      
      &.selected {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        color: white;
        font-weight: bold;
      }
      
      &:hover {
        background: rgba(14, 165, 233, 0.1);
      }
    }
  }

  /* Canais mobile - layout otimizado */
  .mobile-channels {
    .channel-item {
      padding: 12px 16px;
      min-height: 56px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      position: relative;
      flex-shrink: 0;
      
      .channel-avatar {
        width: 36px;
        height: 36px;
        border-radius: 6px;
        margin-right: 12px;
      }
      
      .channel-info {
        flex: 1;
        padding-right: 40px;
        
        .channel-name {
          font-size: 0.85rem;
          line-height: 1.3;
          font-weight: 500;
          word-break: break-word;
          white-space: normal;
          overflow: visible;
          margin-bottom: 4px;
        }
        
        .channel-program {
          font-size: 0.7rem;
          color: rgba(255, 255, 255, 0.7);
          line-height: 1.2;
          
          .program-title {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
      
      .favorite-button {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 32px;
        height: 32px;
        
        .MuiSvgIcon-root {
          font-size: 1.1rem;
        }
      }
      
      &.selected {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        color: white;
        
        .channel-name {
          font-weight: bold;
        }
        
        .channel-program {
          color: rgba(255, 255, 255, 0.8);
        }
      }
      
      &:hover {
        background: rgba(14, 165, 233, 0.1);
      }
    }
  }

  /* Abas mobile */
  .mobile-tabs {
    .MuiTab-root {
      min-width: unset;
      padding: 12px 16px;
      font-size: 0.9rem;
      font-weight: 600;
      
      .MuiSvgIcon-root {
        font-size: 1.1rem;
        margin-right: 6px;
      }
    }
    
    .MuiTabs-indicator {
      height: 3px;
      border-radius: 2px;
    }
  }

  /* EPG Guide mobile */
  .mobile-epg {
    max-height: 30vh;
    overflow-y: auto;
    
    .epg-item {
      padding: 8px 12px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      
      .epg-time {
        font-size: 0.7rem;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 4px;
      }
      
      .epg-title {
        font-size: 0.8rem;
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: 2px;
      }
      
      .epg-description {
        font-size: 0.7rem;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      &.current {
        background: rgba(14, 165, 233, 0.1);
        border-left: 3px solid #0ea5e9;
        
        .epg-title {
          color: #0ea5e9;
          font-weight: bold;
        }
      }
    }
  }

  /* Barra de progresso mobile */
  .mobile-progress {
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1px;
    overflow: hidden;
    margin-top: 4px;
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 100%);
      border-radius: 1px;
      transition: width 0.3s ease;
    }
  }

  /* Scrollbar personalizada para mobile */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    height: 100%;
    max-height: calc(100vh - 56px - 180px);
    min-height: 200px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(14, 165, 233, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(14, 165, 233, 0.5);
      }
    }
  }

  /* Animações suaves */
  .mobile-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Estados de loading mobile */
  .mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    
    .loading-spinner {
      margin-bottom: 12px;
    }
    
    .loading-text {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;
    }
  }

  /* Estados vazios mobile */
  .mobile-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    text-align: center;
    
    .empty-icon {
      font-size: 2rem;
      color: rgba(255, 255, 255, 0.3);
      margin-bottom: 12px;
    }
    
    .empty-title {
      font-size: 0.9rem;
      font-weight: 500;
      margin-bottom: 6px;
      color: rgba(255, 255, 255, 0.8);
    }
    
    .empty-subtitle {
      font-size: 0.75rem;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.4;
    }
  }
}

/* Melhorias globais de scroll para mobile */
@media (max-width: 768px) {
  * {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Garantir que containers com overflow:auto funcionem bem no touch */
  [style*="overflow: auto"],
  [style*="overflow:auto"],
  .MuiBox-root[style*="overflow"] {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
  }
}

/* Otimizações para telas muito pequenas (< 480px) */
@media (max-width: 480px) {
  .mobile-video-player {
    height: 30vh;
    min-height: 180px;
    max-height: 240px;
    flex-shrink: 0;
  }

  .mobile-categories .category-item,
  .mobile-channels .channel-item {
    padding: 10px 12px;
  }

  .mobile-categories .category-item .category-name,
  .mobile-channels .channel-item .channel-info .channel-name {
    font-size: 0.8rem;
  }

  .mobile-channels .channel-item .channel-info .channel-program {
    font-size: 0.65rem;
  }

  .mobile-epg {
    max-height: 25vh;
    
    .epg-item {
      padding: 6px 10px;
      
      .epg-time {
        font-size: 0.65rem;
      }
      
      .epg-title {
        font-size: 0.75rem;
      }
      
      .epg-description {
        font-size: 0.65rem;
      }
    }
  }

  .mobile-channels {
    height: calc(100vh - 56px - 180px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
