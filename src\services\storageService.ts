/**
 * Serviço de armazenamento persistente
 * Usa localStorage no navegador e electron-store no Electron
 */

// Verificar se estamos no Electron
const isElectronApp = (): boolean => {
  return (
    typeof window !== 'undefined' &&
    (
      window.isElectronApp === true ||
      window.location.protocol === 'file:' ||
      (window as any).process?.versions?.electron
    )
  );
};

/**
 * Classe para gerenciar armazenamento persistente
 */
class StorageService {
  /**
   * Obtém um valor do armazenamento
   * @param key Chave do valor
   * @returns Valor armazenado ou null se não existir
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      if (isElectronApp() && window.electronAPI?.store) {
        // Usar electron-store no Electron
        const value = await window.electronAPI.store.get(key);
        return value || null;
      } else {
        // Usar localStorage no navegador
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
      }
    } catch (error) {
      console.error(`Erro ao obter valor para chave ${key}:`, error);
      return null;
    }
  }

  /**
   * Armazena um valor
   * @param key Chave do valor
   * @param value Valor a ser armazenado
   */
  async set<T>(key: string, value: T): Promise<void> {
    try {
      if (isElectronApp() && window.electronAPI?.store) {
        // Usar electron-store no Electron
        await window.electronAPI.store.set(key, value);
      } else {
        // Usar localStorage no navegador
        localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error(`Erro ao armazenar valor para chave ${key}:`, error);
    }
  }

  /**
   * Remove um valor do armazenamento
   * @param key Chave do valor a ser removido
   */
  async remove(key: string): Promise<void> {
    try {
      if (isElectronApp() && window.electronAPI?.store) {
        // Usar electron-store no Electron
        await window.electronAPI.store.delete(key);
      } else {
        // Usar localStorage no navegador
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Erro ao remover valor para chave ${key}:`, error);
    }
  }

  /**
   * Limpa todo o armazenamento
   */
  async clear(): Promise<void> {
    try {
      if (isElectronApp() && window.electronAPI?.store) {
        // Usar electron-store no Electron
        await window.electronAPI.store.clear();
      } else {
        // Usar localStorage no navegador
        localStorage.clear();
      }
    } catch (error) {
      console.error('Erro ao limpar armazenamento:', error);
    }
  }
}

// Exportar instância única
const storageService = new StorageService();
export default storageService;