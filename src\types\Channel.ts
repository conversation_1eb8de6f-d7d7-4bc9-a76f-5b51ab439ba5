export interface Channel {
  id: string;
  name: string;
  url: string;
  group?: string;
  category?: string;
  logo?: string;
  playlistId: string;
  language?: string;
  country?: string;
  description?: string;
  lastWatched?: string;
  watchProgress?: number;
  isFavorite?: boolean;
  thumbnail?: string; // Added for UI consistency
}

export interface Playlist {
  id: string;
  name: string;
  url: string;
  description?: string;
  channelCount: number;
  lastUpdated: string;
  type: 'm3u' | 'm3u8' | 'txt';
  categories?: string[];
  groups?: string[];
  thumbnail?: string;
}

export interface WatchHistoryItem {
  id: string;
  channelId: string;
  playlistId: string;
  channelName: string;
  url: string;
  timestamp: string;
  position: number;
  duration: number;
  logo?: string;
  group?: string;
  category?: string;
  thumbnail?: string; // Added for UI display
}

export interface FavoriteChannel extends Channel {
  addedAt: string;
  notes: string;
}

export interface UserPreferences {
  id: 'user';
  theme: 'light' | 'dark' | 'system';
  defaultPlayer: string;
  autoplay: boolean;
  quality: 'auto' | 'high' | 'medium' | 'low';
  volume: number;
  muted: boolean;
  showCaptions: boolean;
  language: string;
  notifications: boolean;
}

export const DEFAULT_PREFERENCES: Omit<UserPreferences, 'id'> = {
  theme: 'system',
  defaultPlayer: 'html5',
  autoplay: false,
  quality: 'auto',
  volume: 1,
  muted: false,
  showCaptions: false,
  language: 'en',
  notifications: true
};

export interface StorageSchema {
  channels: {
    key: string;
    value: Channel;
    indexes: {
      'by-playlist': string;
      'by-group': string;
      'by-category': string;
    };
  };
  playlists: {
    key: string;
    value: Playlist;
  };
  watchHistory: {
    key: string;
    value: WatchHistoryItem;
    indexes: {
      'by-channel': string;
      'by-playlist': string;
      'by-timestamp': string;
    };
  };
  favorites: {
    key: string;
    value: FavoriteChannel;
    indexes: {
      'by-playlist': string;
    };
  };
  preferences: {
    key: string;
    value: UserPreferences;
  };
}

export type DBStoreName = keyof StorageSchema;

// Helper types for consistent data handling
export type ChannelInput = Omit<Channel, 'id' | 'isFavorite' | 'lastWatched' | 'watchProgress'>;
export type PlaylistInput = Omit<Playlist, 'id' | 'lastUpdated' | 'channelCount'>;
export type WatchHistoryInput = Omit<WatchHistoryItem, 'id'>;
export type FavoriteInput = Omit<FavoriteChannel, 'id' | 'addedAt'>;
export type PreferencesInput = Omit<UserPreferences, 'id'>;
