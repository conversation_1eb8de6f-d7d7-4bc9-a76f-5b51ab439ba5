import React, { useCallback } from 'react';
import { loadSlim } from 'tsparticles-slim';
import Particles from '@tsparticles/react';
import type { Container, Engine } from 'tsparticles-engine';
import { useTheme } from '@mui/material';

interface ParticlesBackgroundProps {
    type?: 'auth' | 'default' | 'movies' | 'series';
    intensity?: 'low' | 'medium' | 'high';
}

const ParticlesBackground: React.FC<ParticlesBackgroundProps> = ({
    type = 'auth',
    intensity = 'medium'
}) => {
    const theme = useTheme();
    const isDarkMode = theme.palette.mode === 'dark';

    const particlesInit = useCallback(async (engine: Engine) => {
        await loadSlim(engine);
    }, []);

    const particlesLoaded = useCallback(async (_container: Container | undefined) => {
        // Callback opcional quando as partículas são carregadas
    }, []);

    // Configurações baseadas no tipo e intensidade - reduzido para melhor performance
    const getParticleCount = () => {
        const baseCount = {
            low: 15,
            medium: 25,
            high: 40
        }[intensity];

        return type === 'auth' ? Math.floor(baseCount * 1.1) : baseCount;
    };

    const getColors = () => {
        switch (type) {
            case 'auth':
                return isDarkMode
                    ? ['#0ea5e9', '#38bdf8', '#06b6d4', '#0891b2']
                    : ['#0284c7', '#0369a1', '#075985', '#0c4a6e'];
            case 'movies':
                return isDarkMode
                    ? ['#e11d48', '#f43f5e', '#ec4899', '#be185d']
                    : ['#be123c', '#9f1239', '#881337', '#7f1d1d'];
            case 'series':
                return isDarkMode
                    ? ['#7c3aed', '#8b5cf6', '#a855f7', '#9333ea']
                    : ['#6d28d9', '#5b21b6', '#4c1d95', '#3730a3'];
            default:
                return isDarkMode
                    ? ['#0ea5e9', '#38bdf8', '#06b6d4']
                    : ['#0284c7', '#0369a1', '#075985'];
        }
    };

    const particlesConfig = {
        background: {
            color: {
                value: 'transparent',
            },
        },
        fpsLimit: 120,
        interactivity: {
            events: {
                onClick: {
                    enable: true,
                    mode: 'push',
                },
                onHover: {
                    enable: true,
                    mode: 'repulse',
                },
                resize: {
                    enable: true,
                },
            },
            modes: {
                push: {
                    quantity: 4,
                },
                repulse: {
                    distance: 100,
                    duration: 0.4,
                },
            },
        },
        particles: {
            color: {
                value: getColors(),
            },
            links: {
                color: getColors()[0],
                distance: 150,
                enable: type === 'auth',
                opacity: isDarkMode ? 0.3 : 0.2,
                width: 1,
            },
            move: {
                direction: 'none' as const,
                enable: true,
                outModes: {
                    default: 'bounce' as const,
                },
                random: false,
                speed: intensity === 'low' ? 1 : intensity === 'high' ? 3 : 2,
                straight: false,
            },
            number: {
                density: {
                    enable: true,
                    area: 800,
                },
                value: getParticleCount(),
            },
            opacity: {
                value: isDarkMode ? 0.3 : 0.2,
                animation: {
                    enable: true,
                    speed: 0.5,
                    minimumValue: 0.05,
                    sync: false,
                },
            },
            shape: {
                type: type === 'auth' ? 'circle' : ['circle', 'triangle', 'polygon'],
                options: {
                    polygon: {
                        sides: 6,
                    },
                },
            },
            size: {
                value: { min: 1, max: intensity === 'low' ? 3 : intensity === 'high' ? 6 : 4 },
                animation: {
                    enable: true,
                    speed: 2,
                    minimumValue: 0.5,
                    sync: false,
                },
            },
        },
        detectRetina: true,
    };

    // Configurações especiais para tela de autenticação
    if (type === 'auth') {
        // Adiciona configurações específicas para auth se necessário
        particlesConfig.particles.move.speed = 1.5;
    }

    return (
        <Particles
            id={`particles-${type}`}
            init={particlesInit}
            loaded={particlesLoaded}
            options={particlesConfig}
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                zIndex: -1,
                pointerEvents: 'none',
            }}
        />
    );
};

export default ParticlesBackground;