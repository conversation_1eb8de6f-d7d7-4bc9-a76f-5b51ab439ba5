const fs = require('fs-extra');
const path = require('path');

console.log('📦 Criando "app pronto" com todos os arquivos necessários...');

const sourceDir = path.join(__dirname, '..');
const targetDir = path.join(sourceDir, 'app pronto');

// Arquivos essenciais na raiz
const rootFiles = [
  'package.json',
  'package-lock.json',
  'vercel.json',
  'vite.config.ts',
  'tsconfig.json',
  'tsconfig.node.json',
  '.vercelignore',
  'index.html',
  '.eslintrc.cjs'
];

// Pastas essenciais
const directories = [
  'src',
  'public', 
  'scripts'
];

async function copyProductionFiles() {
  try {
    // Remover pasta de destino se existir
    if (fs.existsSync(targetDir)) {
      console.log('🗑️ Removendo pasta "app pronto" existente...');
      await fs.remove(targetDir);
    }

    // Criar pasta de destino
    await fs.ensureDir(targetDir);
    console.log('📁 Pasta "app pronto" criada');

    // Copiar arquivos da raiz
    console.log('📄 Copiando arquivos da raiz...');
    for (const file of rootFiles) {
      const sourcePath = path.join(sourceDir, file);
      const targetPath = path.join(targetDir, file);
      
      if (fs.existsSync(sourcePath)) {
        await fs.copy(sourcePath, targetPath);
        console.log(`✅ ${file}`);
      } else {
        console.log(`⚠️ ${file} não encontrado`);
      }
    }

    // Copiar diretórios
    console.log('📂 Copiando diretórios...');
    for (const dir of directories) {
      const sourcePath = path.join(sourceDir, dir);
      const targetPath = path.join(targetDir, dir);
      
      if (fs.existsSync(sourcePath)) {
        await fs.copy(sourcePath, targetPath);
        console.log(`✅ ${dir}/`);
      } else {
        console.log(`⚠️ ${dir}/ não encontrado`);
      }
    }

    // Criar .gitignore para a pasta app pronto
    const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
`;

    await fs.writeFile(path.join(targetDir, '.gitignore'), gitignoreContent);
    console.log('✅ .gitignore criado');

    // Criar README específico
    const readmeContent = `# Neko TV - App Pronto para Deploy

Esta pasta contém todos os arquivos necessários para fazer o build e deploy do Neko TV no Vercel.

## Como usar:

1. Instalar dependências:
\`\`\`bash
npm install
\`\`\`

2. Fazer build local (opcional):
\`\`\`bash
npm run build
\`\`\`

3. Deploy no Vercel:
\`\`\`bash
npx vercel --prod
\`\`\`

## Estrutura:
- \`src/\` - Código fonte da aplicação
- \`public/\` - Assets públicos
- \`scripts/\` - Scripts de build
- \`package.json\` - Dependências e scripts
- \`vercel.json\` - Configuração do Vercel
- \`vite.config.ts\` - Configuração do Vite

## Build final esperado:
- Tamanho total: ~3-4MB
- Assets principais:
  - index.html: ~3.19 kB
  - CSS: ~84 kB (4.81 kB + 79.27 kB)
  - JS: ~3MB (577B + 516KB + 1.13MB + 1.31MB)

Criado automaticamente em ${new Date().toLocaleString('pt-BR')}
`;

    await fs.writeFile(path.join(targetDir, 'README.md'), readmeContent);
    console.log('✅ README.md criado');

    // Verificar tamanho da pasta criada
    const stats = await fs.stat(targetDir);
    console.log('\n🎉 "app pronto" criado com sucesso!');
    console.log(`📍 Localização: ${targetDir}`);
    
    // Contar arquivos
    const countFiles = async (dir) => {
      let count = 0;
      const items = await fs.readdir(dir);
      
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stat = await fs.stat(itemPath);
        
        if (stat.isDirectory()) {
          count += await countFiles(itemPath);
        } else {
          count++;
        }
      }
      
      return count;
    };

    const fileCount = await countFiles(targetDir);
    console.log(`📊 Total de arquivos: ${fileCount}`);
    
    console.log('\n📋 Para usar:');
    console.log('1. cd "app pronto"');
    console.log('2. npm install');
    console.log('3. npm run build (opcional)');
    console.log('4. npx vercel --prod');

  } catch (error) {
    console.error('❌ Erro ao copiar arquivos:', error);
    process.exit(1);
  }
}

copyProductionFiles(); 