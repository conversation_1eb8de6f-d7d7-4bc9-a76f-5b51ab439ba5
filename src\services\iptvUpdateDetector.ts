import { createIPTVService, IPTVService } from './iptvService';

interface UpdateInfo {
  type: 'channels' | 'movies' | 'series' | 'categories';
  action: 'added' | 'removed' | 'updated';
  count: number;
  details?: string[];
}

interface ContentSnapshot {
  channels: number;
  movies: number;
  series: number;
  categories: number;
  channelCategories: number;
  movieCategories: number;
  seriesCategories: number;
  timestamp: number;
  hash: string;
}

class IptvUpdateDetector {
  private static instance: IptvUpdateDetector;
  private readonly STORAGE_KEY = 'neko_iptv_snapshot';
  private lastSnapshot: ContentSnapshot | null = null;
  private iptvService: IPTVService | null = null;

  private constructor() {
    this.loadLastSnapshot();
  }

  public static getInstance(): IptvUpdateDetector {
    if (!IptvUpdateDetector.instance) {
      IptvUpdateDetector.instance = new IptvUpdateDetector();
    }
    return IptvUpdateDetector.instance;
  }

  /**
   * Carrega o último snapshot salvo
   */
  private loadLastSnapshot(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.lastSnapshot = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Erro ao carregar snapshot IPTV:', error);
      this.lastSnapshot = null;
    }
  }

  /**
   * Salva snapshot atual
   */
  private saveSnapshot(snapshot: ContentSnapshot): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(snapshot));
      this.lastSnapshot = snapshot;
    } catch (error) {
      console.warn('Erro ao salvar snapshot IPTV:', error);
    }
  }

  /**
   * Obtém instância do IPTV Service
   */
  private async getIptvService(): Promise<IPTVService> {
    if (!this.iptvService) {
      this.iptvService = await createIPTVService();
    }
    return this.iptvService;
  }

  /**
   * Cria snapshot do conteúdo atual
   */
  private async createCurrentSnapshot(): Promise<ContentSnapshot> {
    try {
      const service = await this.getIptvService();

      // Busca informações atuais do servidor
      const [
        liveCategories,
        movieCategories,
        seriesCategories
      ] = await Promise.all([
        service.getLiveCategories(),
        service.getMovieCategories(),
        service.getSeriesCategories()
      ]);

      // Conta total de streams por categoria (amostragem)
      let totalChannels = 0;
      let totalMovies = 0;
      let totalSeries = 0;

      // Conta canais (primeiras 3 categorias para performance)
      const sampleChannelCategories = liveCategories.slice(0, 3);
      for (const category of sampleChannelCategories) {
        try {
          const streams = await service.getLiveStreams(category.id);
          totalChannels += streams.length;
        } catch (error) {
          // Ignora erros de categorias específicas
        }
      }

      // Conta filmes (primeiras 3 categorias para performance)
      const sampleMovieCategories = movieCategories.slice(0, 3);
      for (const category of sampleMovieCategories) {
        try {
          const streams = await service.getMovieStreams(category.id);
          totalMovies += streams.length;
        } catch (error) {
          // Ignora erros de categorias específicas
        }
      }

      // Conta séries (primeiras 3 categorias para performance)
      const sampleSeriesCategories = seriesCategories.slice(0, 3);
      for (const category of sampleSeriesCategories) {
        try {
          const streams = await service.getSeriesStreams(category.id);
          totalSeries += streams.length;
        } catch (error) {
          // Ignora erros de categorias específicas
        }
      }

      // Cria hash simples baseado nas contagens
      const hashData = `${liveCategories.length}-${movieCategories.length}-${seriesCategories.length}-${totalChannels}-${totalMovies}-${totalSeries}`;
      const hash = btoa(hashData).slice(0, 16);

      return {
        channels: totalChannels,
        movies: totalMovies,
        series: totalSeries,
        categories: liveCategories.length + movieCategories.length + seriesCategories.length,
        channelCategories: liveCategories.length,
        movieCategories: movieCategories.length,
        seriesCategories: seriesCategories.length,
        timestamp: Date.now(),
        hash
      };
    } catch (error) {
      throw new Error(`Erro ao criar snapshot: ${error}`);
    }
  }

  /**
   * Detecta mudanças comparando snapshots
   */
  private detectChanges(oldSnapshot: ContentSnapshot, newSnapshot: ContentSnapshot): UpdateInfo[] {
    const updates: UpdateInfo[] = [];

    // Detecta mudanças em categorias de canais
    if (newSnapshot.channelCategories !== oldSnapshot.channelCategories) {
      const diff = newSnapshot.channelCategories - oldSnapshot.channelCategories;
      updates.push({
        type: 'categories',
        action: diff > 0 ? 'added' : 'removed',
        count: Math.abs(diff),
        details: ['Categorias de canais']
      });
    }

    // Detecta mudanças em categorias de filmes
    if (newSnapshot.movieCategories !== oldSnapshot.movieCategories) {
      const diff = newSnapshot.movieCategories - oldSnapshot.movieCategories;
      updates.push({
        type: 'categories',
        action: diff > 0 ? 'added' : 'removed',
        count: Math.abs(diff),
        details: ['Categorias de filmes']
      });
    }

    // Detecta mudanças em categorias de séries
    if (newSnapshot.seriesCategories !== oldSnapshot.seriesCategories) {
      const diff = newSnapshot.seriesCategories - oldSnapshot.seriesCategories;
      updates.push({
        type: 'categories',
        action: diff > 0 ? 'added' : 'removed',
        count: Math.abs(diff),
        details: ['Categorias de séries']
      });
    }

    // Detecta mudanças em canais
    if (newSnapshot.channels !== oldSnapshot.channels) {
      const diff = newSnapshot.channels - oldSnapshot.channels;
      updates.push({
        type: 'channels',
        action: diff > 0 ? 'added' : 'removed',
        count: Math.abs(diff),
        details: ['Canais de TV']
      });
    }

    // Detecta mudanças em filmes
    if (newSnapshot.movies !== oldSnapshot.movies) {
      const diff = newSnapshot.movies - oldSnapshot.movies;
      updates.push({
        type: 'movies',
        action: diff > 0 ? 'added' : 'removed',
        count: Math.abs(diff),
        details: ['Filmes']
      });
    }

    // Detecta mudanças em séries
    if (newSnapshot.series !== oldSnapshot.series) {
      const diff = newSnapshot.series - oldSnapshot.series;
      updates.push({
        type: 'series',
        action: diff > 0 ? 'added' : 'removed',
        count: Math.abs(diff),
        details: ['Séries']
      });
    }

    return updates;
  }

  /**
   * Verifica se há atualizações disponíveis
   */
  public async checkForUpdates(): Promise<{ hasUpdates: boolean; updates: UpdateInfo[] }> {
    try {
      const currentSnapshot = await this.createCurrentSnapshot();

      // Se não há snapshot anterior, salva o atual e retorna sem atualizações
      if (!this.lastSnapshot) {
        this.saveSnapshot(currentSnapshot);
        return { hasUpdates: false, updates: [] };
      }

      // Compara hashes primeiro (mais rápido)
      if (currentSnapshot.hash === this.lastSnapshot.hash) {
        return { hasUpdates: false, updates: [] };
      }

      // Se hashes diferentes, detecta mudanças específicas
      const updates = this.detectChanges(this.lastSnapshot, currentSnapshot);

      // Salva novo snapshot
      this.saveSnapshot(currentSnapshot);

      return {
        hasUpdates: updates.length > 0,
        updates
      };
    } catch (error) {
      console.error('Erro ao verificar atualizações IPTV:', error);
      return { hasUpdates: false, updates: [] };
    }
  }

  /**
   * Força uma nova verificação (limpa snapshot)
   */
  public forceCheck(): void {
    this.lastSnapshot = null;
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Obtém informações do último snapshot
   */
  public getLastSnapshot(): ContentSnapshot | null {
    return this.lastSnapshot;
  }

  /**
   * Limpa cache do IPTV service para forçar reload
   */
  public async clearIptvCache(): Promise<void> {
    try {
      // Limpa cache do iptvService
      if (this.iptvService && typeof this.iptvService.clearCache === 'function') {
        await this.iptvService.clearCache();
      }

      // Limpa localStorage relacionado ao IPTV
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('iptv') || key.includes('cache') || key.includes('streams')) {
          localStorage.removeItem(key);
        }
      });

      console.log('Cache IPTV limpo com sucesso');
    } catch (error) {
      console.warn('Erro ao limpar cache IPTV:', error);
    }
  }
}

// Exporta instância singleton
export const iptvUpdateDetector = IptvUpdateDetector.getInstance();
export type { UpdateInfo, ContentSnapshot };

// Funções globais para debug (disponível no console)
if (typeof window !== 'undefined') {
  (window as any).debugIptvUpdates = {
    checkUpdates: async () => {
      try {
        console.log('🔄 Verificando atualizações IPTV...');
        const result = await iptvUpdateDetector.checkForUpdates();
        console.log('📊 Resultado:', result);
        return result;
      } catch (error) {
        console.error('❌ Erro ao verificar atualizações:', error);
        throw error;
      }
    },
    forceCheck: () => {
      iptvUpdateDetector.forceCheck();
      console.log('🔄 Snapshot limpo, próxima verificação será completa');
    },
    getSnapshot: () => {
      const snapshot = iptvUpdateDetector.getLastSnapshot();
      console.log('📸 Último snapshot:', snapshot);
      return snapshot;
    },
    clearCache: async () => {
      await iptvUpdateDetector.clearIptvCache();
      console.log('🗑️ Cache IPTV limpo');
    },
    simulateUpdate: () => {
      // Simula uma atualização modificando o snapshot
      const fakeSnapshot = {
        channels: 100,
        movies: 50,
        series: 30,
        categories: 20,
        channelCategories: 10,
        movieCategories: 5,
        seriesCategories: 5,
        timestamp: Date.now(),
        hash: 'fake-hash-' + Date.now()
      };
      localStorage.setItem('neko_iptv_snapshot', JSON.stringify(fakeSnapshot));
      console.log('🎭 Snapshot simulado criado para testar detecção');
    }
  };

  console.log('🛠️ Debug de atualizações IPTV disponível:');
  console.log('debugIptvUpdates.checkUpdates() - Verifica atualizações');
  console.log('debugIptvUpdates.forceCheck() - Força nova verificação');
  console.log('debugIptvUpdates.getSnapshot() - Mostra último snapshot');
  console.log('debugIptvUpdates.clearCache() - Limpa cache IPTV');
  console.log('debugIptvUpdates.simulateUpdate() - Simula atualização para teste');
}
