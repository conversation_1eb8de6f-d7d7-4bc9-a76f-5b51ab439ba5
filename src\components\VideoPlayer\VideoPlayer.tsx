import React, { useState, useEffect, useRef, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import Plyr from 'plyr';
import 'plyr/dist/plyr.css';
import Hls from 'hls.js';
import ReactPlayer from 'react-player';
import { Box, Typography, Select, MenuItem, FormControl, CircularProgress, Paper, Tooltip } from '@mui/material';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import VideoSettingsIcon from '@mui/icons-material/VideoSettings';
import OndemandVideoIcon from '@mui/icons-material/OndemandVideo';
import SlowMotionVideoIcon from '@mui/icons-material/SlowMotionVideo';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { getSafeUrl } from '../../utils/proxyUrl';

// Importamos o Hls.js para uso interno no componente
// Não precisamos mais expor globalmente

interface VideoPlayerProps {
  url: string;
  title?: string;
  style?: React.CSSProperties;
  initialTime?: number;
  autoPlay?: boolean;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onClose?: () => void;
  notification?: {
    show: boolean;
    message: string;
    subtitle: string;
  };
}

// Define what functions/properties we want to expose via the ref
export interface VideoPlayerRef {
  play: () => void;
  pause: () => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  seekTo: (time: number) => void;
}

const players = {
  VIDEOJS: 'videojs',
  PLYR: 'plyr',
  HLS: 'hls',
  REACT_PLAYER: 'react-player'
};

// Throttle function to limit the execution of an event handler
const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function(this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

const VideoPlayer = forwardRef<VideoPlayerRef, VideoPlayerProps>(({ 
  url, 
  title, 
  style, 
  initialTime = 0, 
  autoPlay = false,
  onTimeUpdate,
  onClose,
  notification
}, ref) => {
  // Usar URL direta sem proxy
  const safeUrl = useMemo(() => url, [url]);
  
  const [selectedPlayer, setSelectedPlayer] = useState(players.REACT_PLAYER);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showControls, setShowControls] = useState(false);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [currentTime, setCurrentTime] = useState(initialTime);
  const [duration, setDuration] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerInstanceRef = useRef<any>(null);
  const reactPlayerRef = useRef<any>(null);
  const hlsRef = useRef<Hls | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Timeout para esconder os controles
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Track progress update interval
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // Flag para rastrear se o seek inicial já foi aplicado
  const initialSeekAppliedRef = useRef<boolean>(false);

  // Adicionar um ref para controlar as tentativas de seek
  const seekAttemptsRef = useRef<number>(0);
  const MAX_SEEK_ATTEMPTS = 5;

  // Função para verificar se o seek foi aplicado corretamente
  const verifySeekPosition = useCallback(() => {
    if (!reactPlayerRef.current || initialSeekAppliedRef.current) return;
    
    const currentPosition = reactPlayerRef.current.getCurrentTime();
    console.log(`VideoPlayer verifySeek: Posição atual=${currentPosition}, Alvo=${initialTime}`);
    
    // Se a posição atual estiver muito distante do tempo inicial desejado
    if (Math.abs(currentPosition - initialTime) > 2 && seekAttemptsRef.current < MAX_SEEK_ATTEMPTS) {
      seekAttemptsRef.current++;
      console.log(`VideoPlayer: Retry seek attempt ${seekAttemptsRef.current}/${MAX_SEEK_ATTEMPTS}`);
      
      // Tentar aplicar o seek novamente
      reactPlayerRef.current.seekTo(initialTime);
      
      // Verificar novamente após um curto período
      setTimeout(verifySeekPosition, 500);
    } else if (seekAttemptsRef.current >= MAX_SEEK_ATTEMPTS) {
      console.warn(`VideoPlayer: Desistindo após ${MAX_SEEK_ATTEMPTS} tentativas de seek`);
      initialSeekAppliedRef.current = true; // Marca como aplicado mesmo com falha
    } else {
      console.log('VideoPlayer: Seek aplicado com sucesso!');
      initialSeekAppliedRef.current = true; // Marca como aplicado com sucesso
    }
  }, [initialTime]);

  // Adicionar a função de reset do seekAttemptsRef
  useEffect(() => {
    // Reset do contador de tentativas quando a URL ou initialTime mudarem
    seekAttemptsRef.current = 0;
    // Reset da flag de seek inicial aplicado
    initialSeekAppliedRef.current = false;
  }, [url, initialTime]);
  
  // Expose methods to parent components via ref
  useImperativeHandle(ref, () => ({
    play: () => {
      if (selectedPlayer === players.REACT_PLAYER && reactPlayerRef.current) {
        reactPlayerRef.current.getInternalPlayer().play();
      } else if (videoRef.current) {
        videoRef.current.play();
      }
      setIsPlaying(true);
    },
    pause: () => {
      if (selectedPlayer === players.REACT_PLAYER && reactPlayerRef.current) {
        reactPlayerRef.current.getInternalPlayer().pause();
      } else if (videoRef.current) {
        videoRef.current.pause();
      }
      setIsPlaying(false);
    },
    getCurrentTime: () => {
      if (selectedPlayer === players.REACT_PLAYER && reactPlayerRef.current) {
        return reactPlayerRef.current.getCurrentTime();
      } else if (videoRef.current) {
        return videoRef.current.currentTime;
      }
      return 0;
    },
    getDuration: () => {
      if (selectedPlayer === players.REACT_PLAYER && reactPlayerRef.current) {
        return reactPlayerRef.current.getDuration();
      } else if (videoRef.current) {
        return videoRef.current.duration;
      }
      return 0;
    },
    seekTo: (time: number) => {
      if (selectedPlayer === players.REACT_PLAYER && reactPlayerRef.current) {
        reactPlayerRef.current.seekTo(time);
      } else if (videoRef.current) {
        videoRef.current.currentTime = time;
      }
      setCurrentTime(time);
    }
  }));

  // Reset error and loading state when URL changes
  useEffect(() => {
    setError(null);
    setLoading(true);
    setIsPlaying(autoPlay);

    // Reset das flags e contadores
    seekAttemptsRef.current = 0;
    initialSeekAppliedRef.current = false;
    
    return () => {
      // Limpar quaisquer timeouts pendentes
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [url, autoPlay, initialTime]);

  // Setup interval to track progress
  useEffect(() => {
    if (isPlaying && onTimeUpdate) {
      // Clear existing interval
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
      
      // Update progress every second
      progressIntervalRef.current = setInterval(() => {
        let currentTime = 0;
        let duration = 0;
        
        if (selectedPlayer === players.REACT_PLAYER && reactPlayerRef.current) {
          currentTime = reactPlayerRef.current.getCurrentTime() || 0;
          duration = reactPlayerRef.current.getDuration() || 0;
        } else if (videoRef.current) {
          currentTime = videoRef.current.currentTime;
          duration = videoRef.current.duration;
        }
        
        setCurrentTime(currentTime);
        setDuration(duration);
        
        if (currentTime > 0 && duration > 0) {
          onTimeUpdate(currentTime, duration);
        }
      }, 1000);
    }
    
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [isPlaying, selectedPlayer, onTimeUpdate]);

  const cleanupPlayer = useCallback(() => {
    if (playerInstanceRef.current) {
      if (selectedPlayer === players.VIDEOJS) {
        playerInstanceRef.current.dispose();
      } else if (selectedPlayer === players.PLYR) {
        playerInstanceRef.current.destroy();
      }
      playerInstanceRef.current = null;
    }

    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }
  }, [selectedPlayer]);

  // Helper function para configurar HLS (evita duplicação de código)
  const setupHlsForElement = useCallback((videoElement: HTMLVideoElement) => {
    if (Hls.isSupported() && url.includes('.m3u8')) {
      hlsRef.current = new Hls({
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        fragLoadingMaxRetry: 4
      });
      hlsRef.current.loadSource(url);
      hlsRef.current.attachMedia(videoElement);
      hlsRef.current.on(Hls.Events.MANIFEST_PARSED, () => {
        videoElement.play().then(() => {
          setError(null);
          setLoading(false);
          setIsPlaying(true);
        }).catch(e => {
          console.error('Error playing video:', e);
          setError('Failed to play video');
        });
      });
    } else {
      videoElement.src = url;
      videoElement.play().then(() => {
        setError(null);
        setLoading(false);
        setIsPlaying(true);
      }).catch(e => {
        console.error('Error playing video:', e);
        setError('Failed to play video');
      });
    }
  }, [url]);

  const initializePlayer = useCallback(() => {
    if (!videoRef.current || !url) return;

    // Cleanup previous player instance
    cleanupPlayer();

    try {
      if (selectedPlayer === players.VIDEOJS) {
        playerInstanceRef.current = videojs(videoRef.current, {
          controls: true,
          autoplay: true,
          fluid: true,
          sources: [{ src: url }]
        });

        // Clear error when video starts playing
        playerInstanceRef.current.on('playing', () => {
          setError(null);
          setLoading(false);
          setIsPlaying(true);
        });
      } else if (selectedPlayer === players.PLYR) {
        playerInstanceRef.current = new Plyr(videoRef.current, {
          controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'settings', 'fullscreen'],
          settings: ['quality', 'speed'],
          fullscreen: { 
          enabled: true, 
          fallback: true, 
          iosNative: true
        }
        });
        
        setupHlsForElement(videoRef.current);
      } else if (selectedPlayer === players.HLS) {
        setupHlsForElement(videoRef.current);
      }
    } catch (err) {
      console.error('Error initializing player:', err);
      setError('Failed to initialize player');
    }
  }, [url, selectedPlayer, cleanupPlayer, setupHlsForElement]);

  useEffect(() => {
    if (selectedPlayer !== players.REACT_PLAYER) {
      initializePlayer();
    }

    return () => {
      cleanupPlayer();
    };
  }, [selectedPlayer, url, initializePlayer, cleanupPlayer]);

  // Handle mouse movement to show/hide controls - throttled to improve performance
  const handleMouseMove = useCallback(throttle(() => {
    setShowControls(true);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  }, 150), []);

  const handleMouseLeave = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setShowControls(false);
  }, []);

  const toggleFullscreen = useCallback(async () => {
    if (!containerRef.current) return;
    
    console.log('Tentando alternar fullscreen...');
    const isMobile = window.innerWidth <= 768;
    
    try {
      if (document.fullscreenElement) {
        console.log('Saindo do fullscreen...');
        await document.exitFullscreen();
        
        // Se for mobile, desbloquear orientação quando sair do fullscreen
        if (isMobile && 'screen' in window && 'orientation' in window.screen) {
          try {
            await (window.screen.orientation as any).unlock();
            console.log('Orientação desbloqueada');
          } catch (e) {
            console.log('Não foi possível desbloquear orientação:', e);
          }
        }
      } else {
        console.log('Entrando em fullscreen...');
        const element = containerRef.current;
        
        // Para mobile, primeiro tentar forçar orientação landscape
        if (isMobile && 'screen' in window && 'orientation' in window.screen) {
          try {
            console.log('Tentando forçar orientação landscape...');
            await (window.screen.orientation as any).lock('landscape');
            console.log('Orientação landscape ativada');
          } catch (e) {
            console.log('Não foi possível forçar orientação landscape:', e);
          }
        }
        
        // Aguardar um pouco para a orientação se estabilizar
        if (isMobile) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        // Tentar diferentes métodos de fullscreen para compatibilidade máxima
        let success = false;
        
        // Método padrão (mais moderno)
        if (element.requestFullscreen) {
          try {
            await element.requestFullscreen({ navigationUI: 'hide' });
            success = true;
            console.log('Fullscreen ativado com requestFullscreen');
          } catch (e) {
            console.log('requestFullscreen falhou:', e);
          }
        }
        
        // Fallback para WebKit (Safari, Chrome mobile)
        if (!success && (element as any).webkitRequestFullscreen) {
          try {
            // Para iOS/Safari, usar diferentes parâmetros
            if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
              await (element as any).webkitRequestFullscreen();
            } else {
              await (element as any).webkitRequestFullscreen(1); // ALLOW_KEYBOARD_INPUT = 1
            }
            success = true;
            console.log('Fullscreen ativado com webkitRequestFullscreen');
          } catch (e) {
            console.log('webkitRequestFullscreen falhou:', e);
          }
        }
        
        // Fallback para Firefox
        if (!success && (element as any).mozRequestFullScreen) {
          try {
            await (element as any).mozRequestFullScreen();
            success = true;
            console.log('Fullscreen ativado com mozRequestFullScreen');
          } catch (e) {
            console.log('mozRequestFullScreen falhou:', e);
          }
        }
        
        // Fallback para IE/Edge
        if (!success && (element as any).msRequestFullscreen) {
          try {
            await (element as any).msRequestFullscreen();
            success = true;
            console.log('Fullscreen ativado com msRequestFullscreen');
          } catch (e) {
            console.log('msRequestFullscreen falhou:', e);
          }
        }
        
        // Último recurso: tentar fullscreen no elemento de vídeo diretamente (mobile)
        if (!success) {
          console.log('Tentando fullscreen no elemento de vídeo...');
          const videoElement = element.querySelector('video');
          if (videoElement) {
            try {
              if ((videoElement as any).webkitEnterFullscreen) {
                (videoElement as any).webkitEnterFullscreen();
                success = true;
                console.log('Fullscreen ativado com webkitEnterFullscreen no vídeo');
              } else if ((videoElement as any).requestFullscreen) {
                await (videoElement as any).requestFullscreen();
                success = true;
                console.log('Fullscreen ativado com requestFullscreen no vídeo');
              }
            } catch (e) {
              console.log('Fullscreen no vídeo falhou:', e);
            }
          }
        }
        
        if (!success) {
          console.warn('Nenhum método de fullscreen funcionou');
        }
      }
    } catch (error) {
      console.error('Erro ao alternar fullscreen:', error);
    }
  }, []);

  // Handles for ReactPlayer
  const handleReactPlayerPlay = useCallback(() => {
    setIsPlaying(true);
    setError(null);
    setLoading(false);
  }, []);

  const handleReactPlayerError = useCallback((e: Error) => {
    console.error('ReactPlayer error:', e);
    setError('Erro ao reproduzir vídeo. Tente outro player.');
    setLoading(false);
  }, []);

  // Handler para quando o seeking é realizado pelo usuário
  const handleReactPlayerSeek = useCallback((seconds: number) => {
    console.log(`VideoPlayer: Usuário fez seek para ${seconds} segundos`);
    setCurrentTime(seconds);
  }, []);

  // Handler para mudanças na duração
  const handleReactPlayerDuration = useCallback((duration: number) => {
    console.log(`VideoPlayer: Duração detectada: ${duration} segundos (${Math.floor(duration / 60)}:${Math.floor(duration % 60)}min)`);
    setDuration(duration);
    
    // Se a duração for detectada e temos um tempo inicial para aplicar
    if (duration > 0 && initialTime > 0 && !initialSeekAppliedRef.current && reactPlayerRef.current) {
      console.log(`VideoPlayer: Duração disponível, aplicando seek inicial para ${initialTime}s`);
      setTimeout(() => {
        if (reactPlayerRef.current && !initialSeekAppliedRef.current) {
          reactPlayerRef.current.seekTo(initialTime);
          setTimeout(verifySeekPosition, 1000);
        }
      }, 500);
    }
  }, [initialTime, verifySeekPosition]);

  // Handler para buffer
  const handleReactPlayerBuffer = useCallback(() => {
    console.log('VideoPlayer: Buffering...');
    setLoading(true);
  }, []);

  const handleReactPlayerBufferEnd = useCallback(() => {
    console.log('VideoPlayer: Buffer ended');
    setLoading(false);
  }, []);

  // Handler para progress
  const handleReactPlayerProgress = useCallback(({ playedSeconds, loadedSeconds }: { playedSeconds: number, loadedSeconds: number }) => {
    setCurrentTime(playedSeconds);
    if (onTimeUpdate && duration > 0) {
      onTimeUpdate(playedSeconds, duration);
    }
  }, [onTimeUpdate, duration]);

  const handleReactPlayerReady = useCallback(() => {
    setLoading(false);
    setError(null);
    
    console.log('VideoPlayer: Player ready');
    
    if (reactPlayerRef.current) {
      // Verificar se já temos duração disponível
      const currentDuration = reactPlayerRef.current.getDuration();
      if (currentDuration && currentDuration > 0) {
        console.log(`VideoPlayer onReady: Duração já disponível: ${currentDuration}s`);
        setDuration(currentDuration);
      }
      
      // Aplicar o tempo inicial antes de iniciar a reprodução
      if (initialTime > 0 && !initialSeekAppliedRef.current) {
        console.log(`VideoPlayer onReady: Aplicando tempo inicial de ${initialTime} segundos (${Math.floor(initialTime / 60)}:${Math.floor(initialTime % 60)}min)`);
        
        // Pequeno delay para garantir que o player está realmente pronto
        setTimeout(() => {
          if (reactPlayerRef.current && !initialSeekAppliedRef.current) {
            reactPlayerRef.current.seekTo(initialTime);
            console.log(`VideoPlayer: Seek inicial aplicado para ${initialTime} segundos`);
            
            // Verificar se o seek foi aplicado corretamente após um breve delay
            setTimeout(verifySeekPosition, 1000);
            
            // Forçar play após aplicar o seek
            if (autoPlay) {
              reactPlayerRef.current.getInternalPlayer()?.play()
                .catch((e: Error) => console.warn("Erro ao iniciar autoplay após seek:", e));
            }
          }
        }, 300);
      }
      // Se não tiver tempo inicial, apenas inicia a reprodução
      else if (autoPlay) {
        try {
          reactPlayerRef.current.getInternalPlayer()?.play()
            .catch((e: Error) => console.warn("Erro ao iniciar autoplay no onReady:", e));
        } catch (err) {
          console.warn("Erro ao tentar iniciar autoplay:", err);
        }
      }
    }
  }, [autoPlay, initialTime, verifySeekPosition, initialSeekAppliedRef]);

  // Configuração personalizada para o ReactPlayer
  const reactPlayerConfig = useMemo(() => ({
    file: {
      attributes: {
        controlsList: 'nodownload',
        crossOrigin: 'anonymous'
      },
      // Não forçar o uso da versão local do hls.js
      // Permitir que o ReactPlayer use a versão CDN se necessário
      forceHLS: false,
      hlsVersion: '1.5.20',
      // Remover customHlsLoader para permitir que o ReactPlayer carregue o hls.js normalmente
      hlsOptions: {
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        fragLoadingMaxRetry: 4,
        // Configurações específicas para melhorar o seeking
        enableWorker: true,
        lowLatencyMode: false,
        backBufferLength: 90,
        // Configurações para melhor controle de seeking
        liveSyncDurationCount: 3,
        liveMaxLatencyDurationCount: 10,
        // Configurações para cache e buffer que podem afetar seeking
        manifestLoadingTimeOut: 10000,
        manifestLoadingMaxRetry: 1,
        manifestLoadingRetryDelay: 1000,
        // Habilitar seeking para streams HLS
        startLevel: -1,
        capLevelToPlayerSize: false,
        // Configurações para melhor performance de seeking
        maxLoadingDelay: 4,
        maxBufferHole: 0.5,
        highBufferWatchdogPeriod: 2,
        nudgeOffset: 0.1,
        nudgeMaxRetry: 3,
        // Configurações específicas para VOD (seeking)
        forceKeyFrameOnDiscontinuity: true,
        abrEwmaFastLive: 3.0,
        abrEwmaSlowLive: 9.0,
        abrEwmaFastVoD: 3.0,
        abrEwmaSlowVoD: 9.0
      }
    }
  }), []);

  const displayLoading = loading && !error && !isPlaying;
  const displayError = error && !isPlaying;

  // Garantir que a notificação seja exibida em tela cheia e gerenciar orientação
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreen = document.fullscreenElement !== null;
      const isMobile = window.innerWidth <= 768;
      
      if (isFullscreen) {
        console.log('Player está em tela cheia');
        // Adicionar classe para estilização de fullscreen
        if (containerRef.current) {
          containerRef.current.classList.add('fullscreen');
        }
      } else {
        console.log('Player saiu da tela cheia');
        // Remover classe de fullscreen
        if (containerRef.current) {
          containerRef.current.classList.remove('fullscreen');
        }
        
        // Se for mobile, liberar orientação quando sair do fullscreen
        if (isMobile && 'screen' in window && 'orientation' in window.screen) {
          try {
            (window.screen.orientation as any).unlock();
          } catch (e) {
            console.log('Não foi possível liberar orientação:', e);
          }
        }
      }
    };

    const handleOrientationChange = () => {
      console.log('Orientação mudou');
      
      // Se está em fullscreen, não fazer nada para não interferir
      if (document.fullscreenElement) {
        console.log('Em fullscreen - mantendo estado');
        return;
      }
      
      // Verificar se precisa ajustar layout quando não está em fullscreen
      const isMobile = window.innerWidth <= 768;
      const isLandscape = window.innerWidth > window.innerHeight;
      
      if (isMobile && isLandscape && containerRef.current) {
        console.log('Mobile em landscape - ajustando layout');
        // Adicionar classe para layout landscape quando não está em fullscreen
        containerRef.current.classList.add('mobile-landscape');
      } else if (containerRef.current) {
        containerRef.current.classList.remove('mobile-landscape');
      }
    };

    // Adicionar listeners para fullscreen e orientação
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);
    
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
      
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, []);

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        backgroundColor: '#000',
        aspectRatio: 'auto', // Permite que o player se ajuste ao container
        '& video': {
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        },
        '& .plyr': {
          width: '100%',
          height: '100%'
        },
        '& .plyr__video-wrapper': {
          width: '100%',
          height: '100%'
        },
        '&.fullscreen': {
          maxHeight: '100vh',
          height: '100vh',
          width: '100vw',
          position: 'fixed',
          top: 0,
          left: 0,
          zIndex: 9999,
          '& video': {
            width: '100%',
            height: '100%',
            objectFit: 'contain'
          },
          '& .plyr': {
            height: '100vh'
          },
          '& .plyr__video-wrapper': {
            height: '100vh'
          }
        },
        '&.mobile-landscape': {
          height: '50vh',
          maxHeight: '50vh',
          minHeight: '250px',
          '& video': {
            objectFit: 'contain'
          }
        }
      }}
      ref={containerRef}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {selectedPlayer === players.REACT_PLAYER ? (
        <>
          <ReactPlayer
            ref={reactPlayerRef}
            url={safeUrl}
            playing={isPlaying}
            controls={true}
            width="100%"
            height="100%"
            style={{ position: 'absolute', top: 0, left: 0 }}
            onReady={handleReactPlayerReady}
            onPlay={handleReactPlayerPlay}
            onPause={() => setIsPlaying(false)}
            onError={handleReactPlayerError}
            onBuffer={handleReactPlayerBuffer}
            onBufferEnd={handleReactPlayerBufferEnd}
            onProgress={handleReactPlayerProgress}
            onDuration={handleReactPlayerDuration}
            onSeek={handleReactPlayerSeek}
            config={reactPlayerConfig}
            progressInterval={1000}
          />
          
          {/* Overlay para notificações que permanece visível em tela cheia */}
          {notification && notification.show && (
            <Box
              sx={{
                position: 'absolute',
                top: '15%',
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 99999,
                width: 'auto',
                maxWidth: { xs: '85%', sm: '500px' },
                minWidth: { xs: '250px', sm: '350px' },
                backgroundColor: 'rgba(0, 0, 0, 0.9)',
                backdropFilter: 'blur(10px)',
                borderLeft: '6px solid #2196f3',
                borderRadius: '12px',
                boxShadow: '0 10px 30px rgba(0, 0, 0, 0.5)',
                padding: '16px',
                animation: 'slideIn 0.4s ease-out forwards',
                '@keyframes slideIn': {
                  from: {
                    transform: 'translateX(-50%) translateY(-20px)',
                    opacity: 0
                  },
                  to: {
                    transform: 'translateX(-50%) translateY(0)',
                    opacity: 1
                  }
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <Box
                  sx={{
                    backgroundColor: '#2196f3', 
                    borderRadius: '50%',
                    width: 48,
                    height: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                    boxShadow: '0 0 10px rgba(33, 150, 243, 0.5)'
                  }}
                >
                  <PlayArrowIcon sx={{ color: 'white', fontSize: 28 }} />
                </Box>
                <Box
                  sx={{
                    color: '#ffffff',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    {notification.message}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9, fontSize: '0.95rem' }}>
                    {notification.subtitle}
                  </Typography>
                </Box>
              </Box>
              
              {/* Barra de progresso personalizada */}
              <Box sx={{ 
                position: 'relative', 
                mt: 1.5, 
                height: 6, 
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: '0 0 6px 6px',
                overflow: 'hidden'
              }}>
                <Box 
                  sx={{ 
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    height: '100%',
                    width: '100%',
                    background: 'linear-gradient(to right, #2196f3, #21d4fd)',
                    animation: 'progress 3s linear forwards',
                    '@keyframes progress': {
                      from: { width: '0%' },
                      to: { width: '100%' }
                    }
                  }} 
                />
              </Box>
            </Box>
          )}
          
          {/* Custom overlay controls - Movido para o topo */}
          {showControls && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                p: 1,
                background: 'linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                transition: 'opacity 0.3s ease',
                opacity: showControls ? 1 : 0,
                pointerEvents: showControls ? 'auto' : 'none',
                zIndex: 10
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Paper
                  elevation={0}
                  sx={{
                    display: 'inline-flex',
                    bgcolor: 'rgba(255, 255, 255, 0.15)',
                    px: 1,
                    py: 0.5,
                    borderRadius: 4,
                    mr: 1
                  }}
                >
                  <LiveTvIcon sx={{ fontSize: 16, mr: 0.5, color: '#f44336' }} />
                  <Typography variant="caption" sx={{ color: 'white', fontWeight: 'medium' }}>
                    LIVE
                  </Typography>
                </Paper>
                {title && (
                  <Typography variant="body2" sx={{ color: 'white', fontWeight: 'medium' }}>
                    {title}
                  </Typography>
                )}
              </Box>
              
              {/* Player selection icons */}
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                <Tooltip title="React Player">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.REACT_PLAYER)}
                    sx={{
                      color: selectedPlayer === players.REACT_PLAYER ? 'primary.main' : 'rgba(255,255,255,0.7)',
                      bgcolor: 'rgba(0,0,0,0.3)',
                      mr: 0.5,
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.REACT_PLAYER ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <PlayCircleOutlineIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Video.js">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.VIDEOJS)}
                    sx={{
                      color: selectedPlayer === players.VIDEOJS ? 'primary.main' : 'rgba(255,255,255,0.7)',
                      bgcolor: 'rgba(0,0,0,0.3)',
                      mr: 0.5,
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.VIDEOJS ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <VideoSettingsIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Plyr">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.PLYR)}
                    sx={{
                      color: selectedPlayer === players.PLYR ? 'primary.main' : 'rgba(255,255,255,0.7)', 
                      bgcolor: 'rgba(0,0,0,0.3)',
                      mr: 0.5,
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.PLYR ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <OndemandVideoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="HLS.js">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.HLS)}
                    sx={{
                      color: selectedPlayer === players.HLS ? 'primary.main' : 'rgba(255,255,255,0.7)',
                      bgcolor: 'rgba(0,0,0,0.3)',
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.HLS ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <SlowMotionVideoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              
              {onClose && (
                <IconButton
                  size="small"
                  onClick={onClose}
                  sx={{
                    color: 'white',
                    bgcolor: 'rgba(0,0,0,0.5)',
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          )}
        </>
      ) : (
        <>
          <video
            ref={videoRef}
            className={selectedPlayer === players.VIDEOJS ? 'video-js' : ''}
            controls
            playsInline
            style={{ width: '100%', height: '100%' }}
          />
          
          {/* Custom overlay controls for other players */}
          {showControls && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                p: 1,
                background: 'linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                transition: 'opacity 0.3s ease',
                opacity: showControls ? 1 : 0,
                pointerEvents: showControls ? 'auto' : 'none',
                zIndex: 10
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {title && (
                  <Typography variant="body2" sx={{ color: 'white', fontWeight: 'medium' }}>
                    {title}
                  </Typography>
                )}
              </Box>
              
              {/* Player selection icons */}
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                <Tooltip title="React Player">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.REACT_PLAYER)}
                    sx={{
                      color: selectedPlayer === players.REACT_PLAYER ? 'primary.main' : 'rgba(255,255,255,0.7)',
                      bgcolor: 'rgba(0,0,0,0.3)',
                      mr: 0.5,
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.REACT_PLAYER ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <PlayCircleOutlineIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Video.js">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.VIDEOJS)}
                    sx={{
                      color: selectedPlayer === players.VIDEOJS ? 'primary.main' : 'rgba(255,255,255,0.7)',
                      bgcolor: 'rgba(0,0,0,0.3)',
                      mr: 0.5,
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.VIDEOJS ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <VideoSettingsIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Plyr">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.PLYR)}
                    sx={{
                      color: selectedPlayer === players.PLYR ? 'primary.main' : 'rgba(255,255,255,0.7)', 
                      bgcolor: 'rgba(0,0,0,0.3)',
                      mr: 0.5,
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.PLYR ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <OndemandVideoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="HLS.js">
                  <IconButton 
                    size="small" 
                    onClick={() => setSelectedPlayer(players.HLS)}
                    sx={{
                      color: selectedPlayer === players.HLS ? 'primary.main' : 'rgba(255,255,255,0.7)',
                      bgcolor: 'rgba(0,0,0,0.3)',
                      '&:hover': { bgcolor: 'rgba(0,0,0,0.5)' },
                      border: selectedPlayer === players.HLS ? '1px solid' : 'none',
                      borderColor: 'primary.main',
                      padding: '3px'
                    }}
                  >
                    <SlowMotionVideoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              
              {onClose && (
                <IconButton
                  size="small"
                  onClick={onClose}
                  sx={{
                    color: 'white',
                    bgcolor: 'rgba(0,0,0,0.5)',
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          )}
        </>
      )}
      
      {/* Loading spinner */}
      {displayLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 5,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <CircularProgress color="primary" size={48} />
          <Typography variant="body2" sx={{ color: 'white', mt: 2 }}>
            Loading stream...
          </Typography>
        </Box>
      )}
      
      {/* Error message - só exibe se não estiver tocando */}
      {displayError && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 5,
            textAlign: 'center',
            maxWidth: '80%'
          }}
        >
          <Typography variant="h6" sx={{ color: '#f44336', mb: 1 }}>
            Stream Error
          </Typography>
          <Typography variant="body2" sx={{ color: 'white' }}>
            {error}
          </Typography>
          <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)', mt: 1, display: 'block' }}>
            URL: {url}
          </Typography>
        </Box>
      )}
    </Box>
  );
});

export default VideoPlayer;