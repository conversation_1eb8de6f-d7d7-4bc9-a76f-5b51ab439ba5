/**
 * Utilitário para abrir links externos no navegador padrão
 * Funciona tanto no Electron quanto no navegador web
 */

// Declaração de tipos para o Electron API
declare global {
  interface Window {
    electronAPI?: {
      openExternal: (url: string) => boolean;
      getAppVersion: () => string;
      getPlatform: () => string;
    };
    isElectronApp?: boolean;
  }
}

/**
 * Verifica se estamos rodando no Electron
 */
export function isElectronApp(): boolean {
  return (
    typeof window !== 'undefined' && 
    (
      window.isElectronApp === true ||
      window.location.protocol === 'file:' ||
      (window as any).process?.versions?.electron
    )
  );
}

/**
 * Lista de domínios do Neko TV que devem abrir no navegador externo
 */
const NEKO_TV_DOMAINS = [
  'nekotv.vercel.app',
  'nekotv.top',
  'github.com/nekotv',
  'github.com/nekotv-official',
  'discord.gg/nekotv'
];

/**
 * Lista de domínios externos comuns que devem abrir no navegador externo
 */
const EXTERNAL_DOMAINS = [
  'github.com',
  'discord.gg',
  'wa.me',
  'whatsapp.com',
  'youtube.com',
  'youtu.be',
  'twitter.com',
  'x.com',
  'facebook.com',
  'instagram.com'
];

/**
 * Verifica se uma URL deve ser aberta externamente
 */
export function shouldOpenExternally(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    // Sempre abrir domínios do Neko TV externamente
    if (NEKO_TV_DOMAINS.some(domain => hostname.includes(domain))) {
      return true;
    }
    
    // Abrir outros domínios externos se estiver no Electron
    if (isElectronApp() && EXTERNAL_DOMAINS.some(domain => hostname.includes(domain))) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Erro ao analisar URL:', error);
    return false;
  }
}

/**
 * Abre um link externo no navegador padrão
 * @param url - URL para abrir
 * @param target - Target do link (padrão: '_blank')
 * @returns boolean - true se foi aberto com sucesso
 */
export function openExternal(url: string, target: string = '_blank'): boolean {
  try {
    // Se estiver no Electron e tiver a API disponível
    if (isElectronApp() && window.electronAPI?.openExternal) {
      console.log('🖥️ Abrindo link no navegador externo via Electron:', url);
      return window.electronAPI.openExternal(url);
    }
    
    // Fallback para navegador web normal
    if (shouldOpenExternally(url)) {
      console.log('🌐 Abrindo link no navegador externo via window.open:', url);
      window.open(url, target, 'noopener,noreferrer');
      return true;
    }
    
    // Para links internos ou quando não deve abrir externamente
    if (target === '_blank') {
      window.open(url, target, 'noopener,noreferrer');
    } else {
      window.location.href = url;
    }
    
    return true;
  } catch (error) {
    console.error('Erro ao abrir link externo:', error);
    return false;
  }
}

/**
 * Handler para cliques em links que devem abrir externamente
 * @param url - URL para abrir
 * @param target - Target do link (padrão: '_blank')
 */
export function handleExternalLinkClick(url: string, target: string = '_blank'): void {
  const success = openExternal(url, target);
  
  if (!success) {
    console.warn('Falha ao abrir link externo:', url);
  }
}

/**
 * Hook para substituir window.open em componentes React
 * @param url - URL para abrir
 * @param target - Target do link (padrão: '_blank')
 */
export function useExternalLink(url: string, target: string = '_blank') {
  return () => handleExternalLinkClick(url, target);
}

/**
 * Utilitário para criar props de link externo
 * @param url - URL para abrir
 * @param target - Target do link (padrão: '_blank')
 */
export function createExternalLinkProps(url: string, target: string = '_blank') {
  if (isElectronApp() && shouldOpenExternally(url)) {
    // No Electron, usar onClick para interceptar
    return {
      onClick: (e: React.MouseEvent) => {
        e.preventDefault();
        handleExternalLinkClick(url, target);
      },
      href: '#', // Placeholder href
      target: undefined,
      rel: undefined
    };
  }
  
  // No navegador web, usar props normais
  return {
    href: url,
    target,
    rel: target === '_blank' ? 'noopener noreferrer' : undefined,
    onClick: undefined
  };
}

export default {
  isElectronApp,
  shouldOpenExternally,
  openExternal,
  handleExternalLinkClick,
  useExternalLink,
  createExternalLinkProps
};
