// Script de teste para verificar se o CORS foi resolvido
console.log('🧪 Testando conexão IPTV...');

const testUrl = 'http://pandatvuhd.cloud/player_api.php?username=1Mtt2U&password=SUZ3aJ';

fetch(testUrl)
  .then(response => {
    console.log('✅ Fetch bem-sucedido!');
    console.log('Status:', response.status);
    console.log('Headers:', [...response.headers.entries()]);
    return response.json();
  })
  .then(data => {
    console.log('📄 Dados recebidos:', data);
    if (data.user_info) {
      console.log('👤 Usuário:', data.user_info.username);
      console.log('🔐 Status:', data.user_info.status);
      console.log('🔑 Auth:', data.user_info.auth);
    }
  })
  .catch(error => {
    console.error('❌ Erro no fetch:', error);
    console.error('Tipo do erro:', error.name);
    console.error('Mensagem:', error.message);
  });
