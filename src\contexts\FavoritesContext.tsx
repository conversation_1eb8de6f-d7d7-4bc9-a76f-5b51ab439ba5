import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Stream } from '../services/iptvService';

interface FavoritesContextType {
  favorites: Stream[];
  addFavorite: (channel: Stream) => void;
  removeFavorite: (channelId: string) => void;
  isFavorite: (channelId: string) => boolean;
  forceSave: () => void;
}

const FAVORITES_STORAGE_KEY = 'channelFavorites';
const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

export const FavoritesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [favorites, setFavorites] = useState<Stream[]>([]);
  const [initialized, setInitialized] = useState(false);

  // Função para salvar os favoritos
  const saveFavoritesToStorage = useCallback((favoritesToSave: Stream[]) => {
    try {
      console.log('Salvando favoritos:', favoritesToSave.length, 'canais');
      localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favoritesToSave));
      // Verificar se salvou corretamente
      const saved = localStorage.getItem(FAVORITES_STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        console.log('Favoritos salvos com sucesso:', parsed.length, 'canais');
      }
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
    }
  }, []);

  // Função para carregar os favoritos
  const loadFavoritesFromStorage = useCallback(() => {
    try {
      const storedFavorites = localStorage.getItem(FAVORITES_STORAGE_KEY);
      console.log('Tentando carregar favoritos do localStorage');
      
      if (storedFavorites) {
        const parsedFavorites = JSON.parse(storedFavorites);
        console.log('Favoritos carregados:', parsedFavorites.length, 'canais');
        setFavorites(parsedFavorites);
      } else {
        console.log('Nenhum favorito encontrado no localStorage');
        setFavorites([]);
      }
    } catch (error) {
      console.error('Erro ao carregar favoritos:', error);
      localStorage.removeItem(FAVORITES_STORAGE_KEY);
      setFavorites([]);
    } finally {
      setInitialized(true);
    }
  }, []);

  // Carregar favoritos do localStorage quando o componente for montado
  useEffect(() => {
    loadFavoritesFromStorage();
    
    // Adicionar listener para storage eventos (útil se múltiplas abas estiverem abertas)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === FAVORITES_STORAGE_KEY) {
        loadFavoritesFromStorage();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [loadFavoritesFromStorage]);

  // Salvar favoritos no localStorage quando a lista for atualizada
  useEffect(() => {
    if (initialized) {
      saveFavoritesToStorage(favorites);
    }
  }, [favorites, initialized, saveFavoritesToStorage]);

  const addFavorite = useCallback((channel: Stream) => {
    setFavorites(prev => {
      // Verificar se o canal já existe nos favoritos
      if (!prev.some(fav => fav.id === channel.id)) {
        const newFavorites = [...prev, channel];
        return newFavorites;
      }
      return prev;
    });
  }, []);

  const removeFavorite = useCallback((channelId: string) => {
    setFavorites(prev => {
      const newFavorites = prev.filter(fav => fav.id !== channelId);
      return newFavorites;
    });
  }, []);

  const isFavorite = useCallback((channelId: string) => {
    return favorites.some(fav => fav.id === channelId);
  }, [favorites]);

  // Função para forçar o salvamento
  const forceSave = useCallback(() => {
    console.log('Forçando salvamento dos favoritos:', favorites.length, 'canais');
    saveFavoritesToStorage(favorites);
  }, [favorites, saveFavoritesToStorage]);

  // Salvar favoritos antes de fechar a aba ou o aplicativo
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log('Aplicativo fechando, salvando favoritos...');
      saveFavoritesToStorage(favorites);
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [favorites, saveFavoritesToStorage]);

  const value = {
    favorites,
    addFavorite,
    removeFavorite,
    isFavorite,
    forceSave
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error('useFavorites deve ser usado dentro de um FavoritesProvider');
  }
  return context;
}; 