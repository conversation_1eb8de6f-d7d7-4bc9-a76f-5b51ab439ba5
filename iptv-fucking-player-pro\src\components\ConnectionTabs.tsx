import React from 'react'
import { XMarkIcon, PlusIcon } from '@heroicons/react/24/outline'
import { useConnectionStore } from '../stores/connectionStore'

const ConnectionTabs: React.FC = () => {
  const {
    activeTabs,
    switchTab,
    removeTab,
    openAuthModal
  } = useConnectionStore()

  // Don't show tabs if there's only one or none
  if (activeTabs.length <= 1) {
    return null
  }

  return (
    <div className="bg-slate-900/50 border-b border-slate-700/50 px-4">
      <div className="flex items-center space-x-1 overflow-x-auto scrollbar-hide">
        {activeTabs.map((tab) => (
          <div
            key={tab.id}
            className={`group relative flex items-center min-w-[120px] max-w-[200px] px-3 py-2 text-sm font-medium cursor-pointer transition-all ${
              tab.isActive
                ? 'bg-slate-800 text-white border-t-2 border-sky-500'
                : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
            }`}
            onClick={() => switchTab(tab.id)}
            style={{
              clipPath: 'polygon(8px 0%, calc(100% - 8px) 0%, 100% 100%, 0% 100%)'
            }}
          >
            {/* Loading indicator */}
            {tab.isLoading && (
              <div className="w-2 h-2 mr-2 bg-yellow-500 rounded-full animate-pulse" />
            )}
            
            {/* Error indicator */}
            {tab.hasError && (
              <div className="w-2 h-2 mr-2 bg-red-500 rounded-full" />
            )}
            
            {/* Connection status indicator */}
            {!tab.isLoading && !tab.hasError && (
              <div className="w-2 h-2 mr-2 bg-green-500 rounded-full" />
            )}

            {/* Tab name */}
            <span className="truncate flex-1">
              {tab.name}
            </span>

            {/* Close button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                removeTab(tab.id)
              }}
              className="ml-2 p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-slate-700 transition-all"
            >
              <XMarkIcon className="w-3 h-3" />
            </button>
          </div>
        ))}

        {/* Add new connection tab */}
        <button
          onClick={() => openAuthModal()}
          className="flex items-center justify-center w-8 h-8 ml-2 text-slate-400 hover:text-white hover:bg-slate-800 rounded transition-colors"
          title="Adicionar nova conexão"
        >
          <PlusIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

export default ConnectionTabs