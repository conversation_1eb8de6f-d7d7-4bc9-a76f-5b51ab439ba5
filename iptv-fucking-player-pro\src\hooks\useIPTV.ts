import { useState, useEffect, useCallback } from 'react'
import IPTVService from '../services/iptvService'
import { DatabaseService } from '../services/database'
import { useConnectionStore } from '../stores/connectionStore'

interface Category {
  category_id: string
  category_name: string
  parent_id: number
}

interface Stream {
  num: number
  name: string
  stream_type: string
  stream_id: number
  stream_icon: string
  epg_channel_id: string
  added: string
  category_id: string
  custom_sid: string
  tv_archive: number
  direct_source: string
  tv_archive_duration: number
}

interface UseIPTVReturn {
  // Connection state
  isConnected: boolean
  
  // Categories
  liveCategories: Category[]
  movieCategories: Category[]
  seriesCategories: Category[]
  
  // Streams
  liveStreams: Stream[]
  movieStreams: Stream[]
  seriesStreams: Stream[]
  
  // Loading states
  isLoadingCategories: boolean
  isLoadingStreams: boolean
  
  // Actions
  loadStreams: (categoryId?: string) => Promise<void>
  getStreamUrl: (streamId: number, type: 'live' | 'movie' | 'series') => string
  
  // Error handling
  error: string | null
  clearError: () => void
}

const useIPTV = (): UseIPTVReturn => {
  const [iptvService] = useState(() => new IPTVService())
  const [dbService] = useState(() => DatabaseService.getInstance())
  
  // Get connection state from store
  const { getActiveConnection } = useConnectionStore()
  const activeConnection = getActiveConnection()
  const isConnected = !!activeConnection
  
  // Categories
  const [liveCategories, setLiveCategories] = useState<Category[]>([])
  const [movieCategories, setMovieCategories] = useState<Category[]>([])
  const [seriesCategories, setSeriesCategories] = useState<Category[]>([])
  
  // Streams
  const [liveStreams, setLiveStreams] = useState<Stream[]>([])
  const [movieStreams, setMovieStreams] = useState<Stream[]>([])
  const [seriesStreams, setSeriesStreams] = useState<Stream[]>([])
  
  // Loading states
  const [isLoadingCategories, setIsLoadingCategories] = useState(false)
  const [isLoadingStreams, setIsLoadingStreams] = useState(false)
  
  // Error handling
  const [error, setError] = useState<string | null>(null)

  // Initialize services
  useEffect(() => {
    const initializeServices = async () => {
      try {
        await iptvService.initialize()
        await dbService.initialize()
        console.log('✅ Services initialized')
      } catch (error) {
        console.error('❌ Error initializing services:', error)
        setError('Failed to initialize services')
      }
    }

    initializeServices()
  }, [iptvService, dbService])

  // Load categories when connection changes
  useEffect(() => {
    if (isConnected && activeConnection) {
      loadCategories()
    } else {
      // Clear data when disconnected
      setLiveCategories([])
      setMovieCategories([])
      setSeriesCategories([])
      setLiveStreams([])
      setMovieStreams([])
      setSeriesStreams([])
    }
  }, [isConnected, activeConnection?.id])

  const loadCategories = async () => {
    if (!isConnected || !activeConnection) return

    setIsLoadingCategories(true)
    setError(null)

    try {
      const [live, movies, series] = await Promise.all([
        iptvService.getLiveCategories().catch(error => {
          console.error('Error loading live categories:', error)
          return []
        }),
        iptvService.getMovieCategories().catch(error => {
          console.error('Error loading movie categories:', error)
          return []
        }),
        iptvService.getSeriesCategories().catch(error => {
          console.error('Error loading series categories:', error)
          return []
        })
      ])

      setLiveCategories(live)
      setMovieCategories(movies)
      setSeriesCategories(series)
    } catch (error) {
      console.error('Error loading categories:', error)
      setError('Failed to load categories')
    } finally {
      setIsLoadingCategories(false)
    }
  }

  const loadStreams = async (categoryId?: string) => {
    if (!isConnected || !activeConnection) return

    setIsLoadingStreams(true)
    setError(null)

    try {
      // Load streams from database first (cache)
      const [cachedLive, cachedMovies, cachedSeries] = await Promise.all([
        dbService.getStreams('live', categoryId),
        dbService.getStreams('movie', categoryId),
        dbService.getStreams('series', categoryId)
      ])

      // Set cached data immediately
      setLiveStreams(cachedLive)
      setMovieStreams(cachedMovies)
      setSeriesStreams(cachedSeries)

      // Then load fresh data from API
      const [live, movies, series] = await Promise.all([
        iptvService.getLiveStreams(categoryId).catch(error => {
          console.error('Error loading live streams:', error)
          return cachedLive // Fallback to cached data
        }),
        iptvService.getMovieStreams(categoryId).catch(error => {
          console.error('Error loading movie streams:', error)
          return cachedMovies // Fallback to cached data
        }),
        iptvService.getSeriesStreams(categoryId).catch(error => {
          console.error('Error loading series streams:', error)
          return cachedSeries // Fallback to cached data
        })
      ])

      // Update with fresh data
      setLiveStreams(live)
      setMovieStreams(movies)
      setSeriesStreams(series)

      // Save fresh data to database
      await Promise.all([
        dbService.saveStreams(live, 'live', categoryId),
        dbService.saveStreams(movies, 'movie', categoryId),
        dbService.saveStreams(series, 'series', categoryId)
      ])

    } catch (error) {
      console.error('Error loading streams:', error)
      setError('Failed to load streams')
    } finally {
      setIsLoadingStreams(false)
    }
  }

  const getStreamUrl = useCallback((streamId: number, type: 'live' | 'movie' | 'series'): string => {
    return iptvService.getStreamUrl(streamId, type)
  }, [iptvService])

  const clearError = () => {
    setError(null)
  }

  return {
    // Connection state
    isConnected,
    
    // Categories
    liveCategories,
    movieCategories,
    seriesCategories,
    
    // Streams
    liveStreams,
    movieStreams,
    seriesStreams,
    
    // Loading states
    isLoadingCategories,
    isLoadingStreams,
    
    // Actions
    loadStreams,
    getStreamUrl,
    
    // Error handling
    error,
    clearError
  }
}

export default useIPTV