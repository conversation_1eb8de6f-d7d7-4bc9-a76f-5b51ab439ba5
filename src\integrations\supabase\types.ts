export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      content_views: {
        Row: {
          content_category: string | null
          content_id: string
          content_image: string | null
          content_name: string
          content_type: string
          created_at: string | null
          device_id: string
          epg_info: Json | null
          id: string
          license_id: string
          started_at: string | null
        }
        Insert: {
          content_category?: string | null
          content_id: string
          content_image?: string | null
          content_name: string
          content_type: string
          created_at?: string | null
          device_id: string
          epg_info?: Json | null
          id?: string
          license_id: string
          started_at?: string | null
        }
        Update: {
          content_category?: string | null
          content_id?: string
          content_image?: string | null
          content_name?: string
          content_type?: string
          created_at?: string | null
          device_id?: string
          epg_info?: Json | null
          id?: string
          license_id?: string
          started_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "content_views_license_id_fkey"
            columns: ["license_id"]
            isOneToOne: false
            referencedRelation: "licenses"
            referencedColumns: ["id"]
          },
        ]
      }
      licenses: {
        Row: {
          active: boolean | null
          client_name: string | null
          created_at: string | null
          created_by: string | null
          device_id: string | null
          expiration_date: string | null
          id: string
          iptv_url: string | null
          license_key: string
          notes: string | null
          updated_at: string | null
          config_updated_at: string | null
          last_active: string | null
          last_playlist_fetch: string | null
          config_hash: string | null
        }
        Insert: {
          active?: boolean | null
          client_name?: string | null
          created_at?: string | null
          created_by?: string | null
          device_id?: string | null
          expiration_date?: string | null
          id?: string
          iptv_url?: string | null
          license_key: string
          notes?: string | null
          updated_at?: string | null
          config_updated_at?: string | null
          last_active?: string | null
          last_playlist_fetch?: string | null
          config_hash?: string | null
        }
        Update: {
          active?: boolean | null
          client_name?: string | null
          created_at?: string | null
          created_by?: string | null
          device_id?: string | null
          expiration_date?: string | null
          id?: string
          iptv_url?: string | null
          license_key?: string
          notes?: string | null
          updated_at?: string | null
          config_updated_at?: string | null
          last_active?: string | null
          last_playlist_fetch?: string | null
          config_hash?: string | null
        }
        Relationships: []
      }
      user_profiles: {
        Row: {
          created_at: string | null
          email: string
          id: string
          is_disabled: boolean | null
          updated_at: string | null
          user_role: string
        }
        Insert: {
          created_at?: string | null
          email: string
          id: string
          is_disabled?: boolean | null
          updated_at?: string | null
          user_role?: string
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          is_disabled?: boolean | null
          updated_at?: string | null
          user_role?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_user: {
        Args: { email: string; password: string; role?: string }
        Returns: string
      }
      check_license: {
        Args: { key: string; device: string }
        Returns: {
          is_valid: boolean
          error_message: string
          iptv_url: string
        }[]
      }
      generate_license: {
        Args: { client_name: string; iptv_url?: string }
        Returns: {
          id: string
          license_key: string
        }[]
      }
      generate_license_key: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_random_key: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_content_views_summary: {
        Args: Record<PropertyKey, never>
        Returns: {
          license_id: string
          license_key: string
          customer_name: string
          device_id: string
          last_view_date: string
          total_views: number
          live_views: number
          movie_views: number
          series_views: number
        }[]
      }
      is_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
      register_content_view: {
        Args: {
          p_license_key: string
          p_device_id: string
          p_content_id: string
          p_content_name: string
          p_content_type: string
          p_content_category?: string
          p_content_image?: string
          p_epg_info?: Json
        }
        Returns: Json
      }
      toggle_license: {
        Args: { key: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

// Type aliases for easier use
export type License = Tables<"licenses">
export type LicenseInsert = TablesInsert<"licenses">
export type LicenseUpdate = TablesUpdate<"licenses">

export type ContentView = Tables<"content_views">
export type ContentViewInsert = TablesInsert<"content_views">
export type ContentViewUpdate = TablesUpdate<"content_views">

export type UserProfile = Tables<"user_profiles">
export type UserProfileInsert = TablesInsert<"user_profiles">
export type UserProfileUpdate = TablesUpdate<"user_profiles"> 