import { useState, useEffect, useCallback } from 'react';
import { iptvUpdateDetector, UpdateInfo } from '../services/iptvUpdateDetector';

interface UseUpdateDetectionReturn {
  hasUpdates: boolean;
  updates: UpdateInfo[];
  showModal: boolean;
  loading: boolean;
  checkForUpdates: () => Promise<void>;
  handleUpdate: () => Promise<void>;
  dismissModal: () => void;
  forceCheck: () => void;
}

interface UseUpdateDetectionOptions {
  autoCheck?: boolean;
  checkOnMount?: boolean;
  onUpdateDetected?: (updates: UpdateInfo[]) => void;
  onUpdateComplete?: () => void;
}

export const useUpdateDetection = (options: UseUpdateDetectionOptions = {}): UseUpdateDetectionReturn => {
  const {
    autoCheck = true,
    checkOnMount = true,
    onUpdateDetected,
    onUpdateComplete
  } = options;

  const [hasUpdates, setHasUpdates] = useState(false);
  const [updates, setUpdates] = useState<UpdateInfo[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  /**
   * Verifica atualizações
   */
  const checkForUpdates = useCallback(async () => {
    if (loading) return;

    setLoading(true);
    try {
      const result = await iptvUpdateDetector.checkForUpdates();

      setHasUpdates(result.hasUpdates);
      setUpdates(result.updates);

      if (result.hasUpdates && result.updates.length > 0) {
        setShowModal(true);
        onUpdateDetected?.(result.updates);
      }
    } catch (error) {
      // Silenciar erro quando não há conexão configurada (normal na tela inicial)
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (!errorMessage.includes('No connection URL found')) {
        console.error('Erro ao verificar atualizações:', error);
      }
    } finally {
      setLoading(false);
    }
  }, [loading, onUpdateDetected]);

  /**
   * Executa atualização (limpa cache e recarrega)
   */
  const handleUpdate = useCallback(async () => {
    setLoading(true);
    try {
      // Limpa cache IPTV
      await iptvUpdateDetector.clearIptvCache();

      // Aguarda um pouco para garantir que o cache foi limpo
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Fecha modal
      setShowModal(false);
      setHasUpdates(false);
      setUpdates([]);

      // Callback de conclusão
      onUpdateComplete?.();

      // Recarrega a página para garantir que tudo seja atualizado
      window.location.reload();
    } catch (error) {
      console.error('Erro ao executar atualização:', error);
    } finally {
      setLoading(false);
    }
  }, [onUpdateComplete]);

  /**
   * Dispensa modal sem atualizar
   */
  const dismissModal = useCallback(() => {
    setShowModal(false);
  }, []);

  /**
   * Força nova verificação
   */
  const forceCheck = useCallback(() => {
    iptvUpdateDetector.forceCheck();
    checkForUpdates();
  }, [checkForUpdates]);

  // Verificação inicial
  useEffect(() => {
    if (checkOnMount && autoCheck) {
      // Aguarda um pouco após o mount para não interferir na inicialização
      const timeoutId = setTimeout(() => {
        checkForUpdates();
      }, 2000);

      return () => clearTimeout(timeoutId);
    }
  }, [checkOnMount, autoCheck, checkForUpdates]);

  return {
    hasUpdates,
    updates,
    showModal,
    loading,
    checkForUpdates,
    handleUpdate,
    dismissModal,
    forceCheck
  };
};

export default useUpdateDetection;
