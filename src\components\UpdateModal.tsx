import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  Update as UpdateIcon,
  Tv as TvIcon,
  Movie as MovieIcon,
  LiveTv as LiveTvIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface UpdateInfo {
  type: 'channels' | 'movies' | 'series' | 'categories';
  action: 'added' | 'removed' | 'updated';
  count: number;
  details?: string[];
}

interface UpdateModalProps {
  open: boolean;
  onClose: () => void;
  onUpdate: () => void;
  updates: UpdateInfo[];
  loading?: boolean;
}

const UpdateModal: React.FC<UpdateModalProps> = ({
  open,
  onClose,
  onUpdate,
  updates,
  loading = false
}) => {
  const theme = useTheme();

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case 'channels': return <LiveTvIcon />;
      case 'movies': return <MovieIcon />;
      case 'series': return <TvIcon />;
      case 'categories': return <EditIcon />;
      default: return <UpdateIcon />;
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'added': return <AddIcon sx={{ color: 'success.main' }} />;
      case 'removed': return <RemoveIcon sx={{ color: 'error.main' }} />;
      case 'updated': return <EditIcon sx={{ color: 'warning.main' }} />;
      default: return <UpdateIcon />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'added': return 'success';
      case 'removed': return 'error';
      case 'updated': return 'warning';
      default: return 'primary';
    }
  };

  const getActionText = (action: string) => {
    switch (action) {
      case 'added': return 'Adicionado';
      case 'removed': return 'Removido';
      case 'updated': return 'Atualizado';
      default: return 'Modificado';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'channels': return 'Canais';
      case 'movies': return 'Filmes';
      case 'series': return 'Séries';
      case 'categories': return 'Categorias';
      default: return 'Conteúdo';
    }
  };

  const totalUpdates = updates.reduce((sum, update) => sum + update.count, 0);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95))'
            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95), rgba(241, 245, 249, 0.95))',
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          pb: 1,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
        }}
      >
        <Box
          sx={{
            p: 1,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <UpdateIcon />
        </Box>
        <Box>
          <Typography variant="h6" fontWeight="bold">
            Atualizações Disponíveis
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {totalUpdates} {totalUpdates === 1 ? 'item atualizado' : 'itens atualizados'}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Detectamos atualizações no seu conteúdo IPTV. Clique em "Atualizar" para carregar o novo conteúdo.
        </Typography>

        <List sx={{ py: 0 }}>
          {updates.map((update, index) => (
            <React.Fragment key={index}>
              <ListItem
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  background: alpha(getActionColor(update.action) === 'success' 
                    ? theme.palette.success.main 
                    : getActionColor(update.action) === 'error'
                    ? theme.palette.error.main
                    : theme.palette.warning.main, 0.1),
                }}
              >
                <ListItemIcon>
                  {getUpdateIcon(update.type)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {getTypeText(update.type)}
                      </Typography>
                      <Chip
                        size="small"
                        icon={getActionIcon(update.action)}
                        label={`${getActionText(update.action)} (${update.count})`}
                        color={getActionColor(update.action) as any}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={update.details?.join(', ')}
                />
              </ListItem>
              {index < updates.length - 1 && <Divider sx={{ my: 1 }} />}
            </React.Fragment>
          ))}
        </List>

        <Box
          sx={{
            mt: 2,
            p: 2,
            borderRadius: 2,
            background: alpha(theme.palette.info.main, 0.1),
            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
          }}
        >
          <Typography variant="body2" color="info.main" fontWeight="bold">
            💡 Dica: As atualizações incluem novos canais, filmes, séries e categorias adicionadas ao seu painel.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{ borderRadius: 2 }}
        >
          Mais Tarde
        </Button>
        <Button
          onClick={onUpdate}
          variant="contained"
          startIcon={loading ? <RefreshIcon sx={{ animation: 'spin 1s linear infinite' }} /> : <UpdateIcon />}
          disabled={loading}
          sx={{
            borderRadius: 2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            '&:hover': {
              background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
            },
          }}
        >
          {loading ? 'Atualizando...' : 'Atualizar Agora'}
        </Button>
      </DialogActions>

      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </Dialog>
  );
};

export default UpdateModal;
