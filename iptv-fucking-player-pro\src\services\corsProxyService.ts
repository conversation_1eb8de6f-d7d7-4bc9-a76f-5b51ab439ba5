/**
 * Serviço de proxy CORS para contornar limitações de CORS em desenvolvimento
 */
export class CorsProxyService {
  private static instance: CorsProxyService;
  
  // Lista de proxies CORS públicos (em ordem de preferência)
  private readonly CORS_PROXIES = [
    'https://corsproxy.io/?',
    'https://api.allorigins.win/raw?url=',
    'https://cors-anywhere.herokuapp.com/',
    'https://api.codetabs.com/v1/proxy?quest=',
    'https://cors.eu.org/',
  ];
  
  private constructor() {}
  
  public static getInstance(): CorsProxyService {
    if (!CorsProxyService.instance) {
      CorsProxyService.instance = new CorsProxyService();
    }
    return CorsProxyService.instance;
  }

  /**
   * Busca dados através de proxy CORS
   * @param url URL para buscar os dados
   * @returns Response do fetch
   */
  public async fetchWithProxy(url: string): Promise<Response> {
    const errors: string[] = [];
    const encodedUrl = encodeURIComponent(url);
    
    console.log('🔄 Tentando buscar via proxy CORS:', url);
    
    // Tentar cada proxy até um funcionar
    for (const proxy of this.CORS_PROXIES) {
      try {
        console.log(`🔄 Tentando proxy: ${proxy}`);
        
        const proxyUrl = `${proxy}${encodedUrl}`;
        const response = await fetch(proxyUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Cache-Control': 'no-cache',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
          cache: 'no-cache',
          mode: 'cors',
        });
        
        if (response.ok) {
          console.log(`✅ Proxy funcionou: ${proxy}`);
          return response;
        } else {
          const errorMsg = `Proxy falhou (${response.status}): ${proxy}`;
          console.warn(errorMsg);
          errors.push(errorMsg);
        }
      } catch (error) {
        const errorMsg = `Proxy erro: ${proxy} - ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
        console.warn(errorMsg);
        errors.push(errorMsg);
        continue;
      }
    }
    
    console.error('❌ Todos os proxies CORS falharam:', errors);
    throw new Error(`Todos os proxies CORS falharam ao acessar: ${url}`);
  }
  
  /**
   * Busca JSON através de proxy CORS
   * @param url URL para buscar o JSON
   * @returns Dados JSON convertidos para o tipo T
   */
  public async fetchJson<T = any>(url: string): Promise<T> {
    try {
      const response = await this.fetchWithProxy(url);
      return await response.json();
    } catch (error) {
      throw new Error(`Falha ao buscar JSON: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Busca texto através de proxy CORS
   * @param url URL para buscar o texto
   * @returns Texto da resposta
   */
  public async fetchText(url: string): Promise<string> {
    try {
      const response = await this.fetchWithProxy(url);
      return await response.text();
    } catch (error) {
      throw new Error(`Falha ao buscar texto: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
  
  /**
   * Verifica se um proxy específico está funcionando
   */
  public async testProxy(proxyUrl: string, testUrl: string = 'https://httpbin.org/json'): Promise<boolean> {
    try {
      const encodedTestUrl = encodeURIComponent(testUrl);
      const response = await fetch(`${proxyUrl}${encodedTestUrl}`, {
        method: 'GET',
        cache: 'no-cache',
      });
      
      return response.ok;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Testa todos os proxies e retorna os que estão funcionando
   */
  public async getWorkingProxies(): Promise<string[]> {
    const workingProxies: string[] = [];
    
    for (const proxy of this.CORS_PROXIES) {
      const isWorking = await this.testProxy(proxy);
      if (isWorking) {
        workingProxies.push(proxy);
      }
    }
    
    return workingProxies;
  }
  
  /**
   * Adiciona um proxy customizado à lista
   */
  public addCustomProxy(proxyUrl: string): void {
    if (!this.CORS_PROXIES.includes(proxyUrl)) {
      this.CORS_PROXIES.unshift(proxyUrl); // Adiciona no início (prioridade)
    }
  }
  
  /**
   * Remove um proxy da lista
   */
  public removeProxy(proxyUrl: string): void {
    const index = this.CORS_PROXIES.indexOf(proxyUrl);
    if (index > -1) {
      this.CORS_PROXIES.splice(index, 1);
    }
  }
  
  /**
   * Obtém a lista atual de proxies
   */
  public getProxies(): string[] {
    return [...this.CORS_PROXIES];
  }
}

// Exporta instância singleton para uso em outros módulos
export const corsProxyService = CorsProxyService.getInstance();
export default corsProxyService;
