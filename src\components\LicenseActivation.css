.license-activation {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  padding: 20px;
}

.license-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 30px;
  width: 100%;
  max-width: 500px;
  text-align: center;
}

.license-card h2 {
  color: #1e3a8a;
  margin-bottom: 25px;
  font-size: 28px;
}

.device-info {
  background-color: #f1f5f9;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
}

.device-hint {
  font-size: 12px;
  color: #64748b;
  margin-top: 5px;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #334155;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border-radius: 6px;
  border: 1px solid #cbd5e1;
  font-size: 16px;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.activate-btn {
  width: 100%;
  padding: 14px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.activate-btn:hover {
  background-color: #2563eb;
}

.activate-btn:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
}

.message {
  margin-bottom: 20px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
}

.info {
  background-color: #dbeafe;
  color: #1e40af;
}

.help-text {
  margin-top: 20px;
  font-size: 14px;
  color: #64748b;
}

.license-success {
  text-align: center;
  padding: 20px;
}

.success-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  font-size: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
} 