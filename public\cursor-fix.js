// Cursor fix for PWA
(function() {
  'use strict';
  
  // Function to ensure cursor is visible
  function ensureCursorVisibility() {
    // Remove any CSS that might be hiding the cursor
    const style = document.createElement('style');
    style.textContent = `
      * {
        cursor: inherit !important;
      }
      
      html, body {
        cursor: default !important;
      }
      
      button, [role="button"], a, input, select, textarea {
        cursor: pointer !important;
      }
      
      input[type="text"], input[type="email"], input[type="password"], 
      input[type="number"], input[type="search"], input[type="url"], textarea {
        cursor: text !important;
      }
    `;
    document.head.appendChild(style);
    
    // Force cursor style on body
    document.body.style.cursor = 'default';
    document.documentElement.style.cursor = 'default';
  }
  
  // Apply fixes when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', ensureCursorVisibility);
  } else {
    ensureCursorVisibility();
  }
  
  // Apply fixes when page is fully loaded
  window.addEventListener('load', ensureCursorVisibility);
  
  // Reapply fixes periodically (in case something overrides them)
  setInterval(ensureCursorVisibility, 5000);
  
  // Fix for PWA mode
  if (window.matchMedia('(display-mode: standalone)').matches) {
    console.log('PWA mode detected - applying cursor fixes');
    ensureCursorVisibility();
    
    // Additional fixes for PWA
    document.addEventListener('click', function(e) {
      // Ensure clicked elements have proper cursor
      if (e.target) {
        e.target.style.cursor = 'pointer';
      }
    });
  }
})();