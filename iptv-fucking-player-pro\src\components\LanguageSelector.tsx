import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiGlobe, FiCheck } from 'react-icons/fi';

interface LanguageSelectorProps {
  onLanguageSelect: (language: string) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ onLanguageSelect }) => {
  const [selectedLanguage, setSelectedLanguage] = useState('en-US');

  const languages = [
    {
      code: 'en-US',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸'
    },
    {
      code: 'pt-BR',
      name: 'Portuguese',
      nativeName: 'Português (Brasil)',
      flag: '🇧🇷'
    },
    {
      code: 'es-ES',
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸'
    }
  ];

  const handleContinue = () => {
    onLanguageSelect(selectedLanguage);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-sky-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-slate-800 rounded-2xl shadow-2xl p-8 w-full max-w-md border border-slate-700"
      >
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-sky-600 rounded-full mb-4"
          >
            <FiGlobe className="w-8 h-8 text-white" />
          </motion.div>
          
          <h1 className="text-2xl font-bold text-white mb-2">
            IPTV FUCKING PLAYER PRO
          </h1>
          
          <p className="text-slate-300">
            Choose your preferred language
          </p>
        </div>

        <div className="space-y-3 mb-8">
          {languages.map((language, index) => (
            <motion.button
              key={language.code}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              onClick={() => setSelectedLanguage(language.code)}
              className={`w-full p-4 rounded-lg border-2 transition-all duration-200 flex items-center justify-between ${
                selectedLanguage === language.code
                  ? 'border-sky-500 bg-sky-500/10'
                  : 'border-slate-600 hover:border-slate-500 bg-slate-700/50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{language.flag}</span>
                <div className="text-left">
                  <div className="text-white font-medium">{language.nativeName}</div>
                  <div className="text-slate-400 text-sm">{language.name}</div>
                </div>
              </div>
              
              {selectedLanguage === language.code && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="w-6 h-6 bg-sky-500 rounded-full flex items-center justify-center"
                >
                  <FiCheck className="w-4 h-4 text-white" />
                </motion.div>
              )}
            </motion.button>
          ))}
        </div>

        <motion.button
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          onClick={handleContinue}
          className="w-full bg-sky-600 hover:bg-sky-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          Continue
        </motion.button>
      </motion.div>
    </div>
  );
};

export default LanguageSelector;