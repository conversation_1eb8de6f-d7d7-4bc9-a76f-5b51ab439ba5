import React, { useState } from 'react';
import {
  Box,
  Paper,
  Ty<PERSON>graphy,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  CardActions,
  Grid
} from '@mui/material';
import { styled } from '@mui/material/styles';
import RefreshIcon from '@mui/icons-material/Refresh';
import SettingsBackupRestoreIcon from '@mui/icons-material/SettingsBackupRestore';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { useMessageTicker } from '../hooks/useMessageTicker';

const AdminContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(2),
  borderRadius: 12,
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95))'
    : 'linear-gradient(135deg, rgba(249, 250, 251, 0.95), rgba(243, 244, 246, 0.95))',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
}));

const StatusCard = styled(Card)(({ theme }) => ({
  background: theme.palette.mode === 'dark'
    ? 'rgba(14, 165, 233, 0.05)'
    : 'rgba(14, 165, 233, 0.05)',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.2)' : 'rgba(14, 165, 233, 0.15)'}`,
}));

interface TickerAdminProps {
  onClose?: () => void;
}

const TickerAdmin: React.FC<TickerAdminProps> = ({ onClose }) => {
  const [customUrl, setCustomUrl] = useState('');
  const [urlError, setUrlError] = useState('');
  
  const {
    config,
    loading,
    error,
    refresh,
    preload,
    resetToDefault,
    cacheInfo
  } = useMessageTicker({
    autoLoad: true,
    refreshInterval: 5 // Refresh a cada 5 minutos
  });

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const url = event.target.value;
    setCustomUrl(url);
    setUrlError('');

    // Validação básica de URL
    if (url && url.trim()) {
      try {
        new URL(url);
      } catch {
        setUrlError('URL inválida');
      }
    }
  };

  const handlePreload = async () => {
    if (customUrl && !urlError) {
      await preload();
    }
  };

  const handleRefresh = async () => {
    await refresh();
  };

  const handleReset = () => {
    if (confirm('Resetar para configuração padrão? Isso irá limpar o cache atual.')) {
      resetToDefault();
    }
  };

  const getStatusColor = (enabled: boolean) => {
    return enabled ? 'success' : 'error';
  };

  const getStatusIcon = (enabled: boolean) => {
    return enabled ? <CheckCircleIcon /> : <ErrorIcon />;
  };

  return (
    <AdminContainer>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600, flexGrow: 1 }}>
          Administração do Ticker
        </Typography>
        <Tooltip title="Atualizar configuração">
          <IconButton onClick={handleRefresh} disabled={loading}>
            {loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Erro: {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Status Atual */}
        <Grid item xs={12} md={6}>
          <StatusCard>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                Status Atual
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Ticker Ativo:</Typography>
                  <Chip
                    icon={getStatusIcon(config?.enabled || false)}
                    label={config?.enabled ? 'Ativo' : 'Inativo'}
                    color={getStatusColor(config?.enabled || false)}
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Mensagens:</Typography>
                  <Chip
                    label={config?.messages?.length || 0}
                    color="primary"
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Duração Padrão:</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {config?.defaultDuration || 15}s
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Pode Fechar:</Typography>
                  <Chip
                    label={config?.allowClose ? 'Sim' : 'Não'}
                    color={config?.allowClose ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
              </Box>
            </CardContent>
          </StatusCard>
        </Grid>

        {/* Informações do Cache */}
        <Grid item xs={12} md={6}>
          <StatusCard>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <CloudDownloadIcon sx={{ mr: 1 }} />
                Cache Local
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Cache Disponível:</Typography>
                  <Chip
                    label={cacheInfo.hasCache ? 'Sim' : 'Não'}
                    color={cacheInfo.hasCache ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Cache Válido:</Typography>
                  <Chip
                    label={cacheInfo.isValid ? 'Sim' : 'Não'}
                    color={cacheInfo.isValid ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Última Busca:</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {cacheInfo.lastFetch 
                      ? cacheInfo.lastFetch.toLocaleString()
                      : 'Nunca'
                    }
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </StatusCard>
        </Grid>

        {/* Configuração de URL */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                URL Personalizada
              </Typography>
              
              <TextField
                fullWidth
                label="URL do arquivo JSON"
                value={customUrl}
                onChange={handleUrlChange}
                error={!!urlError}
                helperText={urlError || 'URL do Pastebin Raw ou outro servidor'}
                placeholder="https://pastebin.com/raw/SEU_PASTE_ID"
                sx={{ mb: 2 }}
              />
              
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  onClick={handlePreload}
                  disabled={!customUrl || !!urlError || loading}
                  startIcon={<CloudDownloadIcon />}
                >
                  Testar URL
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={handleRefresh}
                  disabled={loading}
                  startIcon={<RefreshIcon />}
                >
                  Atualizar
                </Button>
                
                <Button
                  variant="outlined"
                  color="warning"
                  onClick={handleReset}
                  startIcon={<SettingsBackupRestoreIcon />}
                >
                  Resetar
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Mensagens Ativas */}
        {config?.messages && config.messages.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Mensagens Configuradas ({config.messages.filter(m => m.enabled !== false).length} ativas)
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {config.messages.map((message, index) => (
                    <Box
                      key={message.id}
                      sx={{
                        p: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1,
                        opacity: message.enabled === false ? 0.5 : 1,
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {message.id}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Chip
                            label={message.enabled === false ? 'Inativa' : 'Ativa'}
                            color={message.enabled === false ? 'default' : 'success'}
                            size="small"
                          />
                          <Chip
                            label={`${message.duration || config.defaultDuration}s`}
                            color="primary"
                            size="small"
                          />
                        </Box>
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary">
                        {message.text}
                        {message.link && (
                          <span style={{ color: '#0ea5e9', fontWeight: 600 }}>
                            {message.link.text}
                          </span>
                        )}
                        {message.suffix}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </AdminContainer>
  );
};

export default TickerAdmin;
