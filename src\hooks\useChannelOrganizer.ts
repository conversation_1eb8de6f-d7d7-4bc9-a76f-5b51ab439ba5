import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  organizeChannels,
  filterByCategory,
  searchGroups,
  getChannelStats,
  groupToChannel,
  type ChannelGroup,
  type ChannelVariant
} from '../utils/channelOrganizer';

interface UseChannelOrganizerOptions {
  enabled?: boolean;
  defaultQuality?: 'SD' | 'HD' | 'FHD' | '4K';
  preferAlternatives?: boolean;
}

interface UseChannelOrganizerReturn {
  // Dados organizados
  channelGroups: ChannelGroup[];
  filteredGroups: ChannelGroup[];
  compatibilityChannels: any[]; // Para compatibilidade com código existente
  
  // Estatísticas
  stats: {
    totalGroups: number;
    totalVariants: number;
    qualityDistribution: Record<string, number>;
    categoryDistribution: Record<string, number>;
    alternativesCount: number;
  };
  
  // Filtros e busca
  selectedCategory: string;
  searchTerm: string;
  setSelectedCategory: (category: string) => void;
  setSearchTerm: (term: string) => void;
  
  // Seleção de variantes
  selectedVariants: Record<string, ChannelVariant>;
  selectVariant: (groupId: string, variant: ChannelVariant) => void;
  getSelectedVariant: (groupId: string) => ChannelVariant | undefined;
  
  // Configurações
  defaultQuality: string;
  setDefaultQuality: (quality: 'SD' | 'HD' | 'FHD' | '4K') => void;
  
  // Utilitários
  isOrganized: boolean;
  toggleOrganization: () => void;
  resetFilters: () => void;
}

export function useChannelOrganizer(
  rawChannels: any[],
  options: UseChannelOrganizerOptions = {}
): UseChannelOrganizerReturn {
  const {
    enabled = true,
    defaultQuality = 'FHD',
    preferAlternatives = false
  } = options;

  // Estados
  const [isOrganized, setIsOrganized] = useState(enabled);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentDefaultQuality, setCurrentDefaultQuality] = useState(defaultQuality);
  const [selectedVariants, setSelectedVariants] = useState<Record<string, ChannelVariant>>({});

  // Organizar canais em grupos
  const channelGroups = useMemo(() => {
    if (!isOrganized || !rawChannels.length) return [];
    
    console.log('🔄 Organizando canais...', rawChannels.length);
    const groups = organizeChannels(rawChannels);
    console.log('✅ Canais organizados:', groups.length, 'grupos');
    
    return groups;
  }, [rawChannels, isOrganized]);

  // Aplicar filtros
  const filteredGroups = useMemo(() => {
    let filtered = channelGroups;
    
    // Filtrar por categoria
    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filterByCategory(filtered, selectedCategory);
    }
    
    // Filtrar por busca
    if (searchTerm.trim()) {
      filtered = searchGroups(filtered, searchTerm);
    }
    
    return filtered;
  }, [channelGroups, selectedCategory, searchTerm]);

  // Gerar canais para compatibilidade
  const compatibilityChannels = useMemo(() => {
    if (!isOrganized) return rawChannels;
    
    return filteredGroups.map(group => {
      const selectedVariant = selectedVariants[group.baseId];
      return groupToChannel(group, selectedVariant?.quality || currentDefaultQuality);
    });
  }, [filteredGroups, selectedVariants, currentDefaultQuality, isOrganized, rawChannels]);

  // Estatísticas
  const stats = useMemo(() => {
    return getChannelStats(channelGroups);
  }, [channelGroups]);

  // Selecionar variante automaticamente baseada na qualidade padrão
  useEffect(() => {
    if (!channelGroups.length) return;
    
    const newSelectedVariants: Record<string, ChannelVariant> = {};
    
    channelGroups.forEach(group => {
      // Se já tem uma variante selecionada, manter
      if (selectedVariants[group.baseId]) {
        newSelectedVariants[group.baseId] = selectedVariants[group.baseId];
        return;
      }
      
      // Tentar encontrar a qualidade padrão
      let preferredVariant = group.variants.find(v => 
        v.quality === currentDefaultQuality && !v.isAlternative
      );
      
      // Se não encontrou, tentar alternativa da mesma qualidade
      if (!preferredVariant && preferAlternatives) {
        preferredVariant = group.variants.find(v => 
          v.quality === currentDefaultQuality && v.isAlternative
        );
      }
      
      // Se ainda não encontrou, usar a melhor qualidade
      if (!preferredVariant) {
        preferredVariant = group.bestQuality;
      }
      
      newSelectedVariants[group.baseId] = preferredVariant;
    });
    
    setSelectedVariants(newSelectedVariants);
  }, [channelGroups, currentDefaultQuality, preferAlternatives]);

  // Funções
  const selectVariant = useCallback((groupId: string, variant: ChannelVariant) => {
    setSelectedVariants(prev => ({
      ...prev,
      [groupId]: variant
    }));
  }, []);

  const getSelectedVariant = useCallback((groupId: string) => {
    return selectedVariants[groupId];
  }, [selectedVariants]);

  const toggleOrganization = useCallback(() => {
    setIsOrganized(prev => !prev);
  }, []);

  const resetFilters = useCallback(() => {
    setSelectedCategory('all');
    setSearchTerm('');
  }, []);

  const setDefaultQuality = useCallback((quality: 'SD' | 'HD' | 'FHD' | '4K') => {
    setCurrentDefaultQuality(quality);
    
    // Atualizar variantes selecionadas para a nova qualidade padrão
    const newSelectedVariants: Record<string, ChannelVariant> = {};
    
    channelGroups.forEach(group => {
      let preferredVariant = group.variants.find(v => 
        v.quality === quality && !v.isAlternative
      );
      
      if (!preferredVariant && preferAlternatives) {
        preferredVariant = group.variants.find(v => 
          v.quality === quality && v.isAlternative
        );
      }
      
      if (!preferredVariant) {
        preferredVariant = group.bestQuality;
      }
      
      newSelectedVariants[group.baseId] = preferredVariant;
    });
    
    setSelectedVariants(newSelectedVariants);
  }, [channelGroups, preferAlternatives]);

  return {
    // Dados organizados
    channelGroups,
    filteredGroups,
    compatibilityChannels,
    
    // Estatísticas
    stats,
    
    // Filtros e busca
    selectedCategory,
    searchTerm,
    setSelectedCategory,
    setSearchTerm,
    
    // Seleção de variantes
    selectedVariants,
    selectVariant,
    getSelectedVariant,
    
    // Configurações
    defaultQuality: currentDefaultQuality,
    setDefaultQuality,
    
    // Utilitários
    isOrganized,
    toggleOrganization,
    resetFilters
  };
}
