// Main exports for Supabase integration
export { supabase, supabaseConfig, getSupabaseClient } from './client';

// MCP exports for admin operations
export { 
  supabaseMCP, 
  supabaseClient, 
  mcpOperations, 
  testMCPConnection,
  type MCPSupabaseOperations 
} from './mcp-client';

export type { 
  Database, 
  Tables, 
  TablesInsert, 
  TablesUpdate, 
  License, 
  LicenseInsert, 
  LicenseUpdate,
  ContentView,
  ContentViewInsert,
  ContentViewUpdate,
  Json 
} from './types';

// Re-export commonly used functions from supabase-js
export { createClient } from '@supabase/supabase-js';
