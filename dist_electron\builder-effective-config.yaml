directories:
  output: dist_electron
  buildResources: build
appId: com.iptv.player
productName: IPTV Player
files:
  - filter:
      - dist/**/*
      - electron/**/*
afterPack: C:\Users\<USER>\OneDrive\Desktop\pasta windows sandbox\project\scripts\after-pack.cjs
beforePack: C:\Users\<USER>\OneDrive\Desktop\pasta windows sandbox\project\scripts\before-pack.cjs
win:
  target:
    - nsis
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: IPTV Player
mac:
  target: dmg
  category: public.app-category.video
linux:
  target:
    - AppImage
    - deb
  category: Video
electronVersion: 28.3.3
