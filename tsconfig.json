{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@hooks/*": ["src/hooks/*"], "@contexts/*": ["src/contexts/*"], "@services/*": ["src/services/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"]}}, "include": ["src", ".eslintrc.cjs", "src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules", "build", "dist", "coverage", "**/*.spec.ts", "**/*.test.ts", "vite.config.ts"], "references": [{"path": "./tsconfig.node.json"}]}