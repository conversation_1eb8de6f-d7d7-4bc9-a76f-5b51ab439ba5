import { DatabaseService } from './database'
import { useConnectionStore, type Connection } from '../stores/connectionStore'

interface UserInfo {
  username: string
  password: string
  message: string
  auth: number
  status: string
  exp_date: string
  is_trial: string
  active_cons: string
  created_at: string
  max_connections: string
  allowed_output_formats: string[]
}

interface ServerInfo {
  url: string
  port: string
  https_port: string
  server_protocol: string
  rtmp_port: string
  timezone: string
  timestamp_now: number
  time_now: string
}

interface AuthResponse {
  user_info: UserInfo
  server_info: ServerInfo
}

interface Category {
  category_id: string
  category_name: string
  parent_id: number
}

interface Stream {
  num: number
  name: string
  stream_type: string
  stream_id: number
  stream_icon: string
  epg_channel_id: string
  added: string
  category_id: string
  custom_sid: string
  tv_archive: number
  direct_source: string
  tv_archive_duration: number
}

class IPTVService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  async initialize(): Promise<void> {
    await this.db.initialize()
  }

  private getCurrentConnection(): Connection | null {
    const store = useConnectionStore.getState()
    return store.getActiveConnection()
  }

  private async fetchWithRetry(url: string, maxRetries: number = 3): Promise<any> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 IPTV API attempt ${attempt}/${maxRetries} for: ${url}`)
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': '*/*',
            'User-Agent': 'IPTV Player'
          }
        })
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        console.log(`✅ IPTV API success on attempt ${attempt}`)
        return data
        
      } catch (error) {
        lastError = error as Error
        console.warn(`⚠️ IPTV API attempt ${attempt} failed:`, error)
        
        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000) // Max 5 seconds
          console.log(`⏳ Waiting ${delay}ms before retry...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    // All retries failed
    if (lastError) {
      console.error(`❌ All IPTV API attempts failed after ${maxRetries} tries`)
      throw lastError
    }
    
    throw new Error('Unknown error in fetchWithRetry')
  }

  async authenticate(url: string, username: string, password: string): Promise<AuthResponse> {
    const cleanUrl = url.replace(/\/$/, '')
    const authUrl = `${cleanUrl}/player_api.php?username=${username}&password=${password}`
    
    console.log('🔐 Authenticating with URL:', authUrl.replace(password, '***'))

    try {
      const response = await fetch(authUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'IPTV-Player/1.0',
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data: AuthResponse = await response.json()
      
      if (!data.user_info) {
        throw new Error('Invalid response format')
      }

      if (data.user_info.auth !== 1) {
        throw new Error('Authentication failed')
      }

      console.log('✅ Authentication successful')
      return data

    } catch (error) {
      console.error('❌ Authentication failed:', error)
      throw error
    }
  }

  async testConnection(url: string, username: string, password: string): Promise<boolean> {
    try {
      await this.authenticate(url, username, password)
      return true
    } catch (error) {
      return false
    }
  }

  private getApiUrl(action: string, params: Record<string, string> = {}): string {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    const { url, username, password } = connection
    const cleanUrl = url.replace(/\/$/, '')
    
    const queryParams = new URLSearchParams({
      username,
      password,
      action,
      ...params
    })

    return `${cleanUrl}/player_api.php?${queryParams.toString()}`
  }

  async getLiveCategories(): Promise<Category[]> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    try {
      const url = this.getApiUrl('get_live_categories')
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'IPTV-Player/1.0',
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const categories: Category[] = await response.json()
      
      // Cache categories with connection-specific key
      const cacheKey = `live_categories_${connection.id}`
      await this.db.setCache(cacheKey, categories, 24 * 60 * 60 * 1000) // 24h TTL
      
      return categories
    } catch (error) {
      console.error('Error fetching live categories:', error)
      
      // Try to get from cache
      const cacheKey = `live_categories_${connection.id}`
      const cached = await this.db.getCache<Category[]>(cacheKey)
      if (cached) {
        console.log('📦 Using cached live categories')
        return cached
      }
      
      throw error
    }
  }

  async getMovieCategories(): Promise<Category[]> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    // Try to get from cache first
    const cacheKey = `movie_categories_${connection.id}`
    const cached = await this.db.getCache<Category[]>(cacheKey)
    if (cached) {
      console.log('📦 Using cached movie categories')
      return cached
    }

    try {
      const url = this.getApiUrl('get_vod_categories')
      const response = await this.fetchWithRetry(url)
      
      const categories: Category[] = Array.isArray(response) ? response : []
      
      // Cache categories with connection-specific key
      await this.db.setCache(cacheKey, categories, 24 * 60 * 60 * 1000) // 24h TTL
      
      return categories
    } catch (error) {
      console.error('Error fetching movie categories:', error)
      
      // Return empty array if no cache available
      return []
    }
  }

  async getSeriesCategories(): Promise<Category[]> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    // Try to get from cache first
    const cacheKey = `series_categories_${connection.id}`
    const cached = await this.db.getCache<Category[]>(cacheKey)
    if (cached) {
      console.log('📦 Using cached series categories')
      return cached
    }

    try {
      const url = this.getApiUrl('get_series_categories')
      const response = await this.fetchWithRetry(url)
      
      const categories: Category[] = Array.isArray(response) ? response : []
      
      // Cache categories with connection-specific key
      await this.db.setCache(cacheKey, categories, 24 * 60 * 60 * 1000) // 24h TTL
      
      return categories
    } catch (error) {
      console.error('Error fetching series categories:', error)
      
      // Return empty array if no cache available
      return []
    }
  }

  async getLiveStreams(categoryId?: string): Promise<Stream[]> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    try {
      const params = categoryId ? { category_id: categoryId } : {}
      const url = this.getApiUrl('get_live_streams', params)
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'IPTV-Player/1.0',
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const streams: Stream[] = await response.json()
      
      // Cache streams with connection-specific key
      const cacheKey = `live_streams_${connection.id}_${categoryId || 'all'}`
      await this.db.setCache(cacheKey, streams, 6 * 60 * 60 * 1000) // 6h TTL
      
      return streams
    } catch (error) {
      console.error('Error fetching live streams:', error)
      
      // Try to get from cache
      const cacheKey = `live_streams_${connection.id}_${categoryId || 'all'}`
      const cached = await this.db.getCache<Stream[]>(cacheKey)
      if (cached) {
        console.log('📦 Using cached live streams')
        return cached
      }
      
      throw error
    }
  }

  async getMovieStreams(categoryId?: string): Promise<Stream[]> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    // Try to get from cache first
    const cacheKey = `movie_streams_${connection.id}_${categoryId || 'all'}`
    const cached = await this.db.getCache<Stream[]>(cacheKey)
    if (cached) {
      console.log('📦 Using cached movie streams')
      return cached
    }

    try {
      const params = categoryId ? { category_id: categoryId } : {}
      const url = this.getApiUrl('get_vod_streams', params)
      const response = await this.fetchWithRetry(url)
      
      const streams: Stream[] = Array.isArray(response) ? response : []
      
      // Cache streams with connection-specific key
      await this.db.setCache(cacheKey, streams, 6 * 60 * 60 * 1000) // 6h TTL
      
      return streams
    } catch (error) {
      console.error('Error fetching movie streams:', error)
      
      // Return empty array if no cache available
      return []
    }
  }

  async getSeriesStreams(categoryId?: string): Promise<Stream[]> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    try {
      const params = categoryId ? { category_id: categoryId } : {}
      const url = this.getApiUrl('get_series', params)
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'IPTV-Player/1.0',
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const streams: Stream[] = await response.json()
      
      // Cache streams with connection-specific key
      const cacheKey = `series_streams_${connection.id}_${categoryId || 'all'}`
      await this.db.setCache(cacheKey, streams, 6 * 60 * 60 * 1000) // 6h TTL
      
      return streams
    } catch (error) {
      console.error('Error fetching series streams:', error)
      
      // Try to get from cache
      const cacheKey = `series_streams_${connection.id}_${categoryId || 'all'}`
      const cached = await this.db.getCache<Stream[]>(cacheKey)
      if (cached) {
        console.log('📦 Using cached series streams')
        return cached
      }
      
      throw error
    }
  }

  getStreamUrl(streamId: number, type: 'live' | 'movie' | 'series'): string {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    const { url, username, password } = connection
    const cleanUrl = url.replace(/\/$/, '')

    switch (type) {
      case 'live':
        return `${cleanUrl}/live/${username}/${password}/${streamId}.m3u8`
      case 'movie':
        return `${cleanUrl}/movie/${username}/${password}/${streamId}.mp4`
      case 'series':
        return `${cleanUrl}/series/${username}/${password}/${streamId}.mp4`
      default:
        throw new Error(`Unknown stream type: ${type}`)
    }
  }
}

export default IPTVService