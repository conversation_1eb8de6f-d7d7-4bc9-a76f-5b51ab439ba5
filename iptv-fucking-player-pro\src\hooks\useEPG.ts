import { useState, useEffect, useCallback } from 'react'
import EPGService, { EPGProgram, EPGChannel } from '../services/epgService'
import { useConnectionStore } from '../stores/connectionStore'

interface UseEPGReturn {
  // EPG data
  getCurrentProgram: (channelId: string) => EPGProgram | null
  getNextProgram: (channelId: string) => EPGProgram | null
  getAllPrograms: (channelId: string) => EPGProgram[]
  getChannelEPG: (channelId: string) => EPGChannel | null
  
  // Loading state
  isLoadingEPG: boolean
  epgError: string | null
  
  // Actions
  loadEPG: () => Promise<void>
  refreshEPG: () => Promise<void>
  
  // Utilities
  formatTime: (date: Date) => string
  formatDuration: (startTime: Date, endTime: Date) => string
}

const useEPG = (): UseEPGReturn => {
  const [epgService] = useState(() => new EPGService())
  const [isLoadingEPG, setIsLoadingEPG] = useState(false)
  const [epgError, setEpgError] = useState<string | null>(null)
  
  const { getActiveConnection } = useConnectionStore()
  const activeConnection = getActiveConnection()

  // Initialize EPG service
  useEffect(() => {
    const initializeEPG = async () => {
      try {
        await epgService.initialize()
        console.log('✅ EPG service initialized')
      } catch (error) {
        console.error('❌ Error initializing EPG service:', error)
        setEpgError('Failed to initialize EPG service')
      }
    }

    initializeEPG()
  }, [epgService])

  const loadEPG = useCallback(async () => {
    if (!activeConnection) {
      console.log('❌ No active connection for EPG loading')
      return
    }

    console.log('🔄 Starting EPG load for:', activeConnection.name)
    setIsLoadingEPG(true)
    setEpgError(null)

    try {
      await epgService.loadEPG()
      console.log('✅ EPG load completed successfully')
    } catch (error) {
      console.error('❌ Error loading EPG:', error)
      setEpgError(error instanceof Error ? error.message : 'Failed to load EPG')
    } finally {
      setIsLoadingEPG(false)
    }
  }, [epgService, activeConnection])

  const refreshEPG = useCallback(async () => {
    // Force refresh by clearing cache
    await loadEPG()
  }, [loadEPG])

  // Load EPG when connection changes
  useEffect(() => {
    if (activeConnection) {
      const cacheStatus = epgService.getCacheStatus()
      console.log(`🔄 EPG: Connection changed: ${activeConnection.name} (Cache: ${cacheStatus.channelCount} channels, ${cacheStatus.age}min old)`)
      
      // Only load if cache is invalid
      if (!cacheStatus.isValid) {
        console.log('📺 EPG: Cache invalid, loading fresh EPG...')
        loadEPG()
      } else {
        console.log('✅ EPG: Using valid cached EPG data')
      }
    }
  }, [activeConnection?.id, loadEPG])

  // Smart progress updates - less frequent for better performance
  useEffect(() => {
    const interval = setInterval(() => {
      epgService.updateProgress()
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [epgService])

  // Auto-refresh EPG when cache expires
  useEffect(() => {
    const checkInterval = setInterval(() => {
      if (activeConnection && epgService.needsRefresh()) {
        console.log('🔄 EPG: Auto-refreshing expired EPG cache')
        loadEPG()
      }
    }, 5 * 60 * 1000) // Check every 5 minutes

    return () => clearInterval(checkInterval)
  }, [activeConnection, loadEPG])

  const getCurrentProgram = useCallback((channelId: string): EPGProgram | null => {
    return epgService.getCurrentProgram(channelId)
  }, [epgService])

  const getNextProgram = useCallback((channelId: string): EPGProgram | null => {
    return epgService.getNextProgram(channelId)
  }, [epgService])

  const getAllPrograms = useCallback((channelId: string): EPGProgram[] => {
    return epgService.getAllPrograms(channelId)
  }, [epgService])

  const getChannelEPG = useCallback((channelId: string): EPGChannel | null => {
    return epgService.getChannelEPG(channelId)
  }, [epgService])

  const formatTime = useCallback((date: Date): string => {
    return epgService.formatTime(date)
  }, [epgService])

  const formatDuration = useCallback((startTime: Date, endTime: Date): string => {
    return epgService.formatDuration(startTime, endTime)
  }, [epgService])

  return {
    // EPG data
    getCurrentProgram,
    getNextProgram,
    getAllPrograms,
    getChannelEPG,
    
    // Loading state
    isLoadingEPG,
    epgError,
    
    // Actions
    loadEPG,
    refreshEPG,
    
    // Utilities
    formatTime,
    formatDuration
  }
}

export default useEPG