import React, { useState, useEffect, useCallback, useRef } from 'react';
import { IPTVService } from '../services/iptvService';
import MediaGrid from './MediaGrid';
import { Category, EPGInfo } from '../services/iptvService';
import styled from 'styled-components';

interface MediaBrowserProps {
  type: 'channel' | 'movie' | 'series';
  service: IPTVService;
}

const ITEMS_PER_PAGE = 24;

const StyledMediaBrowser = styled.div`
  min-height: 100vh;
  background: #141414;
  color: #fff;

  .categories {
    padding: 20px;
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #666 #141414;
    -webkit-overflow-scrolling: touch;
  }

  .categories::-webkit-scrollbar {
    height: 6px;
  }

  .categories::-webkit-scrollbar-track {
    background: #141414;
  }

  .categories::-webkit-scrollbar-thumb {
    background-color: #666;
    border-radius: 6px;
  }

  .category-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 0.875rem;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    &.active {
      background: #007bff;
    }
  }

  .error-message {
    margin: 20px;
    padding: 16px;
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid rgba(255, 59, 48, 0.2);
    border-radius: 8px;
    color: #ff3b30;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    button {
      padding: 8px 16px;
      background: #ff3b30;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;

      &:hover {
        background: #ff2d20;
      }
    }
  }

  @media (max-width: 768px) {
    .categories {
      padding: 12px;
    }

    .category-btn {
      padding: 6px 12px;
      font-size: 0.8125rem;
    }

    .error-message {
      margin: 12px;
      padding: 12px;
      font-size: 0.875rem;
    }
  }
`;

const MediaBrowser: React.FC<MediaBrowserProps> = ({ type, service }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [items, setItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [epgData, setEpgData] = useState<Record<string, EPGInfo[]>>({});
  const requestTimeoutRef = useRef<NodeJS.Timeout>();
  const cachedDataRef = useRef<{[key: string]: any[]}>({});

  const throttledRequest = useCallback((requestFn: () => Promise<any>) => {
    return new Promise((resolve, reject) => {
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }

      requestTimeoutRef.current = setTimeout(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, 1000); // 1 second delay between requests
    });
  }, []);

  const loadCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      let cats: Category[] = [];
      
      const cacheKey = `categories_${type}`;
      if (cachedDataRef.current[cacheKey]) {
        cats = cachedDataRef.current[cacheKey] as Category[];
      } else {
        switch (type) {
          case 'channel':
            cats = await service.getLiveCategories();
            break;
          case 'movie':
            cats = await service.getMovieCategories();
            break;
          case 'series':
            cats = await service.getSeriesCategories();
            break;
        }
        cachedDataRef.current[cacheKey] = cats;
      }
      
      setCategories(cats);
    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Failed to load categories. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [type, service, throttledRequest]);

  const loadItems = useCallback(async () => {
    if (!hasMore || loading) return;

    try {
      setLoading(true);
      setError(null);
      let newItems: any[] = [];
      
      const cacheKey = `items_${type}_${selectedCategory}_${page}`;
      if (cachedDataRef.current[cacheKey]) {
        newItems = cachedDataRef.current[cacheKey] as any[];
      } else {
        switch (type) {
          case 'channel':
            newItems = await service.getLiveStreams(selectedCategory);
            break;
          case 'movie':
            newItems = await service.getMovieStreams(selectedCategory);
            break;
          case 'series':
            newItems = await service.getSeriesStreams(selectedCategory);
            break;
        }
        cachedDataRef.current[cacheKey] = newItems;
      }

      const startIndex = (page - 1) * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const pageItems = newItems.slice(startIndex, endIndex);

      setItems(prev => page === 1 ? pageItems : [...prev, ...pageItems]);
      setHasMore(endIndex < newItems.length);

      if (type === 'channel') {
        pageItems.forEach(async (item) => {
          try {
            const epg = await service.getEPG(item.stream_id);
            setEpgData(prev => ({
              ...prev,
              [item.stream_id]: epg
            }));
          } catch (error) {
            console.error('Error loading EPG:', error);
          }
        });
      }
    } catch (error) {
      console.error(`Error loading ${type}s:`, error);
      setError(`Failed to load ${type}s. Please try again later.`);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [type, service, selectedCategory, page, hasMore, loading, throttledRequest]);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  useEffect(() => {
    setPage(1);
    setItems([]);
    setHasMore(true);
    loadItems();
  }, [selectedCategory, loadItems]);

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
  };

  const formatItems = useCallback((rawItems: any[]) => {
    return rawItems.map(item => ({
      id: item.stream_id,
      title: item.name || item.title,
      type,
      thumbnail: item.stream_icon || item.movie_image || item.cover,
      logo: item.tv_logo,
      epg: type === 'channel' && epgData[item.stream_id] ? {
        current: epgData[item.stream_id].find(p => p.now_playing)?.title,
        next: epgData[item.stream_id].find(p => !p.now_playing)?.title
      } : undefined,
      progress: item.last_watched_position ? (item.last_watched_position / item.duration) * 100 : undefined
    }));
  }, [type, epgData]);

  return (
    <StyledMediaBrowser>
      <div className="categories">
        <button
          className={`category-btn ${selectedCategory === 'all' ? 'active' : ''}`}
          onClick={() => setSelectedCategory('all')}
        >
          All
        </button>
        {categories.map((cat) => (
          <button
            key={cat.id}
            className={`category-btn ${selectedCategory === cat.id ? 'active' : ''}`}
            onClick={() => setSelectedCategory(cat.id)}
          >
            {cat.name}
          </button>
        ))}
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => {
            setError(null);
            loadItems();
          }}>
            Try Again
          </button>
        </div>
      )}

      <MediaGrid
        items={formatItems(items)}
        type={type}
        loading={loading}
        onLoadMore={handleLoadMore}
        hasMore={hasMore}
      />
    </StyledMediaBrowser>
  );
};

export default MediaBrowser;