import React, { useState, useEffect } from 'react'
import { XMarkIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/solid'

interface Connection {
  id?: string
  url: string
  username: string
  password: string
  name: string
}

interface AuthModalProps {
  isOpen: boolean
  onClose?: () => void
  onSuccess: (connection: Connection) => void
  editConnection?: Connection
  isRequired?: boolean
}

interface FormData {
  name: string
  url: string
  username: string
  password: string
}

interface FormErrors {
  name?: string
  url?: string
  username?: string
  password?: string
  general?: string
}

type ModalStep = 'credentials' | 'testing' | 'success' | 'error'

const AuthModal: React.FC<AuthModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  editConnection,
  isRequired = false
}) => {
  const [step, setStep] = useState<ModalStep>('credentials')
  const [formData, setFormData] = useState<FormData>({
    name: '',
    url: '',
    username: '',
    password: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Initialize form data when editing
  useEffect(() => {
    if (editConnection) {
      setFormData({
        name: editConnection.name,
        url: editConnection.url,
        username: editConnection.username,
        password: editConnection.password
      })
    } else {
      setFormData({
        name: '',
        url: '',
        username: '',
        password: ''
      })
    }
    setStep('credentials')
    setErrors({})
  }, [editConnection, isOpen])

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Nome da conexão é obrigatório'
    }

    if (!formData.url.trim()) {
      newErrors.url = 'URL do servidor é obrigatória'
    } else {
      try {
        new URL(formData.url)
      } catch {
        newErrors.url = 'URL inválida'
      }
    }

    if (!formData.username.trim()) {
      newErrors.username = 'Nome de usuário é obrigatório'
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Senha é obrigatória'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const testConnection = async (): Promise<boolean> => {
    try {
      const cleanUrl = formData.url.replace(/\/$/, '')
      const response = await fetch(
        `${cleanUrl}/player_api.php?username=${formData.username}&password=${formData.password}`,
        {
          method: 'GET',
          headers: {
            'User-Agent': 'IPTV-Player/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Cache-Control': 'no-cache'
          }
        }
      )
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      
      if (data.user_info && data.user_info.status === 'Active') {
        return true
      } else {
        throw new Error('Credenciais inválidas ou conta inativa')
      }
    } catch (error) {
      console.error('Connection test failed:', error)
      throw error
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setStep('testing')
    setErrors({})

    try {
      await testConnection()
      setStep('success')
      
      // Wait a moment to show success state
      setTimeout(() => {
        const connection: Connection = {
          id: editConnection?.id,
          ...formData
        }
        onSuccess(connection)
        handleClose()
      }, 1500)
      
    } catch (error) {
      setStep('error')
      setErrors({
        general: error instanceof Error ? error.message : 'Erro ao testar conexão'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (isRequired && step !== 'success') {
      return // Don't allow closing if required and not successful
    }
    
    if (onClose) {
      onClose()
    }
    
    // Reset form after a delay
    setTimeout(() => {
      setStep('credentials')
      setErrors({})
      if (!editConnection) {
        setFormData({ name: '', url: '', username: '', password: '' })
      }
    }, 300)
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="relative w-full max-w-md mx-4 bg-slate-800/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-700/50 overflow-hidden">
        {/* Header */}
        <div className="relative px-6 py-4 bg-gradient-to-r from-sky-500/10 to-blue-500/10 border-b border-slate-700/50">
          <h2 className="text-xl font-semibold text-white">
            {editConnection ? 'Editar Conexão IPTV' : 'Nova Conexão IPTV'}
          </h2>
          {!isRequired && (
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 p-1 text-slate-400 hover:text-white transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-3 bg-slate-900/50">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              step === 'credentials' ? 'bg-sky-500' : 
              step === 'testing' ? 'bg-yellow-500' :
              step === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`} />
            <span className="text-sm text-slate-300">
              {step === 'credentials' && 'Inserir credenciais'}
              {step === 'testing' && 'Testando conexão...'}
              {step === 'success' && 'Conexão bem-sucedida!'}
              {step === 'error' && 'Erro na conexão'}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'credentials' && (
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Connection Name */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Nome da Conexão
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 transition-colors ${
                    errors.name ? 'border-red-500' : 'border-slate-600'
                  }`}
                  placeholder="Ex: Minha Lista IPTV"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                )}
              </div>

              {/* Server URL */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  URL do Servidor
                </label>
                <input
                  type="url"
                  value={formData.url}
                  onChange={(e) => handleInputChange('url', e.target.value)}
                  className={`w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 transition-colors ${
                    errors.url ? 'border-red-500' : 'border-slate-600'
                  }`}
                  placeholder="http://exemplo.com:8080"
                />
                {errors.url && (
                  <p className="mt-1 text-sm text-red-400">{errors.url}</p>
                )}
              </div>

              {/* Username */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Nome de Usuário
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className={`w-full px-3 py-2 bg-slate-700/50 border rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 transition-colors ${
                    errors.username ? 'border-red-500' : 'border-slate-600'
                  }`}
                  placeholder="Seu nome de usuário"
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-red-400">{errors.username}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Senha
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className={`w-full px-3 py-2 pr-10 bg-slate-700/50 border rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 transition-colors ${
                      errors.password ? 'border-red-500' : 'border-slate-600'
                    }`}
                    placeholder="Sua senha"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="w-4 h-4" />
                    ) : (
                      <EyeIcon className="w-4 h-4" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400">{errors.password}</p>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 bg-gradient-to-r from-sky-500 to-blue-600 text-white font-medium rounded-lg hover:from-sky-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-slate-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {editConnection ? 'Atualizar Conexão' : 'Conectar'}
              </button>
            </form>
          )}

          {step === 'testing' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 border-4 border-yellow-500 border-t-transparent rounded-full animate-spin" />
              <h3 className="text-lg font-medium text-white mb-2">
                Testando Conexão
              </h3>
              <p className="text-slate-400">
                Verificando suas credenciais...
              </p>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center py-8">
              <CheckCircleIcon className="w-16 h-16 mx-auto mb-4 text-green-500" />
              <h3 className="text-lg font-medium text-white mb-2">
                Conexão Bem-sucedida!
              </h3>
              <p className="text-slate-400">
                Suas credenciais foram validadas com sucesso.
              </p>
            </div>
          )}

          {step === 'error' && (
            <div className="text-center py-8">
              <ExclamationTriangleIcon className="w-16 h-16 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-medium text-white mb-2">
                Erro na Conexão
              </h3>
              <p className="text-slate-400 mb-4">
                {errors.general || 'Não foi possível conectar ao servidor'}
              </p>
              <button
                onClick={() => setStep('credentials')}
                className="px-4 py-2 bg-slate-700 text-white rounded-lg hover:bg-slate-600 transition-colors"
              >
                Tentar Novamente
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AuthModal