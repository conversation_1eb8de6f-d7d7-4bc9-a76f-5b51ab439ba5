{"name": "iptv-fucking-player-pro", "private": true, "version": "1.0.0", "description": "IPTV FUCKING PLAYER PRO - Modern IPTV Player", "author": {"name": "IPTV Team", "email": "<EMAIL>"}, "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "vite build", "build:electron": "vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "electron": "wait-on tcp:4000 --timeout 30000 && cross-env NODE_ENV=development electron .", "electron:dev": "concurrently -k \"npm run dev\" \"npm run electron\"", "electron:build": "npm run build:electron && electron-builder", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@heroicons/react": "^2.2.0", "axios": "^1.8.4", "framer-motion": "^12.23.9", "hls.js": "^1.5.20", "i18next": "^23.8.2", "i18next-browser-languagedetector": "^7.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.5", "react-icons": "^5.4.0", "react-router-dom": "^6.29.0", "react-toastify": "^11.0.5", "rxjs": "^7.8.2", "sql.js": "^1.10.2", "video.js": "^8.21.0", "zustand": "^4.5.7"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/sql.js": "^1.4.9", "@types/video.js": "^7.3.58", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "@vitest/coverage-v8": "^0.33.0", "@vitest/ui": "^0.33.0", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.6.4", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.33.0", "wait-on": "^7.2.0"}, "build": {"appId": "com.iptv.fucking.player.pro", "productName": "IPTV FUCKING PLAYER PRO", "directories": {"output": "dist_electron"}, "files": ["dist/**/*", "electron/**/*"], "win": {"target": ["nsis"]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "IPTV FUCKING PLAYER PRO"}, "mac": {"target": "dmg", "category": "public.app-category.video"}, "linux": {"target": ["AppImage", "deb"], "category": "Video"}}}