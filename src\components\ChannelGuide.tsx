import React from 'react';
import { Box, Card, CardContent, Typography, Grid } from '@mui/material';
import { Stream, EPGChannel } from '../services/iptvService';

interface ChannelGuideProps {
  channels: Stream[];
  epgData: EPGChannel[];
}

const ChannelGuide: React.FC<ChannelGuideProps> = ({ channels, epgData }) => {
  const findChannelEPG = (channelName: string) => {
    return epgData.find(epg => 
      epg.name.toLowerCase() === channelName.toLowerCase()
    );
  };

  return (
    <Grid container spacing={2}>
      {channels.map((channel) => {
        const epg = findChannelEPG(channel.name);
        const currentProgram = epg?.programs[0];

        return (
          <Grid item xs={12} key={channel.id}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  {channel.thumbnail && (
                    <img 
                      src={channel.thumbnail} 
                      alt={channel.name}
                      style={{ width: 48, height: 48, objectFit: 'cover' }}
                    />
                  )}
                  <Box flex={1}>
                    <Typography variant="h6">{channel.name}</Typography>
                    {currentProgram && (
                      <Box>
                        <Typography variant="subtitle1" color="primary">
                          {currentProgram.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {currentProgram.startTime} - {currentProgram.endTime}
                        </Typography>
                        {currentProgram.description && (
                          <Typography variant="body2">
                            {currentProgram.description}
                          </Typography>
                        )}
                      </Box>
                    )}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        );
      })}
    </Grid>
  );
};

export default ChannelGuide; 