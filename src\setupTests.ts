import '@testing-library/jest-dom';
import { expect, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { cleanup } from '@testing-library/react';
import matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect method with methods from react-testing-library
expect.extend(matchers);

// Runs a cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Mock IndexedDB
const indexedDB = {
  open: vi.fn(),
  deleteDatabase: vi.fn(),
};

const IDBRequest = {
  result: null,
  error: null,
  source: null,
  transaction: null,
  readyState: 'pending',
  onerror: null,
  onsuccess: null,
  onupgradeneeded: null,
};

const IDBDatabase = {
  close: vi.fn(),
  createObjectStore: vi.fn(),
  deleteObjectStore: vi.fn(),
  transaction: vi.fn(),
  objectStoreNames: [],
  name: 'testDB',
  version: 1,
  onabort: null,
  onclose: null,
  onerror: null,
  onversionchange: null,
};

Object.defineProperty(window, 'indexedDB', {
  value: indexedDB,
});

Object.defineProperty(window, 'IDBRequest', {
  value: IDBRequest,
});

Object.defineProperty(window, 'IDBDatabase', {
  value: IDBDatabase,
});

// Mock video players
vi.mock('hls.js', () => ({
  default: {
    isSupported: vi.fn(() => true),
    Events: {},
  },
}));

vi.mock('video.js', () => ({
  default: vi.fn(),
}));

vi.mock('plyr', () => ({
  default: vi.fn(),
}));

// Mock ResizeObserver
class ResizeObserver {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
}

window.ResizeObserver = ResizeObserver;

// Mock IntersectionObserver
class IntersectionObserver {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
  root = null;
  rootMargin = '';
  thresholds = [0];
  takeRecords = vi.fn().mockReturnValue([]);
}

window.IntersectionObserver = IntersectionObserver as any;

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock console methods
const originalConsole = { ...console };
beforeAll(() => {
  console.log = vi.fn();
  console.error = vi.fn();
  console.warn = vi.fn();
  console.info = vi.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
  console.info = originalConsole.info;
});
