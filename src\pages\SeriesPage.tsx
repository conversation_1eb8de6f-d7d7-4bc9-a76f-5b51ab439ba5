import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  CircularProgress, 
  Grid, 
  Paper,
  Tabs,
  Tab,
  alpha,
  TextField,
  InputAdornment,
  useMediaQuery,
  Drawer,
  IconButton,
  Chip,
  Divider,
  useTheme,
  Button,
  Modal,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Fade,
  Rating,
  DialogTitle,
  DialogContent,
  DialogActions,
  Dialog,
  Card,
  CardMedia,
  CardContent,
  Tooltip,
  Slider,
  Stack
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { createIPTVService, IPTVService, Category, Stream, Season, Episode } from '../services/iptvService';
import { dbService } from '../services/dbService';
import MovieCard from '../components/MovieCard/MovieCard';
import ContinueWatchingSeries from '../components/ContinueWatchingSeries/ContinueWatchingSeries';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import TuneIcon from '@mui/icons-material/Tune';
import CloseIcon from '@mui/icons-material/Close';
import AppsIcon from '@mui/icons-material/Apps';
import ViewListIcon from '@mui/icons-material/ViewList';
import MovieIcon from '@mui/icons-material/Movie';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SortIcon from '@mui/icons-material/Sort';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { motion, AnimatePresence } from 'framer-motion';
import StarIcon from '@mui/icons-material/Star';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import TheatersIcon from '@mui/icons-material/Theaters';
import TvIcon from '@mui/icons-material/Tv';
import { tmdbService } from '../services/tmdbService';
import VideoPlayer from '../components/VideoPlayer/VideoPlayer';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import SearchBar from '../components/SearchBar';
import { Searchable } from '../services/searchService';
import SettingsIcon from '@mui/icons-material/Settings';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import BackgroundEffects from '../components/BackgroundEffects';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import VideocamOffIcon from '@mui/icons-material/VideocamOff';
import FavoriteIcon from '@mui/icons-material/Favorite';
import MovieFilterIcon from '@mui/icons-material/MovieFilter';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { keyframes } from '@emotion/react';
import EnhancedSeriesModal from '../components/EnhancedSeriesModal';

const MotionGrid = motion.create(Grid);
const MotionBox = motion.create(Box);
const MotionListItem = motion.create(ListItem);

interface ExtendedStream extends Stream {
  progress?: number;
  rating?: number;
  year?: string;
}

// Adicionando keyframes para animações
const pulseAnimation = keyframes`
  0% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(14, 165, 233, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0);
  }
`;

const slideRightAnimation = keyframes`
  0%, 100% { 
    transform: translateX(-3px) rotate(45deg);
  }
  50% { 
    transform: translateX(3px) rotate(45deg);
  }
`;



const SeriesPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [series, setSeries] = useState<ExtendedStream[]>([]);
  const [allSeries, setAllSeries] = useState<ExtendedStream[]>([]);
  const [iptvService, setIptvService] = useState<IPTVService | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [globalSearch, setGlobalSearch] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [searching, setSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<ExtendedStream[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const seriesPerPage = 20;
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const lastCategoryRef = useRef<string | null>(null);
  const [sortBy, setSortBy] = useState<'alpha' | 'recent' | 'rating'>('alpha');
  const [selectedSeries, setSelectedSeries] = useState<ExtendedStream | null>(null);
  const [seriesDetailsOpen, setSeriesDetailsOpen] = useState(false);
  const [seriesDetails, setSeriesDetails] = useState<any>(null);
  const [seriesLoading, setSeriesLoading] = useState(false);
  const [selectedSeason, setSelectedSeason] = useState<Season | null>(null);
  const [seasons, setSeasons] = useState<Season[]>([]);
  const [episodes, setEpisodes] = useState<Episode[]>([]);
  const [selectedEpisode, setSelectedEpisode] = useState<Episode | null>(null);
  const [playerOpen, setPlayerOpen] = useState(false);
  const [streamUrl, setStreamUrl] = useState<string>('');
  const playerRef = useRef<any>(null);
  const lastSaveTimeRef = useRef<number>(0);
  const SAVE_INTERVAL = 15; // Save every 15 seconds
  const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  const [skipCreditsConfig, setSkipCreditsConfig] = useState<Record<string, number>>({});
  const [autoPlayNextEpisode, setAutoPlayNextEpisode] = useState(true);
  const [endCreditsTime, setEndCreditsTime] = useState(120); // Tempo em segundos
  const nextEpisodeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Carregar as configurações de pular créditos do localStorage ao iniciar
  useEffect(() => {
    try {
      const savedSkipConfig = localStorage.getItem('skipCreditsConfig');
      if (savedSkipConfig) {
        setSkipCreditsConfig(JSON.parse(savedSkipConfig));
      }
      
      const savedAutoPlay = localStorage.getItem('autoPlayNextEpisode');
      if (savedAutoPlay !== null) {
        setAutoPlayNextEpisode(savedAutoPlay === 'true');
      }
    } catch (error) {
      console.error('Erro ao carregar configurações de pular créditos:', error);
    }
  }, []);

  // Salvar a configuração de pular créditos sempre que ela mudar
  useEffect(() => {
    try {
      localStorage.setItem('skipCreditsConfig', JSON.stringify(skipCreditsConfig));
    } catch (error) {
      console.error('Erro ao salvar configurações de pular créditos:', error);
    }
  }, [skipCreditsConfig]);
  
  // Salvar a configuração de auto play
  useEffect(() => {
    try {
      localStorage.setItem('autoPlayNextEpisode', String(autoPlayNextEpisode));
    } catch (error) {
      console.error('Erro ao salvar configuração de reprodução automática:', error);
    }
  }, [autoPlayNextEpisode]);

  useEffect(() => {
    const initService = async () => {
      try {
        // Clean up continue watching list on app start
        await cleanupContinueWatching();
        
        const service = await createIPTVService();
        setIptvService(service);
        const allCats = await service.getSeriesCategories();
        setCategories(allCats);
        if (allCats.length > 0) {
          const firstCategoryId = allCats[0].id;
          setSelectedCategoryId(firstCategoryId);
          lastCategoryRef.current = firstCategoryId;
          await loadSeries(service, firstCategoryId);
        }
      } catch (err) {
        console.error('Error initializing IPTV service:', err);
        setError('Erro ao inicializar o serviço IPTV');
      }
    };
    
    initService();
  }, []);

  useEffect(() => {
    if (iptvService) {
      loadCategories();
    }
  }, [iptvService]);

  useEffect(() => {
    if (iptvService && selectedCategoryId && selectedCategoryId !== lastCategoryRef.current) {
      lastCategoryRef.current = selectedCategoryId;
      loadSeries(iptvService, selectedCategoryId);
    }
  }, [iptvService, selectedCategoryId]);

  const loadCategories = async () => {
    if (!iptvService) return;
    
    try {
      setLoading(true);
      const categoriesData = await iptvService.getSeriesCategories();
      
      if (categoriesData.length > 0) {
        setCategories(categoriesData);
        if (!selectedCategoryId) {
          setSelectedCategoryId(categoriesData[0].id);
        }
      } else {
        setError('Nenhuma categoria encontrada');
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Falha ao carregar categorias');
    } finally {
      setLoading(false);
    }
  };

  const loadAllSeries = async () => {
    if (!iptvService) {
      console.error('SeriesPage: Não é possível carregar séries - iptvService não inicializado');
      return;
    }
    
    console.log('SeriesPage: Iniciando carregamento de todas as séries das categorias...');
    setLoading(true);
    
    try {
      // Certificar que temos as categorias
      if (categories.length === 0) {
        console.log('SeriesPage: Não há categorias carregadas, carregando primeiro...');
        const categoriesData = await iptvService.getSeriesCategories();
        setCategories(categoriesData);
        console.log(`SeriesPage: Categorias carregadas: ${categoriesData.length}`);
      }
      
      console.log(`SeriesPage: Carregando séries de ${categories.length} categorias...`);
      
      const allSeriesData: ExtendedStream[] = [];
      const promises = categories.map(cat => iptvService.getSeriesStreams(cat.id));
      
      const results = await Promise.all(promises);
      
      // Adicionar resultados por categoria com diagnósticos
      let totalSeries = 0;
      results.forEach((seriesInCategory, index) => {
        const categoryName = categories[index]?.name || 'Desconhecida';
        console.log(`SeriesPage: Categoria ${categoryName}: ${seriesInCategory.length} séries`);
        allSeriesData.push(...seriesInCategory);
        totalSeries += seriesInCategory.length;
      });
      
      // Remover duplicatas caso haja
      const uniqueSeriesMap = new Map();
      allSeriesData.forEach(series => {
        uniqueSeriesMap.set(series.id, series);
      });
      
      const uniqueSeriesArray = Array.from(uniqueSeriesMap.values());
      console.log(`SeriesPage: Carregamento completo - ${totalSeries} séries carregadas (${uniqueSeriesArray.length} únicas)`);
      
      setAllSeries(uniqueSeriesArray);
    } catch (error) {
      console.error('SeriesPage: Erro ao carregar todas as séries:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSeries = async (service: IPTVService, categoryId: string) => {
    try {
      setLoading(true);
      const seriesData = await service.getSeriesStreams(categoryId);
      setSeries(seriesData);
      setLoading(false);
    } catch (error) {
      console.error('Error loading series:', error);
      setError('Erro ao carregar séries');
      setLoading(false);
    }
  };

  const handleSearch = useCallback((results: Searchable[], query: string) => {
    console.log(`SeriesPage: Recebida busca para "${query}" com ${results.length} resultados`);
    setSearchTerm(query);
    setSearchResults(results as ExtendedStream[]);
    
    // Verificar se temos resultados na categoria atual
    if (query && results.length === 0 && globalSearch) {
      console.log(`SeriesPage: Sem resultados locais, verificando busca global (${allSeries.length} séries em cache)`);
      
      // Se a busca global está ativada e não temos séries em cache, tentamos carregar todas
      if (allSeries.length === 0) {
        console.log('SeriesPage: Não há séries em cache, tentando carregar todas para busca global');
        loadAllSeries();
      }
    }
    
    if (query && results.length > 0) {
      console.log(`SeriesPage: Mostrando modal com ${results.length} resultados`);
      setSearchModalOpen(true);
    } else if (query && results.length === 0) {
      // Se não houver resultados mas a busca foi realizada, também mostre o modal
      console.log('SeriesPage: Mostrando modal sem resultados');
      setSearchModalOpen(true);
    } else {
      setSearchModalOpen(false);
    }
  }, [globalSearch, allSeries.length]);

  const handleSeriesDetails = (series: ExtendedStream) => {
    setSelectedSeries(series);
    setSeriesDetailsOpen(true);
  };

  const handleSeasonChange = (season: Season) => {
    setSelectedSeason(season);
    loadEpisodes(String(season.id));
  };

  const loadEpisodes = async (seasonId: string) => {
    if (!iptvService || !selectedSeries) return;
    
    try {
      setSeriesLoading(true);
      
      // Try to get episodes from the season object first
      const season = seasons.find(s => String(s.id) === seasonId);
      
      if (season && season.episodes && season.episodes.length > 0) {
        console.log('Using episodes from season object');
        setEpisodes(season.episodes);
      } else {
        // Otherwise fetch from API
        const episodesData = await iptvService.getEpisodes(selectedSeries.id, seasonId);
        
        if (episodesData && episodesData.length > 0) {
          setEpisodes(episodesData);
        } else {
          setError('Nenhum episódio encontrado');
        }
      }
    } catch (error) {
      console.error('Error loading episodes:', error);
      setError('Falha ao carregar episódios');
    } finally {
      setSeriesLoading(false);
    }
  };

  const handlePlayEpisode = (episode: Episode) => {
    if (!selectedSeries) return;
    
    // Armazenar o episódio selecionado
    setSelectedEpisode(episode);
    
    // Construir a URL do stream
    let streamUrl = '';
    if (iptvService) {
      const connection = iptvService.getConnection ? iptvService.getConnection() : null;
      if (connection) {
        streamUrl = `${connection.url}/series/${selectedSeries.id}/${selectedSeason?.id}/${episode.id}.${episode.containerExtension}?username=${connection.username}&password=${connection.password}`;
      }
    }
    
    setStreamUrl(streamUrl);
    setPlayerOpen(true);
  };

  const handleClosePlayer = () => {
    setPlayerOpen(false);
    setStreamUrl('');
    // Limpar o episódio selecionado quando fechar o player
    setSelectedEpisode(null);
    
    // Limpar o timeout de próximo episódio se existir
    if (nextEpisodeTimeoutRef.current) {
      clearTimeout(nextEpisodeTimeoutRef.current);
      nextEpisodeTimeoutRef.current = null;
    }
  };

  const handleCategoryChange = (_event: React.SyntheticEvent, categoryId: string) => {
    if (!iptvService) return;
    setSelectedCategoryId(categoryId);
    lastCategoryRef.current = categoryId;
    setPage(1);
    loadSeries(iptvService, categoryId);
  };

  const handleSeriesSelect = async (seriesItem: ExtendedStream) => {
    const selectedSeriesObj = searchTerm && allSeries.length > 0
      ? allSeries.find(s => s.id === seriesItem.id) 
      : series.find(s => s.id === seriesItem.id);

    if (selectedSeriesObj) {
      setSelectedSeries(selectedSeriesObj);
      setSeriesDetailsOpen(true);
      setSeriesLoading(true);
      
      try {
        if (iptvService) {
          const seriesInfo = await iptvService.getSeriesInfo(seriesItem.id);
          if (seriesInfo) {
            setSelectedSeries(prevSeries => ({...prevSeries, ...seriesInfo}));
            
            const seasonsData = await iptvService.getSeriesSeasons(seriesItem.id);
            setSeasons(seasonsData);
            
            if (seasonsData.length > 0) {
              setSelectedSeason(seasonsData[0]);
              setEpisodes(seasonsData[0].episodes || []);
            }
          }
        }
        
        const seriesSearchResults = await tmdbService.searchTV(seriesItem.name);
        if (seriesSearchResults && seriesSearchResults.results && seriesSearchResults.results.length > 0) {
          const seriesId = seriesSearchResults.results[0].id;
          const details = await tmdbService.getTVDetails(seriesId);
          setSeriesDetails(details);
        }
      } catch (error) {
        console.error("Erro ao buscar dados da série:", error);
      } finally {
        setSeriesLoading(false);
      }
    }
  };

  const handleCloseSeriesDetails = () => {
    setSeriesDetailsOpen(false);
    setSeriesDetails(null);
    setSelectedSeries(null);
    setSeasons([]);
    setEpisodes([]);
    setSelectedSeason(null);
  };

  const handleSeasonSelect = async (season: Season) => {
    try {
      setSelectedSeason(season);
      if (iptvService && selectedSeries) {
        setEpisodes([]);
        
        // Convert season ID to string to ensure compatible types
        const seasonId = String(season.id);
        
        // First check if the season already has episodes
        if (season.episodes && season.episodes.length > 0) {
          setEpisodes(season.episodes);
        } else {
          // If not, fetch them from the API
          const episodesData = await iptvService.getEpisodes(selectedSeries.id, seasonId);
          if (episodesData && episodesData.length > 0) {
            setEpisodes(episodesData);
          } else {
            console.error('No episodes found for this season');
          }
        }
      }
    } catch (error) {
      console.error('Error loading episodes:', error);
    }
  };

  const handleTimeUpdate = async (currentTime: number, duration: number) => {
    if (!selectedSeries || !selectedSeason || !episodes || episodes.length === 0) return;
    
    // Find the current episode being played
    const currentEpisode = episodes.find(ep => {
      const episodeUrl = `${selectedSeries.id}/${selectedSeason.id}/${ep.id}`;
      return streamUrl.includes(episodeUrl);
    });
    
    if (!currentEpisode) {
      console.log('Episódio atual não encontrado. URL do stream:', streamUrl);
      return;
    }
    
    // Check if episode is completed (watched more than 95% of the duration)
    const isCompleted = duration > 0 && currentTime >= duration * 0.95;
    
    // Check if we should save progress now
    const shouldSave = 
      // Save if it's the first update
      lastSaveTimeRef.current === 0 ||
      // Save if enough time has passed since last save
      (currentTime - lastSaveTimeRef.current >= SAVE_INTERVAL) ||
      // Save if we're close to the end (to mark as watched)
      isCompleted;
    
    // Early return if we shouldn't save yet
    if (!shouldSave) {
      return;
    }
    
    // Verificar se é hora de pular para o próximo episódio
    const seriesId = selectedSeries.id;
    const skipThresholdSeconds = skipCreditsConfig[seriesId] || endCreditsTime;
    const timeRemaining = duration - currentTime;
    
    if (autoPlayNextEpisode && duration > 0 && 
        timeRemaining <= skipThresholdSeconds && 
        nextEpisodeTimeoutRef.current === null) {
      
      console.log(`Chegou ao ponto de pular créditos (${skipThresholdSeconds} segundos antes do fim)`);
      
      // Encontrar o próximo episódio
      const currentIndex = episodes.findIndex(ep => ep.id === currentEpisode.id);
      if (currentIndex !== -1 && currentIndex < episodes.length - 1) {
        // Próximo episódio na mesma temporada
        const nextEpisode = episodes[currentIndex + 1];
        console.log('Preparando para pular para o próximo episódio:', nextEpisode.title);
        
        // Iniciar um timeout para mudar de episódio (3 segundos)
        nextEpisodeTimeoutRef.current = setTimeout(() => {
          console.log('Pulando para o próximo episódio:', nextEpisode.title);
          handlePlayEpisode(nextEpisode);
          nextEpisodeTimeoutRef.current = null;
        }, 3000);
      } else if (currentIndex === episodes.length - 1) {
        // Último episódio da temporada, verificar próxima temporada
        const currentSeasonIndex = seasons.findIndex(s => s.id === selectedSeason.id);
        if (currentSeasonIndex !== -1 && currentSeasonIndex < seasons.length - 1) {
          const nextSeason = seasons[currentSeasonIndex + 1];
          console.log('Preparando para pular para a próxima temporada:', nextSeason.name);
          
          // Buscar episódios da próxima temporada
          try {
            const nextSeasonEpisodes = await iptvService?.getEpisodes(selectedSeries.id, String(nextSeason.id));
            if (nextSeasonEpisodes && nextSeasonEpisodes.length > 0) {
              const firstEpisode = nextSeasonEpisodes[0];
              
              nextEpisodeTimeoutRef.current = setTimeout(() => {
                console.log('Pulando para a próxima temporada:', nextSeason.name, 'episódio:', firstEpisode.title);
                // Atualizar temporada selecionada
                setSelectedSeason(nextSeason);
                setEpisodes(nextSeasonEpisodes);
                // Reproduzir o primeiro episódio
                handlePlayEpisode(firstEpisode);
                nextEpisodeTimeoutRef.current = null;
              }, 3000);
            }
          } catch (error) {
            console.error('Erro ao buscar episódios da próxima temporada:', error);
          }
        }
      }
    }
    
    console.log('Salvando progresso para o episódio:', currentEpisode);
    lastSaveTimeRef.current = currentTime;
    
    try {
      // Get the numeric season ID (as displayed in the UI)
      const seasonNumber = Number(selectedSeason.id);
      
      // Save episode progress with a composite ID that includes series, season, and episode
      const compositeId = `${selectedSeries.id}_${selectedSeason.id}_${currentEpisode.id}`;
      
      // If the episode is completed, we'll mark it
      if (isCompleted) {
        console.log('🏁 Episode completed!', currentEpisode.title);
        
        // We can either remove this individual episode entry since it's completed
        await dbService.removeWatchedSeries(compositeId);
        console.log('✅ Removed completed episode from continue watching:', compositeId);
        
        // But we'll still update the main series entry to track the last watched episode
        const mainSeriesData = {
          id: String(selectedSeries.id),
          name: selectedSeries.name,
          thumbnail: selectedSeries.thumbnail || '',
          cover: selectedSeries.cover,
          progress: 0, // Reset progress since episode is completed
          duration: duration,
          lastWatched: Date.now(),
          categoryId: selectedCategoryId,
          seasonId: String(seasonNumber),
          episodeId: currentEpisode.id,
          currentEpisode: {
            number: currentEpisode.episode,
            title: currentEpisode.title,
            season: seasonNumber
          },
          completed: true // Mark as completed
        };
        
        console.log('💾 Updating main series data after completion:', mainSeriesData);
        await dbService.saveWatchedSeries(mainSeriesData);
        
        // Ensure we only keep the most recent 10 items in Continue Watching
        await cleanupContinueWatching();
        
        return; // Exit early since we've handled the completed episode
      }
      
      // For non-completed episodes, save normal progress
      const watchedData = {
        id: compositeId,
        name: selectedSeries.name,
        thumbnail: selectedSeries.thumbnail || '',
        cover: selectedSeries.cover,
        progress: currentTime,
        duration: duration,
        lastWatched: Date.now(),
        categoryId: selectedCategoryId,
        seasonId: String(seasonNumber), // Store the season NUMBER, not the ID
        episodeId: currentEpisode.id,
        currentEpisode: {
          number: currentEpisode.episode,
          title: currentEpisode.title,
          season: seasonNumber // Store the season number (not the episode ID)
        }
      };

      console.log('💾 Saving watched data:', watchedData);

      // Save progress of the episode
      await dbService.saveWatchedSeries(watchedData);

      // Also save progress with just the series ID to track the latest episode
      const mainSeriesData = {
        ...watchedData,
        id: String(selectedSeries.id),
        seasonId: String(seasonNumber) // Store the season NUMBER, not the ID
      };
      
      console.log('💾 Saving main series data:', mainSeriesData);
      await dbService.saveWatchedSeries(mainSeriesData);

      console.log('Progresso salvo com sucesso:', watchedData);
      
      // Check if we need to clean up old entries (every 5 minutes)
      const lastCleanup = localStorage.getItem('lastContinueWatchingCleanup');
      const now = Date.now();
      
      if (!lastCleanup || (now - Number(lastCleanup)) > CLEANUP_INTERVAL) {
        await cleanupContinueWatching();
        localStorage.setItem('lastContinueWatchingCleanup', now.toString());
      }
      
    } catch (error) {
      console.error('Error saving series progress:', error);
    }
  };

  /**
   * Cleans up the Continue Watching list by keeping only the 10 most recent items
   */
  const cleanupContinueWatching = async () => {
    try {
      console.log('🧹 Starting Continue Watching cleanup');
      
      // Get all recently watched series first (without limit)
      const allWatchedSeriesPromise = dbService.getRecentlyWatchedSeries(9999);
      const episodeEntriesPromise = getAllWatchedEpisodes();
      
      const [allWatchedSeries, episodeEntries] = await Promise.all([
        allWatchedSeriesPromise,
        episodeEntriesPromise
      ]);
      
      // Process main series entries (keep top 10)
      if (allWatchedSeries.length > 10) {
        console.log(`🧹 Found ${allWatchedSeries.length} series, keeping 10 most recent`);
        
        // Sort by lastWatched (newest first)
        const sortedSeries = [...allWatchedSeries].sort((a, b) => 
          (b.lastWatched || 0) - (a.lastWatched || 0)
        );
        
        // Remove the older entries (past the first 10)
        const toRemove = sortedSeries.slice(10);
        
        for (const item of toRemove) {
          await dbService.removeWatchedSeries(item.id);
          console.log(`🧹 Removed old series from Continue Watching:`, item.name);
        }
      }
      
      // Process completed episodes (remove if progress >= 95% of duration)
      let removedEpisodes = 0;
      for (const entry of episodeEntries) {
        if (entry.duration > 0 && entry.progress >= entry.duration * 0.95) {
          await dbService.removeWatchedSeries(entry.id);
          removedEpisodes++;
        }
      }
      
      if (removedEpisodes > 0) {
        console.log(`🧹 Removed ${removedEpisodes} completed episodes`);
      }
      
      console.log('🧹 Continue Watching cleanup completed');
    } catch (error) {
      console.error('Error during Continue Watching cleanup:', error);
    }
  };
  
  /**
   * Gets all episode-specific entries from the watched_series store
   */
  const getAllWatchedEpisodes = async () => {
    try {
      // Get all series with a limit of 9999 to effectively get all
      const allSeries = await dbService.getRecentlyWatchedSeries(9999);
      
      // For each series, find episode-specific entries
      const result: any[] = [];
      
      for (const series of allSeries) {
        if (!series.id) continue;
        
        // Try to get episode entries for this series
        try {
          // Get entries with IDs in the format series_season_episode
          const seriesId = series.id;
          
          // We need to use a custom approach since there's no direct API for this
          // In a real app, you might want to add a dedicated method to dbService
          // This is a workaround using existing APIs
          
          // For each episode we know about, try to get its entry
          if (series.seasonId && series.episodeId) {
            const episodeId = `${seriesId}_${series.seasonId}_${series.episodeId}`;
            const episodeEntry = await dbService.getWatchedSeries(episodeId);
            
            if (episodeEntry) {
              result.push(episodeEntry);
            }
          }
        } catch (err) {
          console.error(`Error getting episodes for series ${series.id}:`, err);
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error getting all watched episodes:', error);
      return [];
    }
  };

  // Manipular busca de séries
  const handleGlobalSearchToggle = (enabled: boolean) => {
    setGlobalSearch(enabled);
    
    // Carregar todas as séries quando a pesquisa global for ativada
    if (enabled && allSeries.length === 0) {
      console.log('Carregando todas as séries para pesquisa global');
      loadAllSeries();
    }
  };
  
  // Manipular seleção de resultado de busca
  const handleSelectSearchResult = (seriesId: string) => {
    setSearchModalOpen(false);
    const seriesToSelect = series.find(s => s.id === seriesId) || allSeries.find(s => s.id === seriesId);
    if (seriesToSelect) {
      handleSeriesSelect(seriesToSelect);
    }
  };
  
  // Fechar modal de busca
  const closeSearchModal = () => {
    setSearchModalOpen(false);
  };
  
  // Ordenar séries
  const handleSort = (type: 'alpha' | 'recent' | 'rating') => {
    setSortBy(type);
    setFilterDrawerOpen(false);
  };

  // Carregar mais séries (paginação)
  const handleLoadMore = () => {
    setPage(prevPage => prevPage + 1);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  // Função para ordenar as séries
  const getSortedSeries = () => {
    const sorted = [...series];
    
    switch (sortBy) {
      case 'alpha':
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
      case 'recent':
        return sorted.sort((a, b) => {
          const dateA = a.lastModified ? new Date(a.lastModified).getTime() : 0;
          const dateB = b.lastModified ? new Date(b.lastModified).getTime() : 0;
          return dateB - dateA;
        });
      case 'rating':
        return sorted.sort((a, b) => {
          const ratingA = a.rating || 0;
          const ratingB = b.rating || 0;
          return ratingB - ratingA;
        });
      default:
        return sorted;
    }
  };

  const playSeriesDirectly = async (series: Stream, episodeId: string, seasonId: string, startTime: number = 0) => {
    if (!iptvService) {
      console.error('IPTV service not initialized');
      return;
    }
    
    try {
      setSelectedSeries(series);
      setSeriesLoading(true);
      
      console.log('🔍 Starting playback with:', {
        seriesId: series.id,
        seriesType: typeof series.id,
        seasonId,
        seasonIdType: typeof seasonId,
        episodeId,
        episodeIdType: typeof episodeId,
        startTime
      });
      
      // Get the connection first to ensure we have valid credentials
      const connection = iptvService.getConnection ? iptvService.getConnection() : null;
      if (!connection) {
        throw new Error('No valid connection available');
      }
      
      // Buscar informações detalhadas para ter mais dados da série
      const seriesInfo = await iptvService.getSeriesInfo(series.id);
      if (seriesInfo) {
        setSelectedSeries(prevSeries => ({...prevSeries, ...seriesInfo}));
        console.log('🔍 Got series info:', seriesInfo);
      }
      
      const seasonsData = await iptvService.getSeriesSeasons(series.id);
      if (!seasonsData || seasonsData.length === 0) {
        throw new Error('No seasons found for this series');
      }
      
      console.log('🔍 Full seasons data:', JSON.stringify(seasonsData, null, 2));
      setSeasons(seasonsData);
      
      // Find the season by its ID - the seasonId from the database is a string
      console.log('🔍 Looking for season with ID:', seasonId);
      console.log('🔍 Available seasons:', seasonsData.map(s => ({id: s.id, idType: typeof s.id, name: s.name})));
      
      // Try different methods to find the season
      let selectedSeasonObj;
      
      // Method 1: Direct string comparison
      selectedSeasonObj = seasonsData.find(s => String(s.id) === seasonId);
      console.log('🔍 Method 1 (string comparison) result:', selectedSeasonObj ? 'Found' : 'Not found');
      
      // Method 2: Number comparison
      if (!selectedSeasonObj) {
        const seasonIdNum = Number(seasonId);
        selectedSeasonObj = seasonsData.find(s => Number(s.id) === seasonIdNum);
        console.log('🔍 Method 2 (number comparison) result:', selectedSeasonObj ? 'Found' : 'Not found');
      }
      
      // Method 3: Try finding by season number in array index
      if (!selectedSeasonObj && !isNaN(Number(seasonId))) {
        const index = Number(seasonId) - 1; // Season numbers often start at 1
        if (index >= 0 && index < seasonsData.length) {
          selectedSeasonObj = seasonsData[index];
          console.log('🔍 Method 3 (array index) result:', selectedSeasonObj ? 'Found' : 'Not found');
        }
      }
      
      if (!selectedSeasonObj) {
        console.error('🔴 Season not found:', seasonId, 'Available seasons:', seasonsData.map(s => s.id));
        console.error('🔴 Season type mismatch - seasonId:', typeof seasonId, 'values in data:', seasonsData.map(s => typeof s.id));
        
        // As a fallback, just use the first season
        if (seasonsData.length > 0) {
          console.log('🔍 Using first season as fallback');
          selectedSeasonObj = seasonsData[0];
        } else {
          throw new Error(`Selected season not found: ${seasonId}. Available seasons: ${seasonsData.map(s => s.id).join(', ')}`);
        }
      }
      
      console.log('🔍 Selected season:', selectedSeasonObj);
      setSelectedSeason(selectedSeasonObj);
      
      // Use the selected season's ID for getting episodes
      const selectedSeasonId = String(selectedSeasonObj.id);
      console.log('🔍 Getting episodes for season ID:', selectedSeasonId);
      
      // Try to get episodes from API first
      let episodesData: Episode[] = [];
      try {
        episodesData = await iptvService.getEpisodes(series.id, selectedSeasonId);
      } catch (error) {
        console.error('Error fetching episodes:', error);
      }
      
      // If API call failed or returned no episodes, use episodes from season object
      if (!episodesData || episodesData.length === 0) {
        console.log('🔍 No episodes from API, checking season object for episodes');
        if (selectedSeasonObj.episodes && selectedSeasonObj.episodes.length > 0) {
          console.log('🔍 Using episodes from season object:', selectedSeasonObj.episodes.length);
          episodesData = selectedSeasonObj.episodes;
        } else {
          throw new Error('No episodes found for this season');
        }
      }
      
      console.log('🔍 Got episodes:', episodesData.length);
      setEpisodes(episodesData);
      
      let selectedEpisode = episodesData.find(e => e.id === episodeId);
      
      // If episode not found by ID, try to find by episode number
      if (!selectedEpisode && !isNaN(Number(episodeId))) {
        console.log('🔍 Episode not found by ID, trying by episode number');
        selectedEpisode = episodesData.find(e => String(e.episode) === episodeId);
      }
      
      // If still not found, use the first episode
      if (!selectedEpisode && episodesData.length > 0) {
        console.log('🔍 Using first episode as fallback');
        selectedEpisode = episodesData[0];
      }
      
      if (!selectedEpisode) {
        console.error('🔴 Episode not found:', episodeId, 'Available episodes:', episodesData.map(e => ({id: e.id, title: e.title, episode: e.episode})));
        throw new Error(`Selected episode not found: ${episodeId}`);
      }
      
      console.log('🔍 Selected episode:', selectedEpisode);
      
      // Construct the stream URL using the connection details
      const streamUrl = `${connection.url}/series/${series.id}/${selectedSeasonObj.id}/${selectedEpisode.id}.${selectedEpisode.containerExtension}?username=${connection.username}&password=${connection.password}`;
      
      console.log('🔍 Reproduzindo série:', {
        name: series.name,
        seasonId: selectedSeasonObj.id,
        episodeId: selectedEpisode.id,
        startTime,
        streamUrl
      });
      
      // Store the start time in a ref to ensure it's available when the player opens
      const startTimeToApply = startTime;
      
      // Set stream URL and open player
      setStreamUrl(streamUrl);
      setPlayerOpen(true);
      
      // Set the initial playback time after player is visible
      if (startTimeToApply > 0) {
        console.log('🔍 Will attempt to seek to:', startTimeToApply);
        
        const applySeek = () => {
          if (playerRef.current) {
            console.log('🔍 Player ref available, seeking to:', startTimeToApply);
            playerRef.current.seekTo(startTimeToApply);
            return true;
          }
          return false;
        };
        
        // Try immediately
        const immediate = applySeek();
        
        // Also try with multiple delays to ensure it works
        if (!immediate) {
          const delays = [500, 1500, 3000, 5000];
          delays.forEach(delay => {
            setTimeout(() => {
              if (playerRef.current) {
                console.log(`🔍 Delayed seek (${delay}ms) to:`, startTimeToApply);
                playerRef.current.seekTo(startTimeToApply);
              }
            }, delay);
          });
        }
      }
    } catch (error) {
      console.error('🔴 Erro ao reproduzir série:', error);
      alert('Não foi possível reproduzir este episódio. Por favor, tente novamente mais tarde.');
    } finally {
      setSeriesLoading(false);
    }
  };



  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Background Effects */}
      <BackgroundEffects type="series" intensity="medium" />
      
      {/* Header section */}
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          mb: 3,
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 2, md: 0 }
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Séries
          </Typography>
        </Box>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center',
          gap: 2,
          width: { xs: '100%', md: 'auto' }
        }}>
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              flexGrow: 1,
              maxWidth: 600,
              mb: { xs: 2, md: 0 }
            }}
          >
            {/* SEARCH BAR - IPTV Player */}
            <Box
              position="sticky"
              top={0}
              zIndex={10}
            >
              <Grid container alignItems="center" spacing={2} px={2}>
                <Grid item xs sm md>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <SearchBar
                      items={globalSearch ? [...series, ...allSeries] : series}
                      placeholder="Buscar séries..."
                      onSearch={handleSearch}
                      onSelectItem={(seriesItem) => handleSelectSearchResult(seriesItem.id)}
                    />
                    <Box ml={1}>
                      <Tooltip title={globalSearch ? "Busca global ativada" : "Busca global desativada"}>
                        <FormControlLabel
                          control={
                            <Switch
                              size="small"
                              checked={globalSearch}
                              onChange={(e) => {
                                const isEnabled = e.target.checked;
                                handleGlobalSearchToggle(isEnabled);
                                console.log(`Busca global ${isEnabled ? 'ativada' : 'desativada'} (${series.length} séries locais, ${allSeries.length} séries globais)`);
                              }}
                            />
                          }
                          label={
                            <Typography variant="caption">
                              Global
                            </Typography>
                          }
                        />
                      </Tooltip>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton 
              onClick={() => setFilterDrawerOpen(true)} 
              sx={{ 
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' }
              }}
            >
              <TuneIcon />
            </IconButton>
            
            {/* View mode toggle buttons removed as requested */}
          </Box>
        </Box>
      </Box>

      {/* Continue Watching section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Continue Assistindo
          </Typography>
        </Box>
        <ContinueWatchingSeries 
          onSeriesSelect={(series) => handleSeriesSelect(series)} 
          onSeriesPlay={playSeriesDirectly}
          maxItems={6} 
        />
      </Box>

      {/* Main content */}
      {loading && series.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Box sx={{ mt: 3 }}>
          {categories.length > 0 && (
            <Paper
              sx={{
                backgroundColor: 'rgba(18, 18, 18, 0.8)',
                backdropFilter: 'blur(10px)',
                borderRadius: 2,
                mb: 3,
                overflowX: 'auto',
                '&::-webkit-scrollbar': {
                  height: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '4px',
                },
              }}
            >
              <Tabs
                value={selectedCategoryId}
                onChange={handleCategoryChange}
                variant="scrollable"
                scrollButtons="auto"
                aria-label="Categorias de séries"
                sx={{
                  minHeight: '48px',
                  '& .MuiTabs-indicator': {
                    backgroundColor: theme.palette.primary.main,
                  },
                  '& .MuiTab-root': {
                    minHeight: '48px',
                    color: 'rgba(255, 255, 255, 0.7)',
                    '&.Mui-selected': {
                      color: theme.palette.primary.main,
                    },
                  },
                }}
              >
                {categories.map((category) => (
                  <Tab
                    key={category.id}
                    label={category.name}
                    value={category.id}
                    sx={{
                      textTransform: 'none',
                      fontSize: '0.95rem',
                      fontWeight: 500,
                      px: 2,
                    }}
                  />
                ))}
              </Tabs>
            </Paper>
          )}

          <motion.div
            initial="hidden"
            animate="show"
            variants={containerVariants}
          >
            <MotionGrid container spacing={2}>
              {getSortedSeries().slice(0, page * seriesPerPage).map((seriesItem) => (
                <MotionGrid
                  item
                  xs={6}
                  sm={4}
                  md={3}
                  lg={2.4}
                  key={seriesItem.id}
                  variants={itemVariants}
                  transition={{ duration: 0.3 }}
                >
                  <MovieCard movie={seriesItem} onClick={() => handleSeriesSelect(seriesItem)} />
                </MotionGrid>
              ))}
            </MotionGrid>
          </motion.div>

          {series.length > page * seriesPerPage && (
            <Box sx={{ textAlign: 'center', mt: 4 }}>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleLoadMore}
                size="large"
                sx={{
                  borderRadius: 8,
                  px: 4,
                  py: 1,
                  borderWidth: 2,
                  '&:hover': {
                    borderWidth: 2
                  }
                }}
              >
                Carregar Mais
              </Button>
            </Box>
          )}
        </Box>
      )}

      {/* Modal de Resultados de Busca */}
      <Modal
        open={searchModalOpen}
        onClose={closeSearchModal}
        aria-labelledby="search-results-modal"
        sx={{
          zIndex: 100000
        }}
        BackdropProps={{
          style: {
            backdropFilter: 'blur(3px)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 99999
          }
        }}
      >
        <Fade in={searchModalOpen}>
        <Box sx={{
          position: 'absolute',
            top: '50%',
          left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '90%', sm: '80%', md: '70%' },
            maxWidth: 800,
          bgcolor: 'background.paper',
            borderRadius: 3,
          boxShadow: 24,
          display: 'flex',
          flexDirection: 'column',
            maxHeight: '80vh',
            overflow: 'hidden',
            zIndex: 100001
        }}>
          <Box sx={{ 
            p: 2, 
            display: 'flex', 
            justifyContent: 'space-between',
              alignItems: 'center',
            borderBottom: 1,
            borderColor: 'divider'
          }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SearchIcon color="primary" />
            <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 500 }}>
              {searching ? 'Buscando...' : searchResults.length > 0 
                ? `Resultados para "${searchTerm}"` 
                : 'Nenhum resultado encontrado'}
            </Typography>
                {searchResults.length > 0 && (
                  <Chip 
                    label={`${searchResults.length} encontrados`} 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                )}
              </Box>
              <IconButton onClick={closeSearchModal} size="small" sx={{ color: 'text.secondary' }}>
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          
            <Box sx={{ 
              overflow: 'auto', 
              flex: 1,
              '&::-webkit-scrollbar': {
                width: '8px',
                backgroundColor: alpha(theme.palette.common.white, 0.05),
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.3),
                }
              },
            }}>
            {searching ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4, height: '200px' }}>
                  <CircularProgress size={40} />
              </Box>
            ) : searchResults.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="show"
                >
                  <Grid container spacing={2} sx={{ p: 2 }}>
                {searchResults.map((seriesItem) => (
                      <Grid item xs={12} sm={6} md={4} key={seriesItem.id}>
                        <motion.div variants={itemVariants}>
                          <Paper
                            elevation={0}
                    sx={{ 
                              p: 1.5,
                              borderRadius: 2,
                      transition: 'all 0.2s ease',
                              bgcolor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.05) : alpha(theme.palette.common.black, 0.02),
                              border: '1px solid',
                              borderColor: 'transparent',
                              cursor: 'pointer',
                              display: 'flex',
                              height: '100%',
                              overflow: 'hidden',
                      '&:hover': { 
                                bgcolor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.08) : alpha(theme.palette.common.black, 0.04),
                                borderColor: alpha(theme.palette.primary.main, 0.3),
                                transform: 'translateY(-3px)',
                                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                              }
                            }}
                            onClick={() => handleSelectSearchResult(seriesItem.id)}
                          >
                            <Box sx={{ display: 'flex', width: '100%' }}>
                              {/* Thumbnail */}
                              <Box 
                                sx={{ 
                                  width: 80,
                                  height: 120,
                                  flexShrink: 0,
                                  borderRadius: 1,
                                  overflow: 'hidden',
                                  mr: 2,
                                  position: 'relative',
                                  bgcolor: 'action.hover'
                                }}
                              >
                                <Box
                                  component="img"
                                  src={seriesItem.thumbnail}
                                  alt={seriesItem.name}
                                  onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                                    e.currentTarget.src = '/default-poster.jpg';
                                  }}
                                  sx={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                  }}
                                />
                                {seriesItem.episodeCount && (
                                  <Chip
                                    label={`${seriesItem.episodeCount} eps`}
                                    size="small"
                                    sx={{
                                      position: 'absolute',
                                      bottom: 4,
                                      right: 4,
                                      height: 20,
                                      fontSize: '0.625rem',
                                      fontWeight: 'bold',
                                      backgroundColor: alpha(theme.palette.common.black, 0.6),
                                      color: 'white',
                                      '& .MuiChip-label': {
                                        px: 1
                                      }
                                    }}
                                  />
                                )}
                              </Box>
                              
                              {/* Info */}
                              <Box sx={{ 
                                display: 'flex', 
                                flexDirection: 'column', 
                                width: '100%',
                                overflow: 'hidden',
                                justifyContent: 'space-between'
                              }}>
                                <Box>
                                  <Typography
                                    variant="subtitle1"
                      sx={{
                                      fontWeight: 'medium',
                                      mb: 0.5,
                                      lineHeight: 1.2,
                                      display: '-webkit-box',
                                      WebkitLineClamp: 2,
                                      WebkitBoxOrient: 'vertical',
                                      overflow: 'hidden',
                                    }}
                                  >
                                    {seriesItem.name}
                                  </Typography>
                                  
                                  {seriesItem.genres && (
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                      sx={{
                                        fontSize: '0.75rem',
                                        opacity: 0.7,
                                        mb: 0.5
                                      }}
                                    >
                                      {seriesItem.genres}
                                    </Typography>
                                  )}
                                </Box>
                                
                                <Box sx={{ 
                                  display: 'flex', 
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  mt: 'auto'
                                }}>
                                  {seriesItem.rating && (
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <StarIcon sx={{ fontSize: '0.875rem', color: 'gold', mr: 0.5 }} />
                                      <Typography variant="caption">{seriesItem.rating.toFixed(1)}/10</Typography>
                                    </Box>
                                  )}
                                  
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Button
                                      size="small"
                                      startIcon={<PlayArrowIcon />}
                                      onClick={() => handleSelectSearchResult(seriesItem.id)}
                                      sx={{ 
                                        minWidth: 'auto', 
                                        color: theme.palette.primary.main,
                        '&:hover': {
                                          bgcolor: 'rgba(14, 165, 233, 0.1)'
                                        }
                                      }}
                                    >
                                      Assistir
                                    </Button>
                                    <Typography variant="caption" color="text.secondary">
                                      Ver detalhes
                                    </Typography>
                                  </Box>
                                </Box>
                              </Box>
                            </Box>
                          </Paper>
                        </motion.div>
                      </Grid>
                    ))}
                  </Grid>
                </motion.div>
              ) : (
                <Box sx={{ 
                  p: 6, 
                  textAlign: 'center', 
                  display: 'flex', 
                  flexDirection: 'column', 
                  justifyContent: 'center', 
                  alignItems: 'center' 
                }}>
                  <TvIcon sx={{ fontSize: 50, color: 'text.disabled', mb: 2 }} />
                <Typography sx={{ fontWeight: 500, mb: 1 }}>Nenhum resultado encontrado</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 400, mb: 3 }}>
                  {globalSearch 
                      ? 'Não encontramos séries correspondentes ao termo de busca.' 
                      : 'Tente outros termos de busca ou ative a pesquisa global para procurar em todas as categorias.'
                  }
                </Typography>
                <Button 
                  variant="outlined" 
                    size="medium" 
                    startIcon={<SearchIcon />}
                  onClick={() => setSearchTerm('')}
                >
                    Nova busca
                </Button>
              </Box>
            )}
          </Box>
            </Box>
        </Fade>
      </Modal>

      {/* Modal de filtros */}
      <Drawer
        anchor="right"
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        PaperProps={{
          sx: {
            width: { xs: '80%', sm: '400px' },
            backgroundColor: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(10px)',
            p: 3
          }
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" fontWeight={600}>Filtros</Typography>
          <IconButton onClick={() => setFilterDrawerOpen(false)}>
            <CloseIcon />
          </IconButton>
        </Box>
        
        <Divider sx={{ mb: 3 }} />
        
        <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>Ordenar por</Typography>
        
        <List>
          <ListItem 
            onClick={() => handleSort('alpha')}
            sx={{ 
              borderRadius: 1,
              mb: 1,
              cursor: 'pointer',
              bgcolor: sortBy === 'alpha' ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': { bgcolor: sortBy === 'alpha' ? alpha(theme.palette.primary.main, 0.15) : 'rgba(255, 255, 255, 0.08)' }
            }}
          >
            <ListItemText primary="Ordem alfabética" />
          </ListItem>
          <ListItem 
            onClick={() => handleSort('recent')}
            sx={{ 
              borderRadius: 1,
              mb: 1,
              cursor: 'pointer',
              bgcolor: sortBy === 'recent' ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': { bgcolor: sortBy === 'recent' ? alpha(theme.palette.primary.main, 0.15) : 'rgba(255, 255, 255, 0.08)' }
            }}
          >
            <ListItemText primary="Mais recentes" />
          </ListItem>
          <ListItem 
            onClick={() => handleSort('rating')}
            sx={{ 
              borderRadius: 1,
              cursor: 'pointer',
              bgcolor: sortBy === 'rating' ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': { bgcolor: sortBy === 'rating' ? alpha(theme.palette.primary.main, 0.15) : 'rgba(255, 255, 255, 0.08)' }
            }}
          >
            <ListItemText primary="Melhor avaliação" />
          </ListItem>
        </List>
      </Drawer>

      {/* Enhanced Series Modal */}
      <EnhancedSeriesModal
        open={seriesDetailsOpen}
        onClose={handleCloseSeriesDetails}
        series={selectedSeries ? {
          id: selectedSeries.id,
          name: selectedSeries.name,
          description: selectedSeries.description,
          overview: seriesDetails?.overview || selectedSeries.description,
          thumbnail: selectedSeries.thumbnail,
          cover: selectedSeries.cover,
          rating: seriesDetails?.vote_average || selectedSeries.rating,
          year: seriesDetails?.first_air_date 
            ? new Date(seriesDetails.first_air_date).getFullYear().toString()
            : selectedSeries.year,
          genres: seriesDetails?.genres?.map((g: any) => g.name) || selectedSeries.genres,
          status: seriesDetails?.status || selectedSeries.status,
          network: seriesDetails?.networks?.[0]?.name,
          runtime: seriesDetails?.episode_run_time?.[0],
          totalSeasons: seasons.length,
          totalEpisodes: episodes.length
        } : null}
        seasons={seasons}
        selectedSeason={selectedSeason}
        episodes={episodes}
        onSeasonSelect={handleSeasonSelect}
        onEpisodePlay={handlePlayEpisode}
        loading={seriesLoading}
        autoSkipSettings={{
          enabled: autoPlayNextEpisode,
          skipIntroTime: 10,
          skipCreditsTime: endCreditsTime,
          autoPlayNext: autoPlayNextEpisode
        }}
        onAutoSkipChange={(settings) => {
          setAutoPlayNextEpisode(settings.autoPlayNext);
          setEndCreditsTime(settings.skipCreditsTime);
        }}
      />

      
      {/* Player de Vídeo */}
      <Dialog
        open={playerOpen}
        onClose={handleClosePlayer}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: '#000',
            borderRadius: { xs: 0.5, sm: 1 },
            overflow: 'hidden',
            height: { 
              xs: '70vh', 
              sm: '75vh', 
              md: '80vh', 
              lg: '85vh' 
            }
          }
        }}
      >
        <DialogContent sx={{ 
          p: 0, 
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <VideoPlayer
            ref={playerRef}
            url={streamUrl}
            title={selectedEpisode?.title || selectedSeries?.name}
            initialTime={selectedSeries && 'progress' in selectedSeries ? selectedSeries.progress || 0 : 0}
            autoPlay={true}
            onTimeUpdate={handleTimeUpdate}
            onClose={handleClosePlayer}
            style={{ width: '100%', height: '100%' }}
          />
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default SeriesPage;
