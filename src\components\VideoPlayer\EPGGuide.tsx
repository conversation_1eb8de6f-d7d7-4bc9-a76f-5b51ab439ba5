import React, { useEffect, useState, useCallback } from 'react';
import { Box, Typography, Paper, Grid, useTheme, Alert, Divider, Chip, LinearProgress, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, <PERSON>alogA<PERSON>, Button, Fade, Zoom, Slide, CircularProgress } from '@mui/material';
import moment from 'moment';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import TodayIcon from '@mui/icons-material/Today';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import CloseIcon from '@mui/icons-material/Close';
import ProgressBar from '../ProgressBar';
import { createIPTVService } from '../../services/iptvService';

interface Program {
  title: string;
  startTime: string;
  endTime: string;
  description?: string;
  _originalStart?: string;
  _originalEnd?: string;
}

interface EPGGuideProps {
  epgData: {
    programs: Program[];
    name?: string;
    id?: string;
  };
  onRefreshEpg?: () => void;
}

const EPGGuide: React.FC<EPGGuideProps> = ({ epgData, onRefreshEpg }) => {
  const theme = useTheme();
  const { programs = [], name } = epgData;
  const [currentTime, setCurrentTime] = useState<moment.Moment>(moment());
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [showRefreshButton, setShowRefreshButton] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);

  // Atualizar o horário atual a cada minuto
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(moment());
    }, 60000); // 1 minuto

    return () => clearInterval(timer);
  }, []);

  // EPG data effect removed to reduce console noise

  // Ativar modo de debug pressionando Alt+T
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey && e.key === 't') {
        e.preventDefault();
        setDebugMode(prev => !prev);
        console.log('EPG Debug mode:', !debugMode);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [debugMode]);

  const isCurrentlyPlaying = (startTime: string, endTime: string) => {
    const now = currentTime;
    const start = moment(startTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    return now.isBetween(start, end);
  };

  // Formatação mais precisa de horário com debug
  const formatTime = (timeString: string) => {
    try {
      const time = moment(timeString, 'YYYY-MM-DD HH:mm:ss');
      if (!time.isValid()) {
        console.warn('Invalid time format:', timeString);
        return timeString.substring(11, 16); // Fallback to extract HH:MM directly from string
      }
      return time.format('HH:mm');
    } catch (error) {
      console.error('Error formatting time:', error);
      return '??:??';
    }
  };

  // Formatação mais precisa de data com debug
  const formatDate = (timeString: string) => {
    try {
      const date = moment(timeString, 'YYYY-MM-DD HH:mm:ss');
      if (!date.isValid()) {
        console.warn('Invalid date format:', timeString);
        return timeString.substring(8, 10) + '/' + timeString.substring(5, 7); // Fallback
      }
      return date.format('DD/MM');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '??/??';
    }
  };

  // Calculate program duration in minutes
  const getProgramDuration = (startTime: string, endTime: string) => {
    const start = moment(startTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    return moment.duration(end.diff(start)).asMinutes();
  };

  // Calcular o progresso do programa atual
  const getProgress = (startTime: string, endTime: string) => {
    const now = currentTime;
    const start = moment(startTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    
    if (!now.isBetween(start, end)) return 0;
    
    const totalDuration = end.diff(start);
    const elapsedDuration = now.diff(start);
    
    return Math.min(100, Math.max(0, (elapsedDuration / totalDuration) * 100));
  };

  // Calcular programa com fallback
  const getCorrectedPrograms = (progs: Program[]) => {
    return progs.map(prog => {
      // Verificar se precisa de correção
      const start = moment(prog.startTime, 'YYYY-MM-DD HH:mm:ss');
      const end = moment(prog.endTime, 'YYYY-MM-DD HH:mm:ss');
      
      // Se as datas são válidas e não precisam ser corrigidas
      if (start.isValid() && end.isValid() && end.isAfter(start)) {
        return prog;
      }
      
      // Tentar extrair os horários diretamente do formato XML
      try {
        let fixedStartTime = prog.startTime;
        let fixedEndTime = prog.endTime;
        
        // Extrair a data do timestamp original se possível
        const originalStart = prog.startTime.match(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/);
        const originalEnd = prog.endTime.match(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/);
        
        if (originalStart) {
          const [_, year, month, day, hour, minute, second] = originalStart;
          fixedStartTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        }
        
        if (originalEnd) {
          const [_, year, month, day, hour, minute, second] = originalEnd;
          fixedEndTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        }
        
        return {
          ...prog,
          startTime: fixedStartTime,
          endTime: fixedEndTime
        };
      } catch (error) {
        console.error('Failed to fix program times:', error);
        return prog;
      }
    });
  };

  // Remove invalid programs, apply corrections and sort by start time
  const validPrograms = getCorrectedPrograms(programs)
    .filter(program => {
      const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
      const end = moment(program.endTime, 'YYYY-MM-DD HH:mm:ss');
      return start.isValid() && end.isValid() && end.isAfter(start);
    })
    .sort((a, b) => 
      moment(a.startTime, 'YYYY-MM-DD HH:mm:ss').valueOf() - moment(b.startTime, 'YYYY-MM-DD HH:mm:ss').valueOf()
    );

  // Get programs from today
  const todayPrograms = validPrograms.filter(program => {
    const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(program.endTime, 'YYYY-MM-DD HH:mm:ss');
    const today = moment().startOf('day');
    const tomorrow = moment().add(1, 'day').startOf('day');
    return (start.isSameOrAfter(today) && start.isBefore(tomorrow)) || 
           (end.isSameOrAfter(today) && end.isBefore(tomorrow)) ||
           (start.isBefore(today) && end.isAfter(tomorrow));
  });

  // Calculate time remaining for a program
  const getTimeRemaining = (endTime: string) => {
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    const duration = moment.duration(end.diff(currentTime));
    const hours = Math.floor(duration.asHours());
    const minutes = Math.floor(duration.asMinutes() % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}min restantes`;
    }
    return `${minutes} min restantes`;
  };

  // Encontrar o programa que está tocando agora
  const nowPlaying = todayPrograms.find(program => 
    isCurrentlyPlaying(program.startTime, program.endTime)
  );

  // Handle program click to open dialog
  const handleProgramClick = (program: Program, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    setSelectedProgram(program);
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Refresh EPG data for current channel
  const refreshChannelEPG = async () => {
    if (!onRefreshEpg) return;
    
    try {
      setIsRefreshing(true);
      onRefreshEpg();
      
      // Show success animation
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1500);
    } catch (error) {
      console.error('Error refreshing EPG data:', error);
      setIsRefreshing(false);
    }
  };

  if (!todayPrograms.length) {
    return (
      <Box 
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          position: 'relative'
        }}
      >
        {/* Move refresh button to header instead of floating */}
        <Box 
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            mb: 2
          }}
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
            {name || 'Channel Guide'}
          </Typography>
          
          {onRefreshEpg && (
            <Tooltip title="Atualizar EPG deste canal">
              <IconButton 
                sx={{
                  backdropFilter: 'blur(4px)',
                  boxShadow: 1,
                  bgcolor: 'background.paper',
                  '&:hover': {
                    bgcolor: 'action.hover'
                  }
                }}
                onClick={refreshChannelEPG}
                disabled={isRefreshing}
                size="small"
              >
                {isRefreshing ? <CircularProgress size={16} /> : <RefreshIcon fontSize="small" />}
              </IconButton>
            </Tooltip>
          )}
        </Box>
        
        <Alert 
          severity="info"
          icon={<LiveTvIcon />}
          sx={{ 
            width: '100%', 
            '& .MuiAlert-message': {
              width: '100%',
              textAlign: 'center'
            }
          }}
        >
          No program information available for {name || 'this channel'}
        </Alert>
        <Typography variant="caption" color="text.secondary">
          Channel ID: {epgData.id || 'Not available'}
        </Typography>
      </Box>
    );
  }

  const renderProgramGuide = () => (
    <Box sx={{ pr: 1 }}>
      {/* Currently playing program highlight */}
      {nowPlaying && (
        <Paper
          elevation={0}
          onClick={(e) => handleProgramClick(nowPlaying, e)}
          sx={{
            p: 2,
            mb: 2,
            bgcolor: theme.palette.mode === 'dark' 
              ? 'rgba(14, 165, 233, 0.15)' 
              : 'rgba(25, 118, 210, 0.08)',
            borderRadius: 1,
            borderLeft: '4px solid',
            borderColor: theme.palette.mode === 'dark' ? '#0ea5e9' : theme.palette.primary.main,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
            <Chip 
              icon={<PlayArrowIcon />} 
              label="AO VIVO" 
              size="small" 
              color="primary"
              sx={{ mr: 1, height: 24 }}
            />
            <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>
              <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, opacity: 0.7 }} />
              <Typography variant="body2" sx={{ fontWeight: 'medium', opacity: 0.9 }}>
                {formatTime(nowPlaying.startTime)} - {formatTime(nowPlaying.endTime)}
              </Typography>
            </Box>
          </Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            {nowPlaying.title}
          </Typography>
          
          {/* Barra de progresso */}
          <Box sx={{ width: '100%', mt: 1, mb: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
              <Typography variant="caption" color="primary">
                {Math.round(getProgress(nowPlaying.startTime, nowPlaying.endTime))}% completo
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {getTimeRemaining(nowPlaying.endTime)}
              </Typography>
            </Box>
            <ProgressBar 
              startTime={nowPlaying.startTime}
              endTime={nowPlaying.endTime}
              showPercentage={false}
              height={6}
            />
          </Box>
          
          {nowPlaying.description && (
            <Typography variant="body2" color="text.secondary" sx={{ 
              mt: 1,
              display: '-webkit-box',
              overflow: 'hidden',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: 2
            }}>
              {nowPlaying.description}
            </Typography>
          )}
        </Paper>
      )}

      {/* Próximos programas */}
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
        <ArrowForwardIcon fontSize="small" sx={{ mr: 0.5 }} />
        Próximos Programas
      </Typography>
      
      <Box>
        {todayPrograms
          .filter(program => moment(program.startTime, 'YYYY-MM-DD HH:mm:ss').isAfter(currentTime))
          .slice(0, 5)
          .map((program, index) => {
            const duration = getProgramDuration(program.startTime, program.endTime);
            const startTime = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
            const timeUntilStart = moment.duration(startTime.diff(currentTime));
            const minutesUntilStart = Math.floor(timeUntilStart.asMinutes());
            
            return (
              <Paper
                key={index}
                elevation={0}
                onClick={(e) => handleProgramClick(program, e)}
                sx={{
                  p: 1.5,
                  mb: 1,
                  borderRadius: 1,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                  }
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, opacity: 0.7, fontSize: '1rem' }} />
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {formatTime(program.startTime)} - {formatTime(program.endTime)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                      (em {minutesUntilStart} min)
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {Math.round(duration)} min
                  </Typography>
                </Box>
                
                <Typography 
                  variant="subtitle1" 
                  sx={{ 
                    fontWeight: 'medium',
                    lineHeight: 1.3
                  }}
                >
                  {program.title}
                </Typography>
                
                {program.description && (
                  <Typography 
                    variant="body2" 
                    color="text.secondary" 
                    sx={{ 
                      mt: 0.5,
                      display: '-webkit-box',
                      overflow: 'hidden',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: 2
                    }}
                  >
                    {program.description}
                  </Typography>
                )}
              </Paper>
            );
          })}
      </Box>
    </Box>
  );

  // Escolher o modo de visualização adequado
  return (
    <Box 
      sx={{ 
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.paper',
        borderRadius: 1,
        p: 1,
        position: 'relative',
      }}
    >
      {/* Cabeçalho com hora atual */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2,
        position: 'relative'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TodayIcon fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="subtitle2">
            {currentTime.format('DD/MM - HH:mm')}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 'medium', color: theme.palette.primary.main }}>
            Guia de Programação
          </Typography>
          
          {/* Refresh button */}
          {onRefreshEpg && (
            <Tooltip title="Atualizar EPG deste canal">
              <IconButton 
                size="small"
                color="primary"
                onClick={refreshChannelEPG}
                disabled={isRefreshing}
                sx={{
                  ml: 1,
                  boxShadow: 1,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.8)',
                  backdropFilter: 'blur(4px)',
                  '&:hover': {
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.9)',
                  }
                }}
              >
                {isRefreshing ? 
                  <CircularProgress size={16} /> : 
                  <RefreshIcon fontSize="small" />
                }
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Mostrar visualização de programas */}
      {renderProgramGuide()}

      {/* Program Details Dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        TransitionComponent={Slide}
        TransitionProps={{ direction: 'up' } as any}
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2, 
            overflow: 'hidden',
            backgroundImage: theme.palette.mode === 'dark' 
              ? 'linear-gradient(to bottom, rgba(30, 41, 59, 1), rgba(15, 23, 42, 1))' 
              : 'linear-gradient(to bottom, #f8fafc, #f1f5f9)'
          }
        }}
        maxWidth="sm"
        fullWidth
      >
        {selectedProgram && (
          <>
            <DialogTitle sx={{ 
              pb: 1, 
              display: 'flex', 
              justifyContent: 'space-between',
              alignItems: 'center',
              borderBottom: '1px solid',
              borderColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
            }}>
              <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
                {selectedProgram.title}
              </Typography>
              <IconButton 
                edge="end" 
                onClick={handleCloseDialog}
                aria-label="close"
                size="small" 
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent dividers sx={{ p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    {formatTime(selectedProgram.startTime)} - {formatTime(selectedProgram.endTime)}
                  </Typography>
                  <Chip 
                    label={`${Math.round(getProgramDuration(selectedProgram.startTime, selectedProgram.endTime))} min`} 
                    size="small" 
                    variant="outlined"
                    sx={{ ml: 2, height: 24 }}
                  />
                </Box>
                <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                  {formatDate(selectedProgram.startTime)}
                </Typography>
              </Box>
              {isCurrentlyPlaying(selectedProgram.startTime, selectedProgram.endTime) && (
                <Box sx={{ mb: 3, mt: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                    <Typography variant="caption" color="primary">
                      {Math.round(getProgress(selectedProgram.startTime, selectedProgram.endTime))}% completo
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Ao vivo agora
                    </Typography>
                  </Box>
                  <ProgressBar 
                    startTime={selectedProgram.startTime}
                    endTime={selectedProgram.endTime}
                    showPercentage={false}
                    height={6}
                  />
                </Box>
              )}
              <Typography variant="body1" paragraph sx={{ whiteSpace: 'pre-line' }}>
                {selectedProgram.description || 'Nenhuma descrição disponível para este programa.'}
              </Typography>
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 1.5 }}>
              <Button onClick={handleCloseDialog} variant="outlined">
                Fechar
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default EPGGuide; 