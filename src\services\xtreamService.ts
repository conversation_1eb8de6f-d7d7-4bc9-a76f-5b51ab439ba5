import axios from 'axios';
import { dbService } from './dbService';

export interface XtreamSeries {
  num: number;
  name: string;
  series_id: number;
  cover: string;
  plot: string;
  cast: string;
  director: string;
  genre: string;
  releaseDate: string;
  last_modified: string;
  rating: string;
  rating_5based: number;
  backdrop_path: string[];
  youtube_trailer: string;
  episode_run_time: string;
  category_id: string;
}

export interface XtreamSeason {
  air_date: string;
  episode_count: number;
  id: number;
  name: string;
  overview: string;
  season_number: number;
  cover: string;
  cover_big: string;
}

export interface XtreamEpisode {
  id: string;
  episode_num: number;
  title: string;
  container_extension: string;
  info: {
    movie_image: string;
    plot: string;
    duration_secs: number;
    duration: string;
    releasedate: string;
  };
  added: string;
  season: number;
  direct_source: string;
}

class XtreamService {
  private baseUrl: string = '';
  private username: string = '';
  private password: string = '';

  async initialize() {
    try {
      const connection = await dbService.getConnection();
      if (connection) {
        const { url, username, password } = connection;
        this.baseUrl = url;
        this.username = username;
        this.password = password;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao inicializar serviço Xtream:', error);
      return false;
    }
  }

  get apiUrl() {
    return `${this.baseUrl}/player_api.php?username=${this.username}&password=${this.password}`;
  }

  async getAllSeries(): Promise<XtreamSeries[]> {
    try {
      await this.initialize();
      const response = await axios.get(`${this.apiUrl}&action=get_series`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar séries:', error);
      return [];
    }
  }

  async getSeriesByCategoryId(categoryId: string): Promise<XtreamSeries[]> {
    try {
      await this.initialize();
      const response = await axios.get(`${this.apiUrl}&action=get_series&category_id=${categoryId}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar séries por categoria ${categoryId}:`, error);
      return [];
    }
  }

  async getSeriesInfo(seriesId: number): Promise<{ info: XtreamSeries, episodes: Record<string, XtreamEpisode[]> }> {
    try {
      await this.initialize();
      const response = await axios.get(`${this.apiUrl}&action=get_series_info&series_id=${seriesId}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar informações da série ${seriesId}:`, error);
      return { info: {} as XtreamSeries, episodes: {} };
    }
  }

  async getSeriesCategories(): Promise<any[]> {
    try {
      await this.initialize();
      const response = await axios.get(`${this.apiUrl}&action=get_series_categories`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar categorias de séries:', error);
      return [];
    }
  }

  async getVodStreams(): Promise<any[]> {
    try {
      await this.initialize();
      const response = await axios.get(`${this.apiUrl}&action=get_vod_streams`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar VOD streams:', error);
      return [];
    }
  }

  // Método para gerar URL do episódio
  getEpisodeStreamUrl(episodeId: string, seriesId: number): string {
    return `${this.baseUrl}/series/${this.username}/${this.password}/${episodeId}.mp4`;
  }

  // Método para registrar o progresso de visualização
  async saveWatchProgress(seriesId: string, episodeId: string, seasonNumber: number, progress: number, duration: number) {
    try {
      await dbService.saveWatchedSeries({
        id: seriesId,
        progress,
        duration,
        currentEpisode: episodeId,
        seasonNumber,
        lastWatched: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao salvar progresso:', error);
    }
  }

  // Método para obter o progresso de visualização
  async getWatchProgress(seriesId: string) {
    try {
      return await dbService.getWatchedSeries(seriesId);
    } catch (error) {
      console.error('Erro ao obter progresso:', error);
      return null;
    }
  }
}

export const xtreamService = new XtreamService();
