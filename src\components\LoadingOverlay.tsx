import React from 'react';
import { Box, Typography, CircularProgress, useTheme, Paper, keyframes } from '@mui/material';
import LiveTvIcon from '@mui/icons-material/LiveTv';

interface LoadingOverlayProps {
  message: string;
  submessage?: string;
}

const pulse = keyframes`
  0% { opacity: 0.6; transform: scale(0.98); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 0.6; transform: scale(0.98); }
`;

const rotate = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ message, submessage }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        backdropFilter: 'blur(4px)',
      }}
    >
      <Paper
        elevation={4}
        sx={{
          p: 4,
          borderRadius: 3,
          maxWidth: 400,
          textAlign: 'center',
          backgroundColor: theme.palette.mode === 'dark' 
            ? 'rgba(30, 41, 59, 0.95)' 
            : 'rgba(255, 255, 255, 0.95)',
          animation: `${pulse} 2s infinite ease-in-out`,
          border: '1px solid',
          borderColor: theme.palette.mode === 'dark' 
            ? 'rgba(255, 255, 255, 0.1)' 
            : 'rgba(0, 0, 0, 0.1)',
        }}
      >
        <Box sx={{ mb: 3, position: 'relative', mx: 'auto', width: 80, height: 80 }}>
          <CircularProgress
            size={80}
            thickness={3}
            sx={{
              color: theme.palette.primary.main,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              animation: `${rotate} 8s infinite linear`,
            }}
          >
            <LiveTvIcon sx={{ fontSize: 24, color: 'white' }} />
          </Box>
        </Box>

        <Typography variant="h6" fontWeight="bold" mb={1}>
          {message}
        </Typography>

        {submessage && (
          <Typography variant="body2" color="text.secondary" mt={1}>
            {submessage}
          </Typography>
        )}
      </Paper>
    </Box>
  );
};

export default LoadingOverlay;
