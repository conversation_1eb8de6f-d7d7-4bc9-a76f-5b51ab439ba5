import React, { useState, useEffect, useMemo } from 'react';
import { Box, Typography, Link, useTheme, alpha } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import LaunchIcon from '@mui/icons-material/Launch';
import TvIcon from '@mui/icons-material/Tv';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { messageTickerService } from '../services/messageTickerService';
import { handleExternalLinkClick } from '../utils/openExternal';

// Animação de entrada suave
const slideInFromTop = keyframes`
  0% {
    transform: translateY(-30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
`;

// Animação de brilho
const glow = keyframes`
  0%, 100% {
    box-shadow:
      0 0 20px rgba(14, 165, 233, 0.3),
      0 0 40px rgba(14, 165, 233, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(14, 165, 233, 0.5),
      0 0 60px rgba(14, 165, 233, 0.2);
  }
`;

// Animação de pulso para elementos destacados
const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

// Animação de gradiente
const gradientShift = keyframes`
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
`;

// Container principal do banner - com melhor contraste
const BannerContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  maxWidth: '800px',
  margin: '0 auto',
  padding: theme.spacing(2, 3),
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.95)} 0%,
    ${alpha(theme.palette.background.default, 0.95)} 100%)`,
  borderRadius: '20px',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  backdropFilter: 'blur(20px)',
  boxShadow: `
    0 8px 32px ${alpha('#000000', 0.2)},
    0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)},
    inset 0 1px 0 ${alpha('#ffffff', 0.1)}
  `,
  animation: `${slideInFromTop} 0.8s ease-out, ${glow} 4s infinite`,
  overflow: 'hidden',

  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '2px',
    background: `linear-gradient(90deg,
      ${theme.palette.primary.main},
      ${theme.palette.secondary.main},
      ${theme.palette.primary.main})`,
    backgroundSize: '200% 100%',
    animation: `${gradientShift} 3s ease-in-out infinite`,
  },

  // Overlay para melhor contraste
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.8)} 0%,
      ${alpha(theme.palette.background.default, 0.8)} 100%)`,
    borderRadius: '20px',
    zIndex: 0,
  },

  [theme.breakpoints.down('md')]: {
    maxWidth: '95%',
    padding: theme.spacing(1.5, 2),
  },
}));

// Conteúdo principal do banner
const BannerContent = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(1),
  zIndex: 2, // Acima do overlay

  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    textAlign: 'center',
    gap: theme.spacing(1),
  },
}));

// Ícone principal - com melhor contraste
const MainIcon = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '60px',
  height: '60px',
  borderRadius: '50%',
  background: `linear-gradient(135deg,
    ${theme.palette.primary.main},
    ${theme.palette.secondary.main})`,
  boxShadow: `
    0 0 20px ${alpha(theme.palette.primary.main, 0.4)},
    0 4px 12px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.2)
  `,
  animation: `${pulse} 3s infinite`,
  border: `2px solid ${alpha('#ffffff', 0.2)}`,
  position: 'relative',
  zIndex: 1,

  '& .MuiSvgIcon-root': {
    fontSize: '2rem',
    color: '#ffffff',
    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))',
  },
}));

// Texto principal - com melhor contraste
const MainText = styled(Typography)(({ theme }) => ({
  flex: 1,
  fontWeight: 700, // Aumentado para 700
  fontSize: '1.2rem',
  lineHeight: 1.4,
  color: theme.palette.text.primary,
  textShadow: `
    0 2px 4px rgba(0,0,0,0.3),
    0 1px 2px rgba(0,0,0,0.5)
  `, // Sombra mais forte
  marginLeft: theme.spacing(1),
  marginRight: theme.spacing(1),
  position: 'relative',
  zIndex: 1,

  // Adicionar um outline sutil para melhor legibilidade
  WebkitTextStroke: `0.5px ${alpha(theme.palette.background.default, 0.8)}`,

  [theme.breakpoints.down('md')]: {
    fontSize: '1.1rem',
  },

  [theme.breakpoints.down('sm')]: {
    fontSize: '1rem',
    textAlign: 'center',
    marginLeft: 0,
    marginRight: 0,
  },
}));

// Botão de ação - com melhor contraste
const ActionButton = styled(Link)(({ theme }) => ({
  position: 'relative',
  color: '#ffffff',
  textDecoration: 'none !important',
  fontWeight: 700, // Aumentado para 700
  padding: theme.spacing(1, 2),
  borderRadius: '20px',
  background: `linear-gradient(135deg,
    ${theme.palette.primary.main},
    ${theme.palette.secondary.main})`,
  border: `2px solid ${alpha('#ffffff', 0.2)}`, // Borda mais visível
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  display: 'inline-flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  fontSize: '0.9rem',
  cursor: 'pointer !important',
  pointerEvents: 'auto !important',
  zIndex: 10,
  whiteSpace: 'nowrap',
  textShadow: '0 1px 2px rgba(0,0,0,0.5)', // Sombra no texto
  boxShadow: `
    0 4px 12px ${alpha(theme.palette.primary.main, 0.3)},
    0 2px 4px rgba(0,0,0,0.2)
  `,

  '&:hover, &:focus': {
    transform: 'translateY(-2px)',
    boxShadow: `
      0 8px 20px ${alpha(theme.palette.primary.main, 0.4)},
      0 4px 8px rgba(0,0,0,0.3)
    `,
    background: `linear-gradient(135deg,
      ${theme.palette.primary.light},
      ${theme.palette.secondary.light})`,
    border: `2px solid ${alpha('#ffffff', 0.4)}`,
  },

  '&:active': {
    transform: 'translateY(0)',
  },

  [theme.breakpoints.down('sm')]: {
    fontSize: '0.8rem',
    padding: theme.spacing(0.8, 1.5),
  },
}));

// Container de ações (apenas botão)
const ActionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  flexShrink: 0, // Não encolhe para dar mais espaço ao texto

  [theme.breakpoints.down('sm')]: {
    justifyContent: 'center',
  },
}));

interface WelcomeTvDisplayProps {
  className?: string;
}

const WelcomeTvDisplay: React.FC<WelcomeTvDisplayProps> = ({ className }) => {
  const theme = useTheme();
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [config, setConfig] = useState<any>(null);
  const [displayText, setDisplayText] = useState('');

  // Carrega configuração do ticker
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const tickerConfig = await messageTickerService.getConfig(true);
        setConfig(tickerConfig);
      } catch (error) {
        // Usa configuração padrão se falhar
        setConfig({
          enabled: true,
          messages: [
            {
              id: 'welcome',
              text: '🚀 Streaming em 4K, Full HD e HD com qualidade premium',
              link: {
                text: 'Saiba Mais',
                url: 'https://nekotv.vercel.app',
                target: '_blank'
              },
              suffix: '',
              duration: 8,
              enabled: true
            }
          ]
        });
      }
    };

    loadConfig();
  }, []);

  // Mensagens habilitadas
  const enabledMessages = useMemo(() => 
    config?.messages?.filter((msg: any) => msg.enabled !== false) || [], 
    [config]
  );

  // Rotação de mensagens
  useEffect(() => {
    if (enabledMessages.length > 0) {
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % enabledMessages.length);
      }, 8000); // 8 segundos por mensagem

      return () => clearInterval(interval);
    }
  }, [enabledMessages]);

  // Atualiza texto exibido
  useEffect(() => {
    if (enabledMessages.length > 0) {
      const currentMessage = enabledMessages[currentMessageIndex];
      const fullText = `${currentMessage.text}${currentMessage.suffix || ''}`;
      setDisplayText(fullText);
    }
  }, [currentMessageIndex, enabledMessages]);

  // Handler de clique no link
  const handleLinkClick = (url: string, target: string = '_blank') => {
    try {
      handleExternalLinkClick(url, target);
    } catch (error) {
      window.open(url, target, 'noopener,noreferrer');
    }
  };

  if (!config || enabledMessages.length === 0) {
    return null;
  }

  const currentMessage = enabledMessages[currentMessageIndex];

  return (
    <Box className={className} sx={{ py: 1, width: '100%' }}>
      <BannerContainer>
        <BannerContent>
          {/* Ícone principal */}
          <MainIcon>
            <TvIcon />
          </MainIcon>

          {/* Texto principal - agora com mais espaço */}
          <MainText>
            {displayText || '🚀 Streaming em 4K, Full HD e HD com qualidade premium'}
          </MainText>

          {/* Apenas botão de ação - removidos os badges */}
          <ActionContainer>
            {currentMessage?.link && (
              <ActionButton
                href={currentMessage.link.url}
                target={currentMessage.link.target || '_blank'}
                rel="noopener noreferrer"
                onClick={(e) => {
                  e.preventDefault();
                  handleLinkClick(currentMessage.link.url, currentMessage.link.target);
                }}
              >
                <PlayArrowIcon sx={{ fontSize: '1rem' }} />
                {currentMessage.link.text}
                <LaunchIcon sx={{ fontSize: '0.8rem' }} />
              </ActionButton>
            )}
          </ActionContainer>
        </BannerContent>
      </BannerContainer>
    </Box>
  );
};

export default WelcomeTvDisplay;
