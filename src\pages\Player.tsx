import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import { Box, IconButton, Typography, useTheme, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import RotateScreenIcon from '@mui/icons-material/RotateRight';
import Hls from 'hls.js';
import Plyr from 'plyr';
import 'plyr/dist/plyr.css';
import { useResponsive, useFullscreen, useDeviceDetection } from '../hooks/useResponsive';
import LoadingOverlay from '../components/ui/LoadingOverlay';

interface LocationState {
  url?: string;
  item?: {
    title: string;
    url: string;
    type: string;
    group: string;
    logo?: string;
  };
}

// Styled components for responsive design
const PlayerContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '100vh',
  backgroundColor: '#000',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',

  [theme.breakpoints.down('md')]: {
    height: '100vh',
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  }
}));

const PlayerHeader = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 1001,
  background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%)',
  padding: theme.spacing(1, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  transition: 'opacity 0.3s ease',

  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(0.5, 1),
  }
}));

const VideoWrapper = styled(Box)({
  position: 'relative',
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#000',
});

const Player: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<Plyr>();
  const containerRef = useRef<HTMLDivElement>(null);
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as LocationState;

  // Responsive hooks
  const { isMobile, isTablet, isLandscape, isPortrait, isTouchDevice } = useResponsive();
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const { isIOS, isAndroid, isMobileDevice } = useDeviceDetection();

  const [showControls, setShowControls] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Handle fullscreen for mobile devices
  const handleMobileFullscreen = useCallback(async () => {
    if (!containerRef.current) return;

    try {
      await toggleFullscreen(containerRef.current);

      // For mobile devices, also try to lock orientation to landscape for better viewing
      if (isMobileDevice && 'screen' in window && 'orientation' in window.screen) {
        try {
          if (!isFullscreen && isPortrait) {
            // @ts-ignore - orientation API might not be fully typed
            await window.screen.orientation.lock('landscape');
          }
        } catch (orientationError) {
          console.log('Orientation lock not supported or failed:', orientationError);
        }
      }
    } catch (error) {
      console.error('Mobile fullscreen toggle failed:', error);
    }
  }, [isFullscreen, isMobileDevice, isPortrait, toggleFullscreen]);

  // Handle controls visibility
  const showControlsTemporarily = useCallback(() => {
    setShowControls(true);
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    const timeout = setTimeout(() => {
      setShowControls(false);
    }, 3000);
    setControlsTimeout(timeout);
  }, [controlsTimeout]);

  // Handle mouse/touch events for controls
  const handleUserInteraction = useCallback(() => {
    showControlsTemporarily();
  }, [showControlsTemporarily]);

  useEffect(() => {
    if (!videoRef.current) return;

    const video = videoRef.current;

    // Get source from either the URL parameter or from location state
    let source = '';
    if (id) {
      source = decodeURIComponent(id);
    } else if (state?.url) {
      source = state.url;
    } else if (state?.item?.url) {
      source = state.item.url;
    }

    if (!source) {
      console.error('No video source found');
      return;
    }

    // Initialize Plyr with responsive and mobile-optimized settings
    const plyrConfig = {
      controls: isMobile ? [
        'play-large',
        'play',
        'progress',
        'current-time',
        'mute',
        'captions',
        'settings',
        'fullscreen',
      ] : [
        'play-large',
        'play',
        'progress',
        'current-time',
        'mute',
        'volume',
        'captions',
        'settings',
        'pip',
        'airplay',
        'fullscreen',
      ],
      fullscreen: {
        enabled: true,
        fallback: true,
        iosNative: isIOS,
        container: containerRef.current || undefined,
      },
      ratio: isMobile ? '16:9' : null,
      autoplay: false,
      muted: false,
      clickToPlay: true,
      disableContextMenu: false,
      resetOnEnd: false,
      keyboard: {
        focused: !isTouchDevice,
        global: false,
      },
      tooltips: {
        controls: !isMobile,
        seek: !isMobile,
      },
      captions: {
        active: false,
        language: 'auto',
        update: false,
      },
      hideControls: isMobile,
      invertTime: false,
      toggleInvert: true,
    };

    playerRef.current = new Plyr(video, plyrConfig);

    // Add event listeners for player state
    playerRef.current.on('play', () => {
      setIsPlaying(true);
      setIsLoading(false);
    });

    playerRef.current.on('pause', () => {
      setIsPlaying(false);
    });

    playerRef.current.on('waiting', () => {
      setIsLoading(true);
    });

    playerRef.current.on('canplay', () => {
      setIsLoading(false);
    });

    playerRef.current.on('loadstart', () => {
      setIsLoading(true);
    });

    // Setup HLS if the source is an m3u8 stream
    if (source.includes('.m3u8')) {
      if (Hls.isSupported()) {
        const hls = new Hls({
          enableWorker: true,
          lowLatencyMode: true,
          backBufferLength: 90,
        });
        hls.loadSource(source);
        hls.attachMedia(video);
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          // Don't auto-play on mobile to respect user interaction requirements
          if (!isMobile) {
            video.play().catch(console.error);
          }
        });

        // Handle HLS errors
        hls.on(Hls.Events.ERROR, (_, data) => {
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.error('Network error, trying to recover...');
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.error('Media error, trying to recover...');
                hls.recoverMediaError();
                break;
              default:
                console.error('Fatal error, destroying HLS instance...');
                hls.destroy();
                break;
            }
          }
        });

        return () => {
          hls.destroy();
        };
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // For Safari, which has native HLS support
        video.src = source;
        video.addEventListener('loadedmetadata', () => {
          if (!isMobile) {
            video.play().catch(console.error);
          }
        });
      }
    } else {
      // For regular video sources
      video.src = source;
      if (!isMobile) {
        video.play().catch(console.error);
      }
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
      }
    };
  }, [id, location.state, isMobile]);

  // Mobile-specific optimizations
  useEffect(() => {
    if (isMobile && containerRef.current) {
      // Prevent zoom on double tap
      containerRef.current.style.touchAction = 'manipulation';

      // Add viewport meta tag for mobile if not present
      let viewport = document.querySelector('meta[name="viewport"]');
      if (!viewport) {
        viewport = document.createElement('meta');
        viewport.setAttribute('name', 'viewport');
        document.head.appendChild(viewport);
      }
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }
  }, [isMobile]);

  // Cleanup controls timeout
  useEffect(() => {
    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, [controlsTimeout]);

  // Auto-hide controls initially after 3 seconds
  useEffect(() => {
    const timeout = setTimeout(() => {
      setShowControls(false);
    }, 3000);

    return () => clearTimeout(timeout);
  }, []);

  // Determine title based on available data
  const title = state?.item?.title || "IPTV Player";
  const group = state?.item?.group || "";

  return (
    <PlayerContainer
      ref={containerRef}
      onMouseMove={handleUserInteraction}
      onTouchStart={handleUserInteraction}
      onClick={handleUserInteraction}
    >
      {/* Header with back button and title */}
      <PlayerHeader sx={{ opacity: showControls ? 1 : 0 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton
            onClick={() => navigate(-1)}
            sx={{
              color: 'white',
              '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' }
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                fontSize: { xs: '0.9rem', sm: '1.1rem' },
                fontWeight: 600,
                lineHeight: 1.2,
                maxWidth: { xs: '200px', sm: '300px' },
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {title}
            </Typography>
            {group && (
              <Typography
                variant="caption"
                sx={{
                  color: 'rgba(255,255,255,0.7)',
                  fontSize: { xs: '0.7rem', sm: '0.8rem' },
                  display: 'block',
                  maxWidth: { xs: '200px', sm: '300px' },
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {group}
              </Typography>
            )}
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {/* Orientation hint for mobile */}
          {isMobile && isPortrait && (
            <IconButton
              onClick={() => {
                // Suggest rotation for better viewing
                if ('screen' in window && 'orientation' in window.screen) {
                  try {
                    // @ts-ignore
                    window.screen.orientation.lock('landscape').catch(() => {
                      // Fallback: show a toast or hint to rotate manually
                      console.log('Please rotate your device for better viewing');
                    });
                  } catch (e) {
                    console.log('Orientation lock not supported');
                  }
                }
              }}
              sx={{
                color: 'rgba(255,255,255,0.7)',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' }
              }}
            >
              <RotateScreenIcon fontSize="small" />
            </IconButton>
          )}

          <IconButton
            onClick={isMobile ? handleMobileFullscreen : () => toggleFullscreen(containerRef.current)}
            sx={{
              color: 'white',
              '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' }
            }}
          >
            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </IconButton>
        </Box>
      </PlayerHeader>

      {/* Video player */}
      <VideoWrapper>
        <video
          ref={videoRef}
          className="responsive-plyr-video"
          controls={false} // Let Plyr handle controls
          crossOrigin="anonymous"
          playsInline
          webkit-playsinline="true"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
          }}
        />

        {/* Loading overlay */}
        <LoadingOverlay
          show={isLoading}
          message={isLoading ? 'Carregando stream...' : ''}
          variant="player"
          size={isMobile ? 'medium' : 'large'}
        />
      </VideoWrapper>

      <style>{`
        /* Responsive Plyr Video Styles */
        .responsive-plyr-video {
          width: 100% !important;
          height: 100% !important;
          object-fit: contain;
        }

        /* Custom Plyr theme overrides */
        :root {
          --plyr-color-main: #0ea5e9;
          --plyr-video-background: #000;
          --plyr-control-radius: 8px;
        }

        .plyr {
          width: 100% !important;
          height: 100% !important;
        }

        .plyr--video {
          overflow: hidden;
          width: 100% !important;
          height: 100% !important;
        }

        .plyr__video-wrapper {
          width: 100% !important;
          height: 100% !important;
        }

        /* Mobile-optimized controls */
        .plyr--video .plyr__controls {
          padding: 8px 12px !important;
          background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%) !important;
        }

        .plyr--video .plyr__control {
          padding: 8px !important;
          min-height: 44px !important; /* Touch-friendly size */
        }

        .plyr--video .plyr__control svg {
          width: 20px !important;
          height: 20px !important;
        }

        /* Progress bar optimization */
        .plyr--full-ui input[type=range] {
          height: 6px !important;
        }

        .plyr__progress input[type=range] {
          margin: -3px 0 0 !important;
        }

        /* Time display */
        .plyr__time {
          font-size: 14px !important;
          padding: 0 6px !important;
          font-weight: 500 !important;
        }

        /* Volume control for mobile */
        @media (max-width: 768px) {
          .plyr__volume {
            display: none !important;
          }

          .plyr--video .plyr__control {
            min-height: 48px !important;
            padding: 10px !important;
          }

          .plyr--video .plyr__control svg {
            width: 24px !important;
            height: 24px !important;
          }

          .plyr__time {
            font-size: 16px !important;
          }
        }

        /* Fullscreen optimizations */
        .plyr--fullscreen-active {
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          width: 100vw !important;
          height: 100vh !important;
          z-index: 10000 !important;
        }

        .plyr--fullscreen-active .plyr__video-wrapper {
          width: 100vw !important;
          height: 100vh !important;
        }

        .plyr--fullscreen-active video {
          width: 100vw !important;
          height: 100vh !important;
          object-fit: contain !important;
        }

        /* Hide native fullscreen button on mobile since we have our own */
        @media (max-width: 768px) {
          .plyr__control[data-plyr="fullscreen"] {
            display: none !important;
          }
        }

        /* Loading spinner */
        .plyr__poster {
          background-color: #000 !important;
        }

        /* Captions styling */
        .plyr__captions {
          font-size: 16px !important;
          line-height: 1.4 !important;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
        }

        @media (max-width: 768px) {
          .plyr__captions {
            font-size: 18px !important;
            bottom: 80px !important; /* Above controls */
          }
        }

        /* Settings menu mobile optimization */
        .plyr__menu {
          max-height: 60vh !important;
        }

        @media (max-width: 768px) {
          .plyr__menu {
            bottom: 70px !important;
            right: 10px !important;
            max-height: 50vh !important;
          }
        }
      `}</style>
    </PlayerContainer>
  );
};

export default Player; 