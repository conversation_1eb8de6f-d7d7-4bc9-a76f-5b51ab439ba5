/**
 * Função que retorna a URL original sem usar proxy
 */
export function getProxyUrl(originalUrl: string): string {
  // Retornar a URL original sem proxy
  console.log('🔍 Proxy desativado, retornando URL original:', originalUrl);
  return originalUrl;
}

/**
 * Verifica se é um ambiente de produção HTTPS
 */
export function isProductionHTTPS(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Verificar se está rodando no Electron
  const isElectron = window.location.protocol === 'file:' || (window as any).isElectronApp === true || window.process?.versions?.electron;
  
  // Se estiver no Electron, não usar proxy
  if (isElectron) {
    console.log('🖥️ Electron detectado - desativando proxy');
    return false;
  }
  
  const isHTTPS = window.location.protocol === 'https:';
  const isVercel = window.location.hostname.includes('vercel.app') || 
                   window.location.hostname.includes('dist-');
  const isProduction = process.env.NODE_ENV === 'production';
  
  console.log('🔍 isProductionHTTPS check:', {
    protocol: window.location.protocol,
    hostname: window.location.hostname,
    isHTTPS,
    isVercel,
    isProduction,
    isElectron,
    result: isHTTPS || isVercel || isProduction
  });
  
  return isHTTPS || isVercel || isProduction;
}

/**
 * Wrapper para retornar a URL original sem aplicar proxy
 */
export function getSafeUrl(url: string): string {
  console.log('🔍 getSafeUrl chamado com:', url);
  
  // Se já é uma URL de proxy, extrair a URL original
  if (url.includes('railway.app/proxy') || url.includes('localhost:3001/proxy')) {
    try {
      const urlParams = new URL(url).searchParams;
      const originalUrl = urlParams.get('url');
      if (originalUrl) {
        console.log('🔄 Extraindo URL original do proxy:', originalUrl);
        return decodeURIComponent(originalUrl);
      }
    } catch (e) {
      console.error('Erro ao extrair URL original do proxy:', e);
    }
    return url;
  }
  
  // Retornar a URL original sem proxy
  console.log('🔍 Proxy desativado, retornando URL original:', url);
  return url;
}

/**
 * Função vazia que não configura nenhum interceptor de proxy
 */
export function setupGlobalProxyInterceptor(): void {
  console.log('🚀 setupGlobalProxyInterceptor chamado - proxy desativado');
  console.log('🔍 Proxy global desativado, todas as requisições serão feitas diretamente');
  
  // Não faz nada, apenas retorna sem configurar o interceptor
  return;
  
  // Não há mais código de interceptor de proxy
}

/**
 * Remove os interceptors globais (para limpeza se necessário)
 */
export function removeGlobalProxyInterceptor() {
  // Essa função seria mais complexa de implementar corretamente
  // Por enquanto, apenas loga que seria necessário um reload
  console.log('ℹ️ Para remover interceptors, recarregue a página');
}

/**
 * Função de teste para verificar se o proxy está funcionando
 */
export function testProxySystem() {
  console.log('🧪 === TESTE DO SISTEMA DE PROXY ===');
  console.log('Environment:', {
    isProductionHTTPS: isProductionHTTPS(),
    location: typeof window !== 'undefined' ? {
      protocol: window.location.protocol,
      hostname: window.location.hostname,
      href: window.location.href
    } : 'N/A'
  });
  
  const testUrls = [
    'http://***************/movie/aAp2N8/Dv6Qt4/214680.mp4',
    'https://example.com/test.mp4',
    'http://example.com/live/stream.m3u8',
    'https://api.example.com/data'
  ];
  
  testUrls.forEach(url => {
    const safeUrl = getSafeUrl(url);
    console.log(`URL: ${url}`);
    console.log(`Safe: ${safeUrl}`);
    console.log(`Changed: ${url !== safeUrl}`);
    console.log('---');
  });
  
  console.log('🧪 === FIM DO TESTE ===');
}

// Expor função de teste globalmente para debug
if (typeof window !== 'undefined') {
  (window as any).testProxySystem = testProxySystem;
}