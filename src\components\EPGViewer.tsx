import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { EPGInfo, createIPTVService } from '../services/iptvService';

interface EPGViewerProps {
  streamId: string;
  onClose?: () => void;
}

const EPGContainer = styled.div`
  background: rgba(17, 24, 39, 0.95);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.loading, &.error {
    padding: 2rem;
    text-align: center;
    color: #fff;
  }
`;

const EPGHeader = styled.div`
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  h2 {
    margin: 0;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 600;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
  transition: color 0.2s ease;

  &:hover {
    color: #fff;
  }
`;

const ProgramsList = styled.div`
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
`;

const ProgramItem = styled.div<{ isPlaying?: boolean }>`
  background: ${props => props.isPlaying ? 'rgba(34, 197, 94, 0.1)' : 'rgba(255, 255, 255, 0.05)'};
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: background-color 0.2s ease;
  border: ${props => props.isPlaying ? '1px solid rgba(34, 197, 94, 0.2)' : 'none'};

  &:hover {
    background: ${props => props.isPlaying ? 'rgba(34, 197, 94, 0.15)' : 'rgba(255, 255, 255, 0.1)'};
  }
`;

const ProgramTime = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
`;

const TimeRange = styled.div`
  color: #94a3b8;
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;

  .duration {
    color: #64748b;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
`;

const NowPlayingBadge = styled.span`
  background: #22c55e;
  color: #fff;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
`;

const ProgramDetails = styled.div`
  color: #fff;

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 500;
  }
`;

const ProgramCategory = styled.span`
  display: inline-block;
  background: rgba(99, 102, 241, 0.1);
  color: #818cf8;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  margin-bottom: 0.5rem;
`;

const ProgramDescription = styled.p`
  color: #94a3b8;
  font-size: 0.875rem;
  margin: 0.5rem 0;
  line-height: 1.5;
`;

const ArchiveBadge = styled.span<{ hasArchive: boolean }>`
  display: inline-block;
  background: rgba(234, 179, 8, 0.1);
  color: #fbbf24;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
`;

const LoadingSpinner = styled.div`
  color: #60a5fa;
`;

const ErrorMessage = styled.div`
  color: #f87171;
`;

const NoData = styled.div`
  text-align: center;
  color: #94a3b8;
  padding: 2rem;
`;

export const EPGViewer: React.FC<EPGViewerProps> = ({ streamId, onClose }) => {
  const [programs, setPrograms] = useState<EPGInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadEPG();
  }, [streamId]);

  const loadEPG = async () => {
    try {
      setLoading(true);
      setError(null);
      const iptvService = createIPTVService();
      const epgData = await iptvService.getEPG(streamId);
      setPrograms(epgData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load EPG data');
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (start: number, end: number) => {
    const startDate = new Date(start * 1000);
    const endDate = new Date(end * 1000);
    return `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
  };

  if (loading) {
    return (
      <EPGContainer>
        <LoadingSpinner>Loading EPG data...</LoadingSpinner>
      </EPGContainer>
    );
  }

  if (error) {
    return (
      <EPGContainer>
        <ErrorMessage>{error}</ErrorMessage>
      </EPGContainer>
    );
  }

  if (!programs || programs.length === 0) {
    return (
      <EPGContainer>
        <NoData>No program information available</NoData>
      </EPGContainer>
    );
  }

  return (
    <EPGContainer>
      <EPGHeader>
        <h2>Program Guide</h2>
        {onClose && (
          <CloseButton onClick={onClose}>&times;</CloseButton>
        )}
      </EPGHeader>
      
      <ProgramsList>
        {programs.map((program) => (
          <ProgramItem 
            key={`${program.id}-${program.start_timestamp}`}
            isPlaying={program.now_playing}
          >
            <ProgramTime>
              <TimeRange>
                {formatDuration(program.start_timestamp, program.stop_timestamp)}
              </TimeRange>
              {program.now_playing && (
                <NowPlayingBadge>Now Playing</NowPlayingBadge>
              )}
            </ProgramTime>
            
            <ProgramDetails>
              <h3>{program.title}</h3>
              {program.category && (
                <ProgramCategory>{program.category}</ProgramCategory>
              )}
              {program.description && (
                <ProgramDescription>{program.description}</ProgramDescription>
              )}
              {program.has_archive && (
                <ArchiveBadge hasArchive={true}>Available in Archive</ArchiveBadge>
              )}
            </ProgramDetails>
          </ProgramItem>
        ))}
      </ProgramsList>
    </EPGContainer>
  );
};