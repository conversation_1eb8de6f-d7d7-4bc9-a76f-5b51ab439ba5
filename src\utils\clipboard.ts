// Clipboard utility that works in both Electron and web browsers
// This replaces expo-clipboard functionality

declare global {
  interface Window {
    electronAPI?: {
      clipboard: {
        writeText: (text: string) => Promise<boolean>;
        readText: () => Promise<string>;
      };
    };
    electron?: {
      isElectron: boolean;
      platform: string;
    };
  }
}

export const Clipboard = {
  /**
   * Write text to clipboard
   * @param text - Text to copy to clipboard
   * @returns Promise<boolean> - Success status
   */
  async setStringAsync(text: string): Promise<boolean> {
    try {
      // Check if running in Electron
      if (window.electronAPI?.clipboard) {
        return await window.electronAPI.clipboard.writeText(text);
      }
      
      // Fallback to web API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      }
      
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      return success;
    } catch (error) {
      console.error('Failed to copy text to clipboard:', error);
      return false;
    }
  },

  /**
   * Read text from clipboard
   * @returns Promise<string> - Text from clipboard
   */
  async getStringAsync(): Promise<string> {
    try {
      // Check if running in Electron
      if (window.electronAPI?.clipboard) {
        return await window.electronAPI.clipboard.readText();
      }
      
      // Fallback to web API
      if (navigator.clipboard && window.isSecureContext) {
        return await navigator.clipboard.readText();
      }
      
      // For older browsers, we can't read from clipboard for security reasons
      console.warn('Clipboard read not supported in this environment');
      return '';
    } catch (error) {
      console.error('Failed to read text from clipboard:', error);
      return '';
    }
  },

  /**
   * Check if clipboard functionality is available
   * @returns boolean
   */
  isAvailable(): boolean {
    return !!(
      window.electronAPI?.clipboard ||
      (navigator.clipboard && window.isSecureContext) ||
      document.queryCommandSupported?.('copy')
    );
  }
};

export default Clipboard;
