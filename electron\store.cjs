// Configuração do electron-store para persistência de dados
const Store = require('electron-store');

// Definir o schema para validação dos dados
const schema = {
  license: {
    type: 'object',
    properties: {
      deviceId: { type: 'string' },
      licenseKey: { type: 'string' },
      active: { type: 'boolean' },
      valid: { type: 'boolean' },
      iptvUrl: { type: 'string' },
      iptv_url: { type: 'string' },
      name: { type: 'string' },
      message: { type: 'string' },
      createdAt: { type: ['number', 'null'] },
      activatedAt: { type: ['number', 'null'] },
      lastConfigUpdate: { type: ['number', 'null'] }
    }
  }
};

// Criar instância do store com criptografia para dados sensíveis
const store = new Store({
  name: 'neko-tv-data', // Nome do arquivo de armazenamento
  schema, // Schema para validação
  encryptionKey: 'neko-tv-secure-storage', // Chave de criptografia básica
  clearInvalidConfig: true, // Limpar configuração inválida
  migrations: {
    // Migração para versões futuras se necessário
    '1.0.0': store => {
      // Exemplo de migração
    }
  }
});

module.exports = store;