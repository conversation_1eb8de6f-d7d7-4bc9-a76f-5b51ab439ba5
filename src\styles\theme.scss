// Modern Color Palette
$primary: #2563eb;
$secondary: #64748b;
$success: #22c55e;
$danger: #ef4444;
$warning: #f59e0b;
$info: #3b82f6;
$dark: #1e293b;
$light: #f8fafc;

// Background Colors
$bg-primary: #ffffff;
$bg-secondary: #f1f5f9;
$bg-dark: #0f172a;

// Text Colors
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #f8fafc;

// Gradients
$gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
$gradient-dark: linear-gradient(135deg, #1e293b 0%, #334155 100%);

// Shadows
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

// Border Radius
$radius-sm: 0.375rem;
$radius-md: 0.5rem;
$radius-lg: 0.75rem;
$radius-xl: 1rem;

// Spacing
$spacing-xs: 0.5rem;
$spacing-sm: 1rem;
$spacing-md: 1.5rem;
$spacing-lg: 2rem;
$spacing-xl: 3rem;

// Typography
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-2xl: 1.5rem;
$font-size-3xl: 1.875rem;

// Transitions
$transition-fast: 150ms ease;
$transition-normal: 250ms ease;
$transition-slow: 350ms ease;

// Container Widths
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;

// Z-index
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal: 1040;
$z-popover: 1050;
$z-tooltip: 1060;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin card {
  background: $bg-primary;
  border-radius: $radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-md;
  transition: transform $transition-normal, box-shadow $transition-normal;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

@mixin button-base {
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-md;
  font-weight: 500;
  transition: all $transition-fast;
  cursor: pointer;
  border: none;
  outline: none;
  
  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

@mixin button-primary {
  @include button-base;
  background: $gradient-primary;
  color: $text-light;
  
  &:hover {
    box-shadow: 0 4px 12px rgba($primary, 0.25);
  }
}

@mixin button-danger {
  @include button-base;
  background: $danger;
  color: $text-light;
  
  &:hover {
    box-shadow: 0 4px 12px rgba($danger, 0.25);
  }
}

// Responsive Breakpoints
@mixin responsive($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: 640px) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: 768px) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: 1024px) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: 1280px) { @content; }
  }
} 