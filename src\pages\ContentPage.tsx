import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Typography, CircularProgress, Box } from '@mui/material';
import CategoryList from '../components/CategoryList';
import StreamList from '../components/StreamList';
import SeriesList from '../components/SeriesList';
import MovieList from '../components/MovieList/MovieList';
import { Category, Stream, createIPTVService, IPTVService } from '../services/iptvService';

interface ContentPageProps {
  type: 'channels' | 'movies' | 'series';
}

const ContentPage: React.FC<ContentPageProps> = ({ type }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [streams, setStreams] = useState<Stream[]>([]);
  const [iptvService, setIptvService] = useState<IPTVService | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initService = async () => {
      try {
        const service = await createIPTVService();
        setIptvService(service);
        setError(null);
      } catch (error) {
        console.error('Error initializing IPTV service:', error);
        setError('Failed to initialize IPTV service. Please check your connection.');
        navigate('/', { replace: true });
      }
    };
    initService();
  }, [navigate]);

  useEffect(() => {
    if (iptvService) {
      loadCategories();
    }
  }, [type, iptvService]);

  const getTitle = () => {
    switch (type) {
      case 'channels':
        return 'TV Ao Vivo';
      case 'movies':
        return 'Filmes';
      case 'series':
        return 'Séries';
      default:
        return '';
    }
  };

  const loadCategories = async () => {
    if (!iptvService) return;
    
    try {
      setLoading(true);
      let categories: Category[];
      switch (type) {
        case 'channels':
          categories = await iptvService.getLiveCategories();
          break;
        case 'movies':
          categories = await iptvService.getMovieCategories();
          break;
        case 'series':
          categories = await iptvService.getSeriesCategories();
          break;
        default:
          categories = [];
      }
      
      setCategories(categories);
      if (categories.length > 0) {
        setSelectedCategoryId(categories[0].id);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedCategoryId && iptvService) {
      loadStreams(selectedCategoryId);
    }
  }, [selectedCategoryId, iptvService]);

  const loadStreams = async (categoryId: string) => {
    if (!iptvService) return;
    
    try {
      setLoading(true);
      let categoryStreams: Stream[];
      switch (type) {
        case 'channels':
          categoryStreams = await iptvService.getLiveStreams(categoryId);
          break;
        case 'movies':
          categoryStreams = await iptvService.getMovieStreams(categoryId);
          break;
        case 'series':
          categoryStreams = await iptvService.getSeriesStreams(categoryId);
          break;
        default:
          categoryStreams = [];
      }
      
      setStreams(categoryStreams);
    } catch (error) {
      console.error('Error loading streams:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSeriesSelect = async (series: Stream) => {
    try {
      console.log('Selected series:', series);
    } catch (error) {
      console.error('Error loading series episodes:', error);
    }
  };

  const handleLoadSeasons = async (seriesId: string) => {
    if (!iptvService) return [];
    try {
      return await iptvService.getSeriesSeasons(seriesId);
    } catch (error) {
      console.error('Error loading seasons:', error);
      return [];
    }
  };

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (!iptvService) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Inicializando serviço...</Typography>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {getTitle()}
      </Typography>
      
      <Box display="flex" gap={2}>
        <Box sx={{ width: 240, flexShrink: 0 }}>
          <CategoryList
            categories={categories}
            selectedCategoryId={selectedCategoryId}
            onCategorySelect={setSelectedCategoryId}
          />
        </Box>
        
        <Box sx={{ flex: 1 }}>
          {type === 'series' ? (
            <SeriesList
              series={streams}
              onSeriesSelect={handleSeriesSelect}
              onLoadSeasons={handleLoadSeasons}
            />
          ) : type === 'movies' ? (
            <MovieList movies={streams} />
          ) : (
            <StreamList streams={streams} />
          )}
        </Box>
      </Box>
    </Container>
  );
};

export default ContentPage; 