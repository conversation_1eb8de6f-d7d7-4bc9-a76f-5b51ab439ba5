const fs = require('fs');
const path = require('path');

// Script de pré-build para garantir estrutura correta no Vercel
console.log('🔧 Executando prebuild para Vercel...');
console.log('📍 Diretório atual:', process.cwd());
console.log('📍 __dirname:', __dirname);

// Verificar se estamos no ambiente Vercel
const isVercel = process.env.VERCEL === '1';
console.log(isVercel ? '☁️ Executando no Vercel' : '💻 Executando localmente');

try {
  // Garantir que o diretório public existe e tem a estrutura correta
  const publicDir = path.join(__dirname, '..', 'public');
  const iconsDir = path.join(publicDir, 'icons');
  
  console.log('📁 Verificando diretório public:', publicDir);
  
  // Criar diretórios se não existirem
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
    console.log('✅ Diretório public criado');
  } else {
    console.log('✅ Diretório public já existe');
  }
  
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
    console.log('✅ Diretório icons criado');
  } else {
    console.log('✅ Diretório icons já existe');
  }
  
  // Verificar se arquivos importantes estão presentes
  const requiredFiles = [
    'public/manifest.json',
    'public/service-worker.js'
  ];
  
  // Logo é opcional no Vercel
  const optionalFiles = [
    'public/logo-neko-tv.png'
  ];
  
  const missingFiles = [];
  const missingOptionalFiles = [];
  
  requiredFiles.forEach(file => {
    const fullPath = path.join(__dirname, '..', file);
    if (!fs.existsSync(fullPath)) {
      missingFiles.push(file);
    } else {
      console.log('✅ Arquivo encontrado:', file);
    }
  });
  
  optionalFiles.forEach(file => {
    const fullPath = path.join(__dirname, '..', file);
    if (!fs.existsSync(fullPath)) {
      missingOptionalFiles.push(file);
    } else {
      console.log('✅ Arquivo opcional encontrado:', file);
    }
  });
  
  if (missingFiles.length > 0) {
    console.log('⚠️ Arquivos obrigatórios faltando:', missingFiles);
  }
  
  if (missingOptionalFiles.length > 0) {
    console.log('ℹ️ Arquivos opcionais faltando:', missingOptionalFiles);
  }
  
  // Verificar e criar service-worker.js se não existir
  const swPath = path.join(publicDir, 'service-worker.js');
  if (!fs.existsSync(swPath)) {
    console.log('📝 Criando service-worker.js...');
    const swContent = `// Service Worker para IPTV Player
const CACHE_NAME = 'iptv-player-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
      .catch(err => console.log('Cache failed:', err))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
      .catch(err => console.log('Fetch failed:', err))
  );
});`;
    
    fs.writeFileSync(swPath, swContent);
    console.log('✅ Service worker criado');
  } else {
    console.log('✅ Service worker já existe');
  }
  
  // Verificar e criar manifest.json se não existir
  const manifestPath = path.join(publicDir, 'manifest.json');
  if (!fs.existsSync(manifestPath)) {
    console.log('📝 Criando manifest.json...');
    const manifestContent = {
      "name": "Neko TV - IPTV Player",
      "short_name": "Neko TV",
      "description": "Seu player IPTV favorito",
      "start_url": "/",
      "display": "standalone",
      "background_color": "#000000",
      "theme_color": "#007bff",
      "icons": [
        {
          "src": "/icons/icon-192.png",
          "sizes": "192x192",
          "type": "image/png"
        }
      ]
    };
    
    fs.writeFileSync(manifestPath, JSON.stringify(manifestContent, null, 2));
    console.log('✅ Manifest.json criado');
  } else {
    console.log('✅ Manifest.json já existe');
  }
  
  // Não criar logo se não existir (pode ser muito grande para o Vercel)
  // Em vez disso, apenas avisar
  const logoPath = path.join(publicDir, 'logo-neko-tv.png');
  if (!fs.existsSync(logoPath)) {
    console.log('ℹ️ Logo não encontrado - isso é OK para o Vercel');
  } else {
    const stats = fs.statSync(logoPath);
    console.log(`✅ Logo encontrado (${(stats.size / 1024 / 1024).toFixed(2)}MB)`);
  }
  
  // Verificar estrutura de ícones necessária
  const iconFiles = ['icon-48.png', 'icon-144.png', 'icon-192.png'];
  iconFiles.forEach(iconFile => {
    const iconPath = path.join(iconsDir, iconFile);
    if (!fs.existsSync(iconPath)) {
      console.log(`ℹ️ Ícone ${iconFile} não encontrado - criando placeholder`);
      // Criar um pequeno placeholder PNG (1x1 pixel transparente)
      const placeholder = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
        0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41,
        0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00,
        0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
        0x42, 0x60, 0x82
      ]);
      
      try {
        fs.writeFileSync(iconPath, placeholder);
        console.log(`✅ Placeholder criado para ${iconFile}`);
      } catch (err) {
        console.log(`⚠️ Erro ao criar placeholder para ${iconFile}:`, err.message);
      }
    } else {
      console.log(`✅ Ícone ${iconFile} já existe`);
    }
  });
  
  console.log('✅ Prebuild concluído com sucesso!');
  
} catch (error) {
  console.error('❌ Erro no prebuild:', error.message);
  
  // No Vercel, não falhar o build por problemas no prebuild
  if (isVercel) {
    console.log('⚠️ Continuando build no Vercel apesar do erro no prebuild...');
    process.exit(0);
  } else {
    process.exit(1);
  }
} 