import React, { useState, useEffect } from 'react';
import { 
  Card,
  Typography,
  Box, 
  CardContent,
  Container, 
  Paper,
  Button, 
  CircularProgress,
  useTheme,
  useMediaQuery,
  alpha,
  TextField,
  Backdrop,
  Fade,
  IconButton,
  Tooltip
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { deviceService } from '../services/deviceService';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import RefreshIcon from '@mui/icons-material/Refresh';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import LockIcon from '@mui/icons-material/Lock';
import DoneIcon from '@mui/icons-material/Done';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { useNavigate } from 'react-router-dom';
import { dbService } from '../services/dbService';
import { createIPTVService } from '../services/iptvService';
import { handleExternalLinkClick, createExternalLinkProps } from '../utils/openExternal';

const MotionCard = motion.create(Card);
const MotionBox = motion.create(Box);

const Access: React.FC<{ setUserInfo?: any, setServerInfo?: any }> = ({ setUserInfo, setServerInfo }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  
  const [deviceId, setDeviceId] = useState<string>('');
  const [copied, setCopied] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');
  const [isError, setIsError] = useState<boolean>(false);
  const [licenseKey, setLicenseKey] = useState<string>('');
  const [showManualLogin, setShowManualLogin] = useState<boolean>(false);
  const [manualUrl, setManualUrl] = useState<string>('');
  const [manualUsername, setManualUsername] = useState<string>('');
  const [manualPassword, setManualPassword] = useState<string>('');
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loginSuccess, setLoginSuccess] = useState<boolean>(false);

  useEffect(() => {
    // Função assíncrona para inicialização
    const initializeDevice = async () => {
      // Obter o ID do dispositivo
      try {
        const id = deviceService.getDeviceId();
        setDeviceId(id);
      } catch (error) {
        console.error('Erro ao obter ID do dispositivo:', error);
        setDeviceId('ID não disponível');
      }
      
      // Verificar licença automaticamente apenas se já existir uma salva
      const savedLicense = await deviceService.loadLicense();
      if (savedLicense && savedLicense.active) {
        checkLicense();
      }
    };
    
    // Executar a função de inicialização
    initializeDevice();
  }, []);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(deviceId).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }).catch(err => {
      console.error('Erro ao copiar para área de transferência:', err);
    });
  };
  
  const checkLicense = async () => {
    setIsChecking(true);
    setMessage('Verificando licença...');
    setIsError(false);
    
    try {
      // Verificar se há uma licença para este dispositivo
      const result = await deviceService.checkActivation();
      
      if (result && result.valid) {
        setMessage('Licença encontrada! Redirecionando...');
        // Definir server info com URL do IPTV se existir
        if (result.iptvUrl) {
          const iptvUrl = result.iptvUrl;
          
          // Extrair username e password da URL se existirem
          let username = '';
          let password = '';
          let serverBaseUrl = '';
          
          try {
            const urlObj = new URL(iptvUrl);
            const params = new URLSearchParams(urlObj.search);
            if (params.has('username')) username = params.get('username') || '';
            if (params.has('password')) password = params.get('password') || '';
            
            // Extrair apenas o domínio base para exibição
            serverBaseUrl = urlObj.origin;
          } catch (error) {
            console.error('Erro ao extrair credenciais da URL:', error);
            serverBaseUrl = iptvUrl; // Fallback
          }
          
          // Salvar a URL COMPLETA (não modificada) na conexão 
          // para garantir que diferentes listas no mesmo servidor sejam tratadas como entidades separadas
          await dbService.setConnection({
            url: iptvUrl, // URL completa original
            username: username,
            password: password,
            type: 'url',
            format: 'm3u',
            autoLogin: true,
            lastUsed: Date.now()
          });
          
          if (setServerInfo) {
            // Para exibição, mostrar apenas o domínio base
            setServerInfo({
              url: serverBaseUrl,
              username: username,
              password: password,
              name: result.name || 'Neko TV'
            });
          }
          
          // Tentar obter informações do usuário do servidor IPTV
          try {
            // Criar serviço temporário para verificar
            const iptvService = await createIPTVService();
            const userInfoResponse = await iptvService.getUserInfo();
            
            if (userInfoResponse && userInfoResponse.user_info) {
              const { user_info } = userInfoResponse;
              console.log('Obtidas informações do usuário IPTV:', user_info);
              
              if (setUserInfo) {
                setUserInfo({
                  username: user_info.username || '',
                  password: user_info.password || '',
                  status: user_info.status || 'active',
                  message: user_info.message || '',
                  exp_date: user_info.exp_date || '',
                  is_trial: user_info.is_trial || '0',
                  active_cons: user_info.active_cons || '0',
                  max_connections: user_info.max_connections || '1',
                  created_at: user_info.created_at || '',
                  allowed_output_formats: user_info.allowed_output_formats || [],
                  auth: user_info.auth || 1,
                  formattedCreatedAt: user_info.formattedCreatedAt,
                  formattedExpDate: user_info.formattedExpDate,
                  isExpired: user_info.isExpired
                });
              }
            }
          } catch (iptvError) {
            console.error('Erro ao obter informações do servidor IPTV:', iptvError);
          }
          
          // Redirecionar para a página principal de conteúdo
          setTimeout(() => {
            navigate('/content');
          }, 1500);
        } else {
          setMessage('Licença válida, mas sem URL configurada.');
          setIsError(true);
        }
      } else {
        setMessage(result.message || 'Nenhuma licença encontrada para este dispositivo.');
        setIsError(true);
      }
    } catch (error) {
      console.error('Erro ao verificar licença:', error);
      setMessage('Erro ao verificar licença. Tente novamente.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };

  const fetchLicenseByKey = async (key: string) => {
    setIsChecking(true);
    setMessage('Buscando licença...');
    setIsError(false);
    
    // Validação rigorosa da chave - no mínimo 8 caracteres e sem espaços extras
    const trimmedKey = key.trim();
    if (!trimmedKey || trimmedKey.length < 8) {
      setMessage('Código de licença inválido ou muito curto. Por favor, verifique e tente novamente.');
      setIsError(true);
      setIsChecking(false);
      return;
    }
    
    try {
      console.log(`Buscando licença exata com chave: "${trimmedKey}"`);
      const license = await deviceService.fetchLicenseByKey(trimmedKey);
      
      // Log do resultado para depuração
      console.log('Resultado da busca por chave:', license);
      
      if (!license) {
        setMessage('Licença não encontrada. Verifique o código e tente novamente.');
        setIsError(true);
        setIsChecking(false);
        return;
      }
      
      if (!license.active) {
        setMessage('Esta licença não está ativa. Entre em contato com o suporte.');
        setIsError(true);
        setIsChecking(false);
        return;
      }
      
      setMessage('Licença encontrada! Configurando aplicativo...');
      
      const iptvUrl = license.iptv_url || license.iptvUrl || '';
      
      if (!iptvUrl) {
        setMessage('Licença encontrada, mas sem URL de IPTV configurada.');
        setIsError(true);
        setIsChecking(false);
        return;
      }
      
      // Extrair username e password da URL se existirem
      let username = '';
      let password = '';
      let serverBaseUrl = '';
      
      try {
        const urlObj = new URL(iptvUrl);
        const params = new URLSearchParams(urlObj.search);
        if (params.has('username')) username = params.get('username') || '';
        if (params.has('password')) password = params.get('password') || '';
        
        // Extrair apenas o domínio base para exibição
        serverBaseUrl = urlObj.origin;
      } catch (error) {
        console.error('Erro ao extrair credenciais da URL:', error);
        serverBaseUrl = iptvUrl; // Fallback
      }
      
      // Salvar a URL COMPLETA (não modificada) na conexão 
      // para garantir que diferentes listas no mesmo servidor sejam tratadas como entidades separadas
      await dbService.setConnection({
        url: iptvUrl, // URL completa original
        username: username,
        password: password,
        type: 'url',
        format: 'm3u',
        autoLogin: true,
        lastUsed: Date.now()
      });

      // Configurar informações do servidor IPTV
      if (setServerInfo) {
        // Para exibição, mostrar apenas o domínio base
        setServerInfo({
          url: serverBaseUrl,
          username: username,
          password: password,
          name: license.name || 'Neko TV'
        });
      }
      
      // Tentar obter informações do usuário do servidor IPTV
      try {
        // Criar serviço temporário para verificar
        const iptvService = await createIPTVService();
        const userInfoResponse = await iptvService.getUserInfo();
        
        if (userInfoResponse && userInfoResponse.user_info) {
          const { user_info } = userInfoResponse;
          console.log('Obtidas informações do usuário IPTV:', user_info);
          
          if (setUserInfo) {
            setUserInfo({
              username: user_info.username || '',
              password: user_info.password || '',
              status: user_info.status || 'active',
              message: user_info.message || '',
              exp_date: user_info.exp_date || '',
              is_trial: user_info.is_trial || '0',
              active_cons: user_info.active_cons || '0',
              max_connections: user_info.max_connections || '1',
              created_at: user_info.created_at || '',
              allowed_output_formats: user_info.allowed_output_formats || [],
              auth: user_info.auth || 1,
              formattedCreatedAt: user_info.formattedCreatedAt,
              formattedExpDate: user_info.formattedExpDate,
              isExpired: user_info.isExpired
            });
          }
        }
      } catch (iptvError) {
        console.error('Erro ao obter informações do servidor IPTV:', iptvError);
      }
      
      console.log('Conexão IPTV configurada:', iptvUrl);
      
      // Redirecionar para a página principal de conteúdo
      setTimeout(() => {
        navigate('/content');
      }, 1500);
    } catch (error) {
      console.error('Erro ao buscar licença:', error);
      setMessage('Erro ao buscar licença. Verifique sua conexão.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };

  const fetchLicenseByClientName = async (clientName: string) => {
    try {
      console.log('Buscando licença para cliente:', clientName);
      const license = await deviceService.fetchLicenseByClientName(clientName);
      
      if (license && license.active) {
        setMessage('Licença encontrada! Configurando aplicativo...');
        
        const iptvUrl = license.iptv_url || license.iptvUrl || '';
        
        if (!iptvUrl) {
          console.log('Licença encontrada para cliente, mas sem URL de IPTV configurada.');
        return;
      }
      
        // Extrair username e password da URL se existirem
        let username = '';
        let password = '';
        let serverBaseUrl = '';
        
        try {
          const urlObj = new URL(iptvUrl);
          const params = new URLSearchParams(urlObj.search);
          if (params.has('username')) username = params.get('username') || '';
          if (params.has('password')) password = params.get('password') || '';
          
          // Extrair apenas o domínio base para exibição
          serverBaseUrl = urlObj.origin;
        } catch (error) {
          console.error('Erro ao extrair credenciais da URL:', error);
          serverBaseUrl = iptvUrl; // Fallback
        }
        
        // Salvar a URL COMPLETA (não modificada) na conexão 
        // para garantir que diferentes listas no mesmo servidor sejam tratadas como entidades separadas
        await dbService.setConnection({
          url: iptvUrl, // URL completa original
          username: username,
          password: password,
          type: 'url',
          format: 'm3u',
          autoLogin: true,
          lastUsed: Date.now()
        });

        // Configurar informações do servidor IPTV
        if (setServerInfo) {
          // Para exibição, mostrar apenas o domínio base
          setServerInfo({
            url: serverBaseUrl,
            username: username,
            password: password,
            name: license.name || 'Neko TV'
          });
        }
        
        // Tentar obter informações do usuário do servidor IPTV
        try {
          // Criar serviço temporário para verificar
      const iptvService = await createIPTVService();
          const userInfoResponse = await iptvService.getUserInfo();
          
          if (userInfoResponse && userInfoResponse.user_info) {
            const { user_info } = userInfoResponse;
            console.log('Obtidas informações do usuário IPTV:', user_info);
            
            if (setUserInfo) {
              setUserInfo({
                username: user_info.username || '',
                password: user_info.password || '',
                status: user_info.status || 'active',
                message: user_info.message || '',
                exp_date: user_info.exp_date || '',
                is_trial: user_info.is_trial || '0',
                active_cons: user_info.active_cons || '0',
                max_connections: user_info.max_connections || '1',
                created_at: user_info.created_at || '',
                allowed_output_formats: user_info.allowed_output_formats || [],
                auth: user_info.auth || 1,
                formattedCreatedAt: user_info.formattedCreatedAt,
                formattedExpDate: user_info.formattedExpDate,
                isExpired: user_info.isExpired
              });
            }
          }
        } catch (iptvError) {
          console.error('Erro ao obter informações do servidor IPTV:', iptvError);
        }
        
        console.log('Conexão IPTV configurada via nome de cliente:', iptvUrl);
        
        // Redirecionar para a página principal de conteúdo
        setTimeout(() => {
        navigate('/content');
        }, 1000);
      }
    } catch (error) {
      console.error('Erro ao buscar licença por nome de cliente:', error);
    }
  };

  const handleLicenseKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLicenseKey(e.target.value);
  };

  const handleActivateWithKey = () => {
    // Validação adicional antes de enviar para processamento
    const trimmedKey = licenseKey.trim();
    
    if (!trimmedKey) {
      setMessage('Por favor, digite uma chave de licença válida.');
      setIsError(true);
      return;
    }
    
    if (trimmedKey.length < 8) {
      setMessage('O código de licença parece incompleto. Verifique e tente novamente.');
      setIsError(true);
      return;
    }
    
    // Verificar se contém caracteres inválidos
    if (!/^[A-Za-z0-9\-]+$/.test(trimmedKey)) {
      setMessage('O código de licença contém caracteres inválidos. Use apenas letras, números e hífen.');
      setIsError(true);
      return;
    }
    
    // Se passar em todas as validações, continuar com o processo
    fetchLicenseByKey(trimmedKey);
  };

  const handleManualLogin = async () => {
    setIsChecking(true);
    setMessage('Conectando...');
    setIsError(false);

    try {
      // Validar URL
      if (!manualUrl || manualUrl.trim().length === 0) {
        setMessage('Por favor, insira uma URL válida.');
        setIsError(true);
        return;
      }

      // Salvar a conexão primeiro
      await dbService.setConnection({
        url: manualUrl.trim(),
        username: manualUsername || '',
        password: manualPassword || '',
        type: 'url',
        format: 'm3u',
        autoLogin: true,
        lastUsed: Date.now()
      });

      // Agora criar o serviço IPTV e testar
      const iptvService = await createIPTVService();
      const userInfo = await iptvService.getUserInfo();

      if (userInfo && userInfo.user_info) {
        setMessage('Conectado com sucesso! Redirecionando...');
        setLoginSuccess(true);
        
        // Atualizar as informações do usuário e servidor se as props existirem
        if (setUserInfo) {
          setUserInfo(userInfo.user_info);
        }
        
        if (setServerInfo) {
          setServerInfo({
            url: manualUrl.trim(),
            username: manualUsername || '',
            password: manualPassword || ''
          });
        }

        // Feedback visual de sucesso com animação
        const successTimeout = setTimeout(() => {
          navigate('/content');
        }, 2000);

        return () => clearTimeout(successTimeout);
      } else {
        setMessage('Falha na conexão. Verifique os dados e tente novamente.');
        setIsError(true);
      }
    } catch (error) {
      console.error('Erro no login manual:', error);
      setMessage('Erro ao tentar conectar. Verifique a URL e as credenciais.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };

  // Efeitos de decoração para o fundo
  const renderBackgroundEffects = () => (
    <>
      {/* Círculos decorativos */}
      <Box
        component={motion.div}
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.6 }}
        transition={{ duration: 2, ease: "easeOut" }}
        sx={{
          position: 'absolute',
          top: '15%',
          right: '10%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(33,150,243,0.2) 0%, rgba(33,150,243,0) 70%)',
          filter: 'blur(30px)',
          zIndex: 0,
        }}
      />
      
      <Box
        component={motion.div}
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.5 }}
        transition={{ duration: 2, delay: 0.5, ease: "easeOut" }}
        sx={{
          position: 'absolute',
          bottom: '15%',
          left: '10%',
          width: '250px',
          height: '250px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(156,39,176,0.2) 0%, rgba(156,39,176,0) 70%)',
          filter: 'blur(25px)',
          zIndex: 0,
        }}
      />
      
      {/* Linhas decorativas */}
      <Box
        component={motion.div}
        initial={{ opacity: 0, width: 0 }}
        animate={{ opacity: 0.4, width: '30%' }}
        transition={{ duration: 1.5, ease: "easeOut" }}
        sx={{
          position: 'absolute',
          top: '30%',
          left: 0,
          height: '1px',
          background: 'linear-gradient(90deg, rgba(33,150,243,0) 0%, rgba(33,150,243,0.8) 50%, rgba(33,150,243,0) 100%)',
          zIndex: 0,
        }}
      />
      
      <Box
        component={motion.div}
        initial={{ opacity: 0, width: 0 }}
        animate={{ opacity: 0.4, width: '40%' }}
        transition={{ duration: 1.5, delay: 0.3, ease: "easeOut" }}
        sx={{
          position: 'absolute',
          bottom: '25%',
          right: 0,
          height: '1px',
          background: 'linear-gradient(90deg, rgba(33,150,243,0) 0%, rgba(156,39,176,0.8) 50%, rgba(33,150,243,0) 100%)',
          zIndex: 0,
        }}
      />
      
      {/* Pontos decorativos */}
      {[...Array(20)].map((_, i) => (
        <Box
          key={i}
          component={motion.div}
          initial={{ 
            opacity: 0,
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight
          }}
          animate={{ 
            opacity: Math.random() * 0.5 + 0.1,
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: Math.random() * 3 + 2,
            repeat: Infinity,
            repeatType: "reverse",
            delay: Math.random() * 2
          }}
          sx={{
            position: 'absolute',
            width: Math.random() * 4 + 1,
            height: Math.random() * 4 + 1,
            borderRadius: '50%',
            backgroundColor: i % 2 ? 'primary.main' : 'secondary.main',
            filter: 'blur(1px)',
            zIndex: 0,
          }}
        />
      ))}
    </>
  );

  return (
    <>
      <Backdrop
        open={loginSuccess}
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(8px)',
        }}
      >
        <Fade in={loginSuccess}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: [0, 1.2, 1] }}
              transition={{
                duration: 0.5,
                times: [0, 0.6, 1],
                ease: "easeInOut"
              }}
            >
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.success.main,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: `0 0 30px ${alpha(theme.palette.success.main, 0.5)}`
                }}
              >
                <DoneIcon sx={{ fontSize: 40, color: '#fff' }} />
              </Box>
            </motion.div>
            <Typography
              variant="h5"
              sx={{
                color: theme.palette.success.main,
                fontWeight: 600,
                textAlign: 'center'
              }}
            >
              Login bem-sucedido!
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.text.secondary,
                textAlign: 'center'
              }}
            >
              Redirecionando para o conteúdo...
            </Typography>
          </Box>
        </Fade>
      </Backdrop>

      <Container 
        maxWidth={false}
        disableGutters
        sx={{
        height: '100vh',
        width: '100vw',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        background: theme.palette.mode === 'dark' 
          ? `linear-gradient(135deg, 
              rgba(17, 25, 40, 0.95), 
              rgba(28, 37, 54, 0.95)
            ), url('/icons/openart-image_HcXPUXj4_1742859866308_raw.jpg')`
          : `linear-gradient(135deg, 
              rgba(248, 250, 252, 0.95), 
              rgba(226, 232, 240, 0.95)
            ), url('/icons/openart-image_HcXPUXj4_1742859866308_raw.jpg')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative',
        overflow: 'hidden',
        minHeight: '100vh',
        minWidth: '100vw',
        maxHeight: '100vh',
        maxWidth: '100vw',
        padding: 0,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at 50% 50%, 
            ${alpha(theme.palette.primary.main, 0.1)} 0%, 
            transparent 50%
          )`,
          animation: 'pulse 4s ease-in-out infinite',
          zIndex: 0
        },
        '@keyframes pulse': {
          '0%, 100%': {
            transform: 'scale(1)',
            opacity: 0.3
          },
          '50%': {
            transform: 'scale(1.2)',
            opacity: 0.5
          }
        }
      }}
    >
      {/* Background effects */}
      {renderBackgroundEffects()}
      
      {/* Logo no topo com efeitos avançados */}
      <MotionBox
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          type: "spring",
          stiffness: 200,
          damping: 20,
          delay: 0.2
        }}
        sx={{ 
          position: 'absolute',
          top: isMobile ? '5%' : '10%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          zIndex: 1
        }}
      >
        <motion.div
          whileHover={{ 
            scale: 1.05,
            rotate: [0, -5, 5, -5, 0],
            transition: {
              duration: 0.5,
              rotate: {
                repeat: Infinity,
                repeatType: "reverse",
                duration: 2
              }
            }
          }}
        >
          <Box 
            sx={{
              width: isMobile ? 100 : 140,
              height: isMobile ? 100 : 140,
              mb: 2,
              mx: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                inset: -4,
                background: `linear-gradient(135deg, 
                  ${theme.palette.primary.main}, 
                  ${theme.palette.secondary.main})`,
                borderRadius: '50%',
                opacity: 0.5,
                filter: 'blur(20px)',
                animation: 'pulse 3s ease-in-out infinite'
              },
              '@keyframes pulse': {
                '0%, 100%': {
                  transform: 'scale(0.8)',
                  opacity: 0.3
                },
                '50%': {
                  transform: 'scale(1.2)',
                  opacity: 0.7
                }
              }
            }}
          >
            <img 
              src="/logo-neko-tv.png" 
              alt="Neko TV Logo" 
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'contain', 
                filter: 'drop-shadow(0 8px 24px rgba(56, 189, 248, 0.5))',
                position: 'relative',
                zIndex: 1
              }} 
            />
          </Box>
        </motion.div>
      </MotionBox>

      {/* Card principal */}
      <MotionCard 
        sx={{ 
          width: isMobile ? '90%' : isTablet ? '70%' : '500px',
          maxWidth: '500px',
          borderRadius: 8,
          background: alpha(theme.palette.mode === 'dark' 
            ? '#1a1f35' 
            : '#ffffff', 
            theme.palette.mode === 'dark' ? 0.8 : 0.9),
          backdropFilter: 'blur(20px)',
          boxShadow: theme.palette.mode === 'dark'
            ? `0 25px 50px -12px rgba(0,0,0,0.5),
               0 0 0 1px rgba(255,255,255,0.1),
               0 0 40px -10px ${alpha(theme.palette.primary.main, 0.3)}`
            : `0 25px 50px -12px rgba(0,0,0,0.15),
               0 0 0 1px rgba(0,0,0,0.05),
               0 0 40px -10px ${alpha(theme.palette.primary.main, 0.2)}`,
          position: 'relative',
          overflow: 'hidden',
          zIndex: 2,
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          '&::before': {
            content: '""',
            position: 'absolute',
            inset: 0,
            background: `linear-gradient(135deg,
              ${alpha(theme.palette.primary.main, 0.15)},
              ${alpha(theme.palette.primary.dark, 0.05)})`,
            maskImage: 'radial-gradient(circle at 100% 0%, black, transparent 50%)',
            zIndex: -1
          }
        }}
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        {/* Borda superior decorativa */}
        <Box 
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(to right, #0ea5e9, #38bdf8, #0ea5e9)',
            zIndex: 3
          }}
        />
        
        <CardContent sx={{ p: isMobile ? 3 : 4, overflow: 'clip' }}>
          {showManualLogin ? (
            <>
              {/* Cabeçalho do Login Manual */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography 
                  variant={isMobile ? "h5" : "h4"} 
                  sx={{ 
                    fontWeight: 700, 
                    color: theme.palette.mode === 'dark' ? 'white' : 'text.primary',
                    mb: 1,
                    position: 'relative'
                  }}
                >
                  Conexão Manual
                </Typography>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: 'text.secondary',
                    opacity: 0.8,
                    fontSize: '0.875rem'
                  }}
                >
                  Conecte-se usando sua URL IPTV personalizada
                </Typography>
              </Box>

              {/* Formulário Elegante */}
              <Box 
                component="form" 
                sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  gap: 3, 
                  mb: 3 
                }}
                onSubmit={(e) => {
                  e.preventDefault();
                  handleManualLogin();
                }}
              >
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <TextField
                    label="URL do Servidor IPTV"
                    variant="outlined"
                    fullWidth
                    required
                    value={manualUrl}
                    onChange={(e) => setManualUrl(e.target.value)}
                    placeholder="http://exemplo.com:8080"
                    InputProps={{
                      startAdornment: (
                        <LockIcon sx={{ 
                          mr: 1, 
                          color: theme.palette.mode === 'dark' 
                            ? alpha(theme.palette.primary.main, 0.7)
                            : theme.palette.primary.main
                        }} />
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        backgroundColor: alpha(
                          theme.palette.background.paper,
                          theme.palette.mode === 'dark' ? 0.15 : 0.8
                        ),
                        backdropFilter: 'blur(12px)',
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        '&:hover': {
                          backgroundColor: alpha(
                            theme.palette.background.paper,
                            theme.palette.mode === 'dark' ? 0.25 : 0.9
                          ),
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: alpha(theme.palette.primary.main, 0.5),
                          }
                        },
                        '&.Mui-focused': {
                          backgroundColor: alpha(
                            theme.palette.background.paper,
                            theme.palette.mode === 'dark' ? 0.3 : 0.95
                          ),
                          transform: 'translateY(-2px)',
                          boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.25)}`,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderWidth: 2,
                            borderColor: theme.palette.primary.main,
                          }
                        }
                      },
                      '& .MuiInputLabel-root': {
                        backgroundColor: 'transparent',
                        '&.Mui-focused': {
                          color: theme.palette.primary.main
                        }
                      }
                    }}
                  />
                </motion.div>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    style={{ flex: 1 }}
                  >
                    <TextField
                      label="Usuário (opcional)"
                      variant="outlined"
                      fullWidth
                      value={manualUsername}
                      onChange={(e) => setManualUsername(e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <motion.div
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <IconButton 
                              size="small"
                              sx={{ mr: 1 }}
                              onClick={() => {/* Função para sugerir usuário se necessário */}}
                            >
                              <KeyboardArrowRightIcon sx={{ 
                                color: theme.palette.mode === 'dark' 
                                  ? alpha(theme.palette.primary.main, 0.7)
                                  : theme.palette.primary.main
                              }} />
                            </IconButton>
                          </motion.div>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          backgroundColor: alpha(
                            theme.palette.background.paper,
                            theme.palette.mode === 'dark' ? 0.15 : 0.8
                          ),
                          backdropFilter: 'blur(12px)',
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                          '&:hover': {
                            backgroundColor: alpha(
                              theme.palette.background.paper,
                              theme.palette.mode === 'dark' ? 0.25 : 0.9
                            ),
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderColor: alpha(theme.palette.primary.main, 0.5),
                            }
                          },
                          '&.Mui-focused': {
                            backgroundColor: alpha(
                              theme.palette.background.paper,
                              theme.palette.mode === 'dark' ? 0.3 : 0.95
                            ),
                            transform: 'translateY(-2px)',
                            boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.25)}`,
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderWidth: 2,
                              borderColor: theme.palette.primary.main,
                            }
                          }
                        }
                      }}
                    />
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    style={{ flex: 1 }}
                  >
                    <TextField
                      label="Senha (opcional)"
                      variant="outlined"
                      type={showPassword ? 'text' : 'password'}
                      fullWidth
                      value={manualPassword}
                      onChange={(e) => setManualPassword(e.target.value)}
                      InputProps={{
                        endAdornment: (
                          <motion.div
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                              size="small"
                            >
                              {showPassword ? (
                                <VisibilityOffIcon sx={{ color: 'text.secondary' }} />
                              ) : (
                                <VisibilityIcon sx={{ color: 'text.secondary' }} />
                              )}
                            </IconButton>
                          </motion.div>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          backgroundColor: alpha(
                            theme.palette.background.paper,
                            theme.palette.mode === 'dark' ? 0.15 : 0.8
                          ),
                          backdropFilter: 'blur(12px)',
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                          '&:hover': {
                            backgroundColor: alpha(
                              theme.palette.background.paper,
                              theme.palette.mode === 'dark' ? 0.25 : 0.9
                            ),
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderColor: alpha(theme.palette.primary.main, 0.5),
                            }
                          },
                          '&.Mui-focused': {
                            backgroundColor: alpha(
                              theme.palette.background.paper,
                              theme.palette.mode === 'dark' ? 0.3 : 0.95
                            ),
                            transform: 'translateY(-2px)',
                            boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.25)}`,
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderWidth: 2,
                              borderColor: theme.palette.primary.main,
                            }
                          }
                        }
                      }}
                    />
                  </motion.div>
                </Box>
              </Box>

              {/* Botões de Ação do Login Manual */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 3 }}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="large"
                    fullWidth
                    onClick={handleManualLogin}
                    disabled={isChecking || !manualUrl.trim()}
                    sx={{ 
                      py: 2, 
                      fontWeight: 700,
                      fontSize: '1rem',
                      borderRadius: 3,
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                      boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                      textTransform: 'none',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.4)}`,
                      },
                      '&:disabled': {
                        background: theme.palette.action.disabledBackground,
                        color: theme.palette.action.disabled
                      }
                    }}
                  >
                    {isChecking ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CircularProgress size={20} color="inherit" />
                        <Typography variant="inherit">Conectando...</Typography>
                      </Box>
                    ) : (
                      'Conectar Agora'
                    )}
                  </Button>
                </motion.div>

                <Button 
                  variant="text" 
                  size="medium"
                  onClick={() => setShowManualLogin(false)} 
                  sx={{ 
                    py: 1.5,
                    fontWeight: 600,
                    color: 'text.secondary',
                    borderRadius: 2,
                    textTransform: 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.action.hover, 0.1),
                      color: theme.palette.primary.main,
                    }
                  }}
                >
                  ← Voltar ao código do dispositivo
                </Button>
              </Box>

              {/* Dica de Uso */}
              <Box sx={{ mt: 3, p: 2, borderRadius: 2, backgroundColor: alpha(theme.palette.info.main, 0.1), border: `1px solid ${alpha(theme.palette.info.main, 0.2)}` }}>
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: theme.palette.info.main,
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    display: 'block',
                    textAlign: 'center'
                  }}
                >
                  💡 <strong>Dica:</strong> A URL deve incluir o protocolo (http:// ou https://) e a porta se necessário
                </Typography>
              </Box>
            </>
          ) : (
            <>
              <Box sx={{ textAlign: 'center', mb: 4, position: 'relative' }}>
                <Typography 
                  variant={isMobile ? "h5" : "h4"} 
                  sx={{ 
                    fontWeight: 800,
                    background: `linear-gradient(135deg, 
                      ${theme.palette.primary.main}, 
                      ${theme.palette.secondary.main})`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))',
                    mb: 1
                  }}
                >
                  Código do Dispositivo
                </Typography>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: alpha(theme.palette.text.primary, 0.6),
                    maxWidth: '80%',
                    mx: 'auto'
                  }}
                >
                  Use este código único para ativar sua licença
                </Typography>
              </Box>
              <Paper
                component={motion.div}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.8 }}
                sx={{
                  p: isMobile ? 2.5 : 3.5,
                  borderRadius: 3,
                  background: theme.palette.mode === 'dark' 
                    ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.8))'
                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(241, 245, 249, 0.9))',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 10px 25px rgba(0,0,0,0.2)'
                    : '0 10px 25px rgba(0,0,0,0.06)',
                  border: `1px solid ${alpha(theme.palette.divider, 0.05)}`,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  mb: 4
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    p: 3,
                    mb: 3,
                    borderRadius: 3,
                    background: alpha(theme.palette.mode === 'dark' ? '#2d3748' : '#f7fafc', 0.7),
                    border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    display: 'flex',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
                      border: `2px solid ${alpha(theme.palette.primary.main, 0.4)}`
                    }
                  }}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ 
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      delay: 0.2
                    }}
                  >
                    <Typography
                      variant={isMobile ? "h5" : "h4"}
                      fontFamily="'JetBrains Mono', monospace"
                      fontWeight="700"
                      sx={{
                        background: `linear-gradient(135deg, 
                          ${theme.palette.primary.main}, 
                          ${theme.palette.secondary.main})`,
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        letterSpacing: 2,
                        display: 'inline-block',
                        position: 'relative',
                        textAlign: 'center',
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
                      }}
                    >
                      {deviceId}
                    </Typography>
                  </motion.div>
                  
                  {/* Efeito pulsante sutil atrás do código */}
                  <Box
                    component={motion.div}
                    animate={{ 
                      opacity: [0.1, 0.3, 0.1],
                      scale: [1, 1.05, 1]
                    }}
                    transition={{ 
                      repeat: Infinity, 
                      duration: 3,
                      ease: "easeInOut"
                    }}
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      borderRadius: 'inherit',
                      background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 70%)`,
                      zIndex: -1
                    }}
                  />
                </Box>
                  
                <Box 
                  sx={{ 
                    display: 'flex',
                    gap: 2,
                    width: '100%',
                    justifyContent: 'center'
                  }}
                >
                  <Button 
                    variant="outlined" 
                    color="primary"
                    size={isMobile ? "medium" : "large"}
                    startIcon={<ContentCopyIcon />}
                    onClick={copyToClipboard}
                    sx={{
                      borderRadius: 2,
                      px: 3,
                      py: 1,
                      fontWeight: 600,
                      transition: 'all 0.3s',
                      boxShadow: theme.palette.mode === 'dark'
                        ? 'none'
                        : '0 4px 8px rgba(0,0,0,0.04)',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 6px 10px rgba(0,0,0,0.1)'
                      }
                    }}
                  >
                    {copied ? "Copiado!" : "Copiar"}
                  </Button>
                    
                  <Button 
                    variant="contained" 
                    color="primary"
                    size={isMobile ? "medium" : "large"}
                    startIcon={isChecking ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
                    onClick={checkLicense}
                    disabled={isChecking}
                    sx={{
                      borderRadius: 2,
                      px: 3,
                      py: 1,
                      fontWeight: 600,
                      backgroundImage: 'linear-gradient(135deg, #0ea5e9, #38bdf8)',
                      transition: 'all 0.3s',
                      boxShadow: '0 6px 15px rgba(14, 165, 233, 0.3)',
                      '&:hover': {
                        boxShadow: '0 8px 20px rgba(14, 165, 233, 0.4)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    {isChecking ? "Verificando..." : "Verificar"}
                  </Button>
                </Box>
              </Paper>
            </>
          )}
          
          {/* Mensagem de status */}
          {message && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <Paper 
                sx={{ 
                  p: 2, 
                  mb: 3,
                  borderRadius: 2,
                  bgcolor: isError ? 'error.light' : 'success.light',
                  color: isError ? 'error.dark' : 'success.dark',
                  boxShadow: `0 4px 12px ${alpha(isError ? theme.palette.error.main : theme.palette.success.main, 0.2)}`
                }}
              >
                <Typography sx={{ fontWeight: 500 }}>{message}</Typography>
              </Paper>
            </motion.div>
          )}
          
          {!showManualLogin && (
            <>
              {/* Instruções elegantes */}
              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography 
                  variant="body1" 
                  sx={{ 
                    color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)',
                    maxWidth: '85%',
                    mx: 'auto',
                    lineHeight: 1.6,
                    fontSize: '1rem',
                    fontWeight: 400,
                    mb: 1
                  }}
                >
                  Para ativar seu aplicativo, envie este código para o administrador
                </Typography>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.5)',
                    maxWidth: '80%',
                    mx: 'auto',
                    lineHeight: 1.5,
                    fontSize: '0.875rem'
                  }}
                >
                  Após aprovação, você terá acesso completo a todos os conteúdos
                </Typography>
              </Box>

              {/* Seção de Ações Modernas */}
              <Box sx={{ mt: 5, mb: 1 }}>
                {/* Divisor elegante */}
                <Box 
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 4,
                    opacity: 0.3
                  }}
                >
                  <Box sx={{ flex: 1, height: '1px', background: 'linear-gradient(90deg, transparent, currentColor, transparent)' }} />
                  <Typography variant="caption" sx={{ mx: 2, color: 'text.secondary', fontSize: '0.75rem' }}>
                    OPÇÕES
                  </Typography>
                  <Box sx={{ flex: 1, height: '1px', background: 'linear-gradient(90deg, transparent, currentColor, transparent)' }} />
                </Box>

                {/* Botões redesenhados */}
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: isMobile ? 'column' : 'row', 
                  gap: isMobile ? 3 : 4, 
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                  {/* Botão Comprar Plano - Design Premium Atualizado */}
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    style={{ 
                      position: 'relative',
                      width: isMobile ? '100%' : 'auto'
                    }}
                  >
                    <Button
                      {...createExternalLinkProps('https://nekotv.top/')}
                      size="large"
                      startIcon={
                        <LiveTvIcon sx={{ 
                          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
                        }} />
                      }
                      sx={{
                        minWidth: isMobile ? '100%' : '220px',
                        px: 4,
                        py: 2,
                        fontWeight: 700,
                        fontSize: '1rem',
                        borderRadius: 3,
                        background: `linear-gradient(135deg, 
                          ${theme.palette.primary.main} 0%, 
                          ${theme.palette.secondary.main} 100%)`,
                        color: '#fff',
                        boxShadow: `
                          0 10px 30px -5px ${alpha(theme.palette.primary.main, 0.4)},
                          0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)} inset,
                          0 2px 4px rgba(0,0,0,0.1)
                        `,
                        position: 'relative',
                        overflow: 'hidden',
                        textTransform: 'none',
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          background: `linear-gradient(135deg,
                            ${alpha('#fff', 0.1)},
                            ${alpha('#fff', 0.05)})`,
                          opacity: 0,
                          transition: 'opacity 0.4s ease'
                        },
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: `
                            0 15px 40px -5px ${alpha(theme.palette.primary.main, 0.5)},
                            0 0 0 2px ${alpha(theme.palette.primary.main, 0.4)} inset,
                            0 4px 8px rgba(0,0,0,0.2)
                          `,
                          textDecoration: 'none',
                          '&::before': {
                            opacity: 1
                          }
                        },
                        '&:active': {
                          transform: 'translateY(0)',
                          boxShadow: `
                            0 5px 15px -5px ${alpha(theme.palette.primary.main, 0.4)},
                            0 0 0 1px ${alpha(theme.palette.primary.main, 0.3)} inset
                          `
                        }
                      }}
                    >
                      <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 1.5,
                        position: 'relative',
                        zIndex: 1
                      }}>
                        <Typography 
                          variant="inherit" 
                          sx={{ 
                            fontWeight: 'inherit',
                            textShadow: '0 2px 4px rgba(0,0,0,0.2)'
                          }}
                        >
                          Adquirir Licença
                        </Typography>
                      </Box>
                    </Button>
                  </motion.div>

                  {/* Texto "ou" elegante */}
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: 'text.secondary',
                      fontWeight: 500,
                      opacity: 0.6,
                      fontSize: '0.875rem',
                      mx: 1
                    }}
                  >
                    ou
                  </Typography>

                  {/* Botão Login Manual - Design Atualizado */}
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    style={{ 
                      position: 'relative',
                      width: isMobile ? '100%' : 'auto'
                    }}
                  >
                    <Button
                      onClick={() => setShowManualLogin(true)}
                      size="large"
                      variant="outlined"
                      sx={{
                        minWidth: isMobile ? '100%' : '200px',
                        px: 4,
                        py: 2,
                        fontWeight: 600,
                        fontSize: '1rem',
                        borderRadius: 3,
                        color: theme.palette.mode === 'dark' ? '#fff' : theme.palette.primary.main,
                        borderColor: theme.palette.mode === 'dark' 
                          ? alpha(theme.palette.primary.main, 0.3)
                          : alpha(theme.palette.primary.main, 0.5),
                        backgroundColor: theme.palette.mode === 'dark'
                          ? alpha(theme.palette.background.paper, 0.1)
                          : alpha(theme.palette.background.paper, 0.8),
                        backdropFilter: 'blur(10px)',
                        textTransform: 'none',
                        position: 'relative',
                        overflow: 'hidden',
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          borderRadius: 'inherit',
                          border: '2px solid transparent',
                          background: `linear-gradient(135deg, 
                            ${theme.palette.primary.main}, 
                            ${theme.palette.secondary.main}) border-box`,
                          WebkitMask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',
                          WebkitMaskComposite: 'destination-out',
                          maskComposite: 'exclude',
                          opacity: 0,
                          transition: 'opacity 0.4s ease'
                        },
                        '&:hover': {
                          borderColor: 'transparent',
                          backgroundColor: theme.palette.mode === 'dark'
                            ? alpha(theme.palette.background.paper, 0.2)
                            : alpha(theme.palette.background.paper, 0.9),
                          transform: 'translateY(-2px)',
                          boxShadow: `
                            0 8px 20px ${alpha(theme.palette.primary.main, 0.2)},
                            0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)}
                          `,
                          '&::before': {
                            opacity: 1
                          }
                        },
                        '&:active': {
                          transform: 'translateY(0)',
                          boxShadow: `0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)}`
                        }
                      }}
                    >
                      Conexão Manual
                    </Button>
                  </motion.div>
                </Box>

                {/* Dica adicional */}
                <Box sx={{ mt: 4, textAlign: 'center' }}>
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: 'text.secondary',
                      opacity: 0.6,
                      fontSize: '0.75rem',
                      fontStyle: 'italic'
                    }}
                  >
                    💡 Já possui uma URL IPTV? Use a conexão manual
                  </Typography>
                </Box>
              </Box>
            </>
          )}
        </CardContent>
      </MotionCard>
      
      {/* Rodapé */}
      <MotionBox
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.7 }}
        transition={{ delay: 1.2, duration: 1 }}
        sx={{ 
          position: 'absolute',
          bottom: isMobile ? '5%' : '10%',
          textAlign: 'center',
          zIndex: 1
        }}
      >
        <Typography 
          variant="caption" 
          sx={{ 
            color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.6)',
            fontSize: isMobile ? '0.7rem' : '0.75rem'
          }}
        >
          © 2023 Neko TV • Todos os direitos reservados
        </Typography>
      </MotionBox>
    </Container>
    </>
  );
};

export default Access;