/* Estilos para garantir que o AppBar do Neko TV fique sempre visível */
.neko-app-bar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10000 !important; /* Valor muito alto para ficar acima do ticker */
  width: 100% !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Estilos adicionais para garantir visibilidade em dispositivos móveis */
@media screen and (max-width: 600px) {
  .neko-app-bar {
    height: auto !important;
    min-height: 56px !important;
  }
}

/* Estilos para o contêiner principal abaixo do AppBar */
.main-content-below-appbar {
  padding-top: 70px; /* Espaço para o AppBar */
}

/* Garantir que as transições não afetem a visibilidade */
.neko-app-bar {
  transition: background-color 0.3s ease, box-shadow 0.3s ease !important;
  transform: none !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
} 