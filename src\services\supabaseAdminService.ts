import { supabaseMCP, mcpOperations, testMCPConnection } from '../integrations/supabase/mcp-client';
import type { License, LicenseInsert, LicenseUpdate } from '../integrations/supabase/types';

export interface AdminStats {
  totalLicenses: number;
  activeLicenses: number;
  inactiveLicenses: number;
  usedLicenses: number;
  totalUsers: number;
  recentActivity: any[];
}

export interface UserManagement {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
}

export class SupabaseAdminService {
  private static instance: SupabaseAdminService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): SupabaseAdminService {
    if (!SupabaseAdminService.instance) {
      SupabaseAdminService.instance = new SupabaseAdminService();
    }
    return SupabaseAdminService.instance;
  }

  // Inicializar e testar conexão MCP
  async initialize(): Promise<boolean> {
    if (this.isInitialized) return true;

    try {
      console.log('🚀 Inicializando Supabase Admin Service com MCP...');
      
      const isConnected = await testMCPConnection();
      if (!isConnected) {
        throw new Error('Falha na conexão MCP');
      }

      this.isInitialized = true;
      console.log('✅ Supabase Admin Service inicializado com sucesso!');
      return true;
    } catch (error) {
      console.error('❌ Erro ao inicializar Admin Service:', error);
      return false;
    }
  }

  // === GERENCIAMENTO DE LICENÇAS ===

  // Criar licença com validação completa
  async createLicense(data: Partial<LicenseInsert>): Promise<License | null> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Gerar chave única se não fornecida
      if (!data.license_key) {
        data.license_key = await this.generateUniqueLicenseKey();
      }

      const licenseData: LicenseInsert = {
        license_key: data.license_key,
        client_name: data.client_name || null,
        iptv_url: data.iptv_url || null,
        notes: data.notes || null,
        active: data.active ?? true,
        ...data
      };

      const result = await mcpOperations.createLicense(licenseData);
      console.log('✅ Licença criada via MCP:', result.license_key);
      return result;
    } catch (error) {
      console.error('❌ Erro ao criar licença via MCP:', error);
      return null;
    }
  }

  // Atualizar licença com permissões completas
  async updateLicense(id: string, data: Partial<LicenseUpdate>): Promise<License | null> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const result = await mcpOperations.updateLicense(id, data);
      console.log('✅ Licença atualizada via MCP:', id);
      return result;
    } catch (error) {
      console.error('❌ Erro ao atualizar licença via MCP:', error);
      return null;
    }
  }

  // Deletar licença permanentemente
  async deleteLicense(id: string): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const success = await mcpOperations.deleteLicense(id);
      if (success) {
        console.log('✅ Licença deletada via MCP:', id);
      }
      return success;
    } catch (error) {
      console.error('❌ Erro ao deletar licença via MCP:', error);
      return false;
    }
  }

  // Obter todas as licenças com detalhes completos
  async getAllLicenses(): Promise<License[]> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const licenses = await mcpOperations.getAllLicenses();
      console.log(`✅ ${licenses.length} licenças obtidas via MCP`);
      return licenses;
    } catch (error) {
      console.error('❌ Erro ao obter licenças via MCP:', error);
      return [];
    }
  }

  // Gerar chave de licença única
  private async generateUniqueLicenseKey(): Promise<string> {
    try {
      const { data, error } = await supabaseMCP.rpc('generate_random_key');
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erro ao gerar chave via RPC, usando fallback:', error);
      // Fallback: gerar chave localmente
      return this.generateLocalLicenseKey();
    }
  }

  private generateLocalLicenseKey(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    
    for (let i = 0; i < 2; i++) {
      for (let j = 0; j < 5; j++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      if (i < 1) result += '-';
    }
    
    return result;
  }

  // === GERENCIAMENTO DE USUÁRIOS ===

  // Criar usuário admin
  async createAdminUser(email: string, password: string): Promise<UserManagement | null> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const result = await mcpOperations.createUser(email, password);
      console.log('✅ Usuário admin criado via MCP:', email);
      return result.user;
    } catch (error) {
      console.error('❌ Erro ao criar usuário via MCP:', error);
      return null;
    }
  }

  // Listar todos os usuários
  async getAllUsers(): Promise<UserManagement[]> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const { data, error } = await supabaseMCP.auth.admin.listUsers();
      
      if (error) throw error;
      
      console.log(`✅ ${data.users.length} usuários obtidos via MCP`);
      return data.users.map(user => ({
        id: user.id,
        email: user.email || '',
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
        email_confirmed_at: user.email_confirmed_at
      }));
    } catch (error) {
      console.error('❌ Erro ao obter usuários via MCP:', error);
      return [];
    }
  }

  // Deletar usuário
  async deleteUser(userId: string): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const success = await mcpOperations.deleteUser(userId);
      if (success) {
        console.log('✅ Usuário deletado via MCP:', userId);
      }
      return success;
    } catch (error) {
      console.error('❌ Erro ao deletar usuário via MCP:', error);
      return false;
    }
  }

  // === ESTATÍSTICAS E ANALYTICS ===

  // Obter estatísticas completas
  async getAdminStats(): Promise<AdminStats> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const [licenses, users] = await Promise.all([
        this.getAllLicenses(),
        this.getAllUsers()
      ]);

      const activeLicenses = licenses.filter(l => l.active).length;
      const usedLicenses = licenses.filter(l => l.device_id).length;

      const stats: AdminStats = {
        totalLicenses: licenses.length,
        activeLicenses,
        inactiveLicenses: licenses.length - activeLicenses,
        usedLicenses,
        totalUsers: users.length,
        recentActivity: [] // TODO: Implementar atividade recente
      };

      console.log('✅ Estatísticas obtidas via MCP:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Erro ao obter estatísticas via MCP:', error);
      return {
        totalLicenses: 0,
        activeLicenses: 0,
        inactiveLicenses: 0,
        usedLicenses: 0,
        totalUsers: 0,
        recentActivity: []
      };
    }
  }

  // === OPERAÇÕES AVANÇADAS ===

  // Executar SQL customizado
  async executeCustomSQL(query: string): Promise<any> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const result = await mcpOperations.executeSQL(query);
      console.log('✅ SQL executado via MCP');
      return result;
    } catch (error) {
      console.error('❌ Erro ao executar SQL via MCP:', error);
      throw error;
    }
  }

  // Backup de dados
  async createBackup(): Promise<any> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const [licenses, users] = await Promise.all([
        this.getAllLicenses(),
        this.getAllUsers()
      ]);

      const backup = {
        timestamp: new Date().toISOString(),
        licenses,
        users,
        version: '1.0'
      };

      console.log('✅ Backup criado via MCP');
      return backup;
    } catch (error) {
      console.error('❌ Erro ao criar backup via MCP:', error);
      throw error;
    }
  }

  // Verificar saúde do sistema
  async healthCheck(): Promise<any> {
    try {
      const isHealthy = await testMCPConnection();
      const stats = await this.getAdminStats();
      
      const health = {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        mcp_connection: isHealthy,
        database_accessible: stats.totalLicenses >= 0,
        auth_working: stats.totalUsers >= 0,
        stats
      };

      console.log('✅ Health check via MCP:', health.status);
      return health;
    } catch (error) {
      console.error('❌ Erro no health check via MCP:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Instância singleton
export const supabaseAdminService = SupabaseAdminService.getInstance();

// Exportar para uso global
if (typeof window !== 'undefined') {
  (window as any).supabaseAdmin = supabaseAdminService;
  
  console.log('💡 Supabase Admin Service disponível globalmente:');
  console.log('supabaseAdmin.initialize() - Inicializar');
  console.log('supabaseAdmin.healthCheck() - Verificar saúde');
  console.log('supabaseAdmin.getAdminStats() - Estatísticas');
}
