import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Connection {
  id: string
  url: string
  username: string
  password: string
  name: string
  isFavorite: boolean
  isActive: boolean
  lastUsed: number
  createdAt: number
  updatedAt: number
}

export interface ConnectionTab {
  id: string
  connectionId: string
  name: string
  isActive: boolean
  hasError: boolean
  isLoading: boolean
  lastActivity: number
}

interface ConnectionState {
  // Connections
  connections: Connection[]
  activeConnectionId: string | null
  
  // Tabs
  activeTabs: ConnectionTab[]
  
  // UI State
  isAuthModalOpen: boolean
  editingConnection: Connection | null
  
  // Actions
  addConnection: (connection: Omit<Connection, 'id' | 'createdAt' | 'updatedAt'>) => string
  updateConnection: (id: string, updates: Partial<Connection>) => void
  deleteConnection: (id: string) => void
  setActiveConnection: (id: string | null) => void
  
  // Tab Actions
  addTab: (connection: Connection) => void
  removeTab: (tabId: string) => void
  switchTab: (tabId: string) => void
  updateTab: (tabId: string, updates: Partial<ConnectionTab>) => void
  
  // Modal Actions
  openAuthModal: (connection?: Connection) => void
  closeAuthModal: () => void
  
  // Utility
  getActiveConnection: () => Connection | null
  getConnectionById: (id: string) => Connection | null
}

const generateId = () => Math.random().toString(36).substr(2, 9)

export const useConnectionStore = create<ConnectionState>()(
  persist(
    (set, get) => ({
      // Initial state
      connections: [],
      activeConnectionId: null,
      activeTabs: [],
      isAuthModalOpen: false,
      editingConnection: null,

      // Connection actions
      addConnection: (connectionData) => {
        const id = generateId()
        const now = Date.now()
        
        const connection: Connection = {
          ...connectionData,
          id,
          createdAt: now,
          updatedAt: now,
          lastUsed: now,
          isActive: false,
          isFavorite: false
        }

        console.log('💾 Adding connection to store:', connection.name)
        
        set((state) => {
          const newState = {
            connections: [...state.connections, connection]
          }
          console.log('💾 New connections count:', newState.connections.length)
          return newState
        })

        return id
      },

      updateConnection: (id, updates) => {
        set((state) => ({
          connections: state.connections.map(conn =>
            conn.id === id
              ? { ...conn, ...updates, updatedAt: Date.now() }
              : conn
          )
        }))
      },

      deleteConnection: (id) => {
        set((state) => ({
          connections: state.connections.filter(conn => conn.id !== id),
          activeTabs: state.activeTabs.filter(tab => tab.connectionId !== id),
          activeConnectionId: state.activeConnectionId === id ? null : state.activeConnectionId
        }))
      },

      setActiveConnection: (id) => {
        set((state) => {
          // Update lastUsed for the active connection
          const connections = state.connections.map(conn => ({
            ...conn,
            isActive: conn.id === id,
            lastUsed: conn.id === id ? Date.now() : conn.lastUsed
          }))

          return {
            activeConnectionId: id,
            connections
          }
        })
      },

      // Tab actions
      addTab: (connection) => {
        const state = get()
        
        // Check if tab already exists
        const existingTab = state.activeTabs.find(tab => tab.connectionId === connection.id)
        if (existingTab) {
          get().switchTab(existingTab.id)
          return
        }

        const tabId = generateId()
        const newTab: ConnectionTab = {
          id: tabId,
          connectionId: connection.id,
          name: connection.name,
          isActive: true,
          hasError: false,
          isLoading: false,
          lastActivity: Date.now()
        }

        set((state) => ({
          activeTabs: [
            ...state.activeTabs.map(tab => ({ ...tab, isActive: false })),
            newTab
          ]
        }))

        // Set as active connection
        get().setActiveConnection(connection.id)
      },

      removeTab: (tabId) => {
        set((state) => {
          const tabToRemove = state.activeTabs.find(tab => tab.id === tabId)
          const remainingTabs = state.activeTabs.filter(tab => tab.id !== tabId)
          
          let newActiveConnectionId = state.activeConnectionId

          // If removing active tab, switch to another tab
          if (tabToRemove?.isActive && remainingTabs.length > 0) {
            const nextTab = remainingTabs[remainingTabs.length - 1]
            nextTab.isActive = true
            newActiveConnectionId = nextTab.connectionId
          } else if (remainingTabs.length === 0) {
            newActiveConnectionId = null
          }

          return {
            activeTabs: remainingTabs,
            activeConnectionId: newActiveConnectionId
          }
        })
      },

      switchTab: (tabId) => {
        set((state) => {
          const tabs = state.activeTabs.map(tab => ({
            ...tab,
            isActive: tab.id === tabId,
            lastActivity: tab.id === tabId ? Date.now() : tab.lastActivity
          }))

          const activeTab = tabs.find(tab => tab.id === tabId)
          const activeConnectionId = activeTab ? activeTab.connectionId : state.activeConnectionId

          return {
            activeTabs: tabs,
            activeConnectionId
          }
        })

        // Update connection lastUsed
        const activeTab = get().activeTabs.find(tab => tab.id === tabId)
        if (activeTab) {
          get().setActiveConnection(activeTab.connectionId)
        }
      },

      updateTab: (tabId, updates) => {
        set((state) => ({
          activeTabs: state.activeTabs.map(tab =>
            tab.id === tabId
              ? { ...tab, ...updates, lastActivity: Date.now() }
              : tab
          )
        }))
      },

      // Modal actions
      openAuthModal: (connection) => {
        console.log('🔓 Opening auth modal', connection ? 'for editing' : 'for new connection')
        set({
          isAuthModalOpen: true,
          editingConnection: connection || null
        })
      },

      closeAuthModal: () => {
        console.log('🔒 Closing auth modal')
        set({
          isAuthModalOpen: false,
          editingConnection: null
        })
      },

      // Utility functions
      getActiveConnection: () => {
        const state = get()
        return state.connections.find(conn => conn.id === state.activeConnectionId) || null
      },

      getConnectionById: (id) => {
        const state = get()
        return state.connections.find(conn => conn.id === id) || null
      }
    }),
    {
      name: 'iptv-connections',
      partialize: (state) => ({
        connections: state.connections,
        activeConnectionId: state.activeConnectionId,
        activeTabs: state.activeTabs
      }),
      onRehydrateStorage: () => (state) => {
        console.log('🔄 Rehydrating connection store:', state?.connections?.length || 0, 'connections')
      }
    }
  )
)