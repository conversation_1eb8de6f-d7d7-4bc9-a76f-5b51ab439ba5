import React from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';

const Layout: React.FC = () => {
  const location = useLocation();

  return (
    <div className="app-layout">
      <nav className="main-nav">
        <ul>
          <li>
            <Link to="/" className={location.pathname === '/' ? 'active' : ''}>
              Home
            </Link>
          </li>
          <li>
            <Link 
              to="/playlists" 
              className={location.pathname === '/playlists' ? 'active' : ''}
            >
              Playlists
            </Link>
          </li>
          <li>
            <Link 
              to="/settings" 
              className={location.pathname === '/settings' ? 'active' : ''}
            >
              Settings
            </Link>
          </li>
        </ul>
      </nav>

      <main className="main-content">
        <Outlet />
      </main>

      <style>{`
        .app-layout {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }

        .main-nav {
          background-color: #1a1a1a;
          padding: 1rem;
        }

        .main-nav ul {
          list-style: none;
          padding: 0;
          margin: 0;
          display: flex;
          gap: 2rem;
          justify-content: center;
        }

        .main-nav a {
          color: #fff;
          text-decoration: none;
          font-size: 1.1rem;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          transition: background-color 0.2s;
        }

        .main-nav a:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .main-nav a.active {
          background-color: #007bff;
        }

        .main-content {
          flex: 1;
          padding: 2rem;
          background-color: #f8f9fa;
        }
      `}</style>
    </div>
  );
};

export default Layout; 