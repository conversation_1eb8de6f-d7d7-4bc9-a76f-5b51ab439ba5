import React, { useState } from 'react';
import {
  Box,
  <PERSON>,
  <PERSON>u,
  <PERSON>uItem,
  IconButton,
  Typography,
  <PERSON>vider,
  <PERSON>ge,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Hd as HdIcon,
  FourK as FourKIcon,
  HighQuality as QualityIcon,
  MoreVert as MoreIcon,
  PlayArrow as PlayIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import type { ChannelGroup, ChannelVariant } from '../utils/channelOrganizer';

interface ChannelQualitySelectorProps {
  channelGroup: ChannelGroup;
  selectedVariant?: ChannelVariant;
  onVariantSelect: (variant: ChannelVariant) => void;
  compact?: boolean;
}

const ChannelQualitySelector: React.FC<ChannelQualitySelectorProps> = ({
  channelGroup,
  selectedVariant,
  onVariantSelect,
  compact = false
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleVariantSelect = (variant: ChannelVariant) => {
    onVariantSelect(variant);
    handleClose();
  };

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case '4K': return <FourKIcon />;
      case 'FHD': return <HdIcon />;
      case 'HD': return <HdIcon />;
      default: return <QualityIcon />;
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case '4K': return '#ff6b35';
      case 'FHD': return '#4caf50';
      case 'HD': return '#2196f3';
      case 'SD': return '#757575';
      default: return theme.palette.text.secondary;
    }
  };

  const currentVariant = selectedVariant || channelGroup.bestQuality;
  const hasMultipleVariants = channelGroup.variants.length > 1;

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        <Chip
          size="small"
          label={currentVariant.quality}
          sx={{
            bgcolor: alpha(getQualityColor(currentVariant.quality), 0.1),
            color: getQualityColor(currentVariant.quality),
            fontWeight: 600,
            fontSize: '0.7rem'
          }}
        />
        {hasMultipleVariants && (
          <Tooltip title={`${channelGroup.variants.length} qualidades disponíveis`}>
            <Badge badgeContent={channelGroup.variants.length} color="primary">
              <IconButton
                size="small"
                onClick={handleClick}
                sx={{ p: 0.5 }}
              >
                <MoreIcon fontSize="small" />
              </IconButton>
            </Badge>
          </Tooltip>
        )}
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {/* Qualidade Atual */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {getQualityIcon(currentVariant.quality)}
        <Typography
          variant="body2"
          sx={{
            color: getQualityColor(currentVariant.quality),
            fontWeight: 600
          }}
        >
          {currentVariant.quality}
        </Typography>
        {currentVariant.isH265 && (
          <Chip
            size="small"
            label="H265"
            sx={{
              bgcolor: alpha(theme.palette.warning.main, 0.1),
              color: theme.palette.warning.main,
              fontSize: '0.6rem',
              height: 16
            }}
          />
        )}
      </Box>

      {/* Botão de Variantes */}
      {hasMultipleVariants && (
        <Tooltip title="Escolher qualidade">
          <IconButton
            size="small"
            onClick={handleClick}
            sx={{
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.2)
              }
            }}
          >
            <Badge badgeContent={channelGroup.variants.length} color="primary">
              <MoreIcon fontSize="small" />
            </Badge>
          </IconButton>
        </Tooltip>
      )}

      {/* Menu de Variantes */}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            maxHeight: 300,
            minWidth: 250,
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
          }
        }}
      >
        <Box sx={{ p: 1 }}>
          <Typography variant="subtitle2" color="primary" gutterBottom>
            📺 {channelGroup.baseName}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Escolha a qualidade desejada:
          </Typography>
        </Box>
        
        <Divider />

        {/* Variantes Principais */}
        {channelGroup.variants
          .filter(v => !v.isAlternative)
          .sort((a, b) => {
            const qualityOrder = { '4K': 4, 'FHD': 3, 'HD': 2, 'SD': 1 };
            return (qualityOrder[b.quality as keyof typeof qualityOrder] || 0) - 
                   (qualityOrder[a.quality as keyof typeof qualityOrder] || 0);
          })
          .map((variant) => (
            <MenuItem
              key={variant.id}
              onClick={() => handleVariantSelect(variant)}
              selected={currentVariant.id === variant.id}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                py: 1
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                {getQualityIcon(variant.quality)}
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {variant.quality}
                </Typography>
                {variant.isH265 && (
                  <Chip
                    size="small"
                    label="H265"
                    sx={{
                      bgcolor: alpha(theme.palette.warning.main, 0.1),
                      color: theme.palette.warning.main,
                      fontSize: '0.6rem',
                      height: 16
                    }}
                  />
                )}
              </Box>
              {currentVariant.id === variant.id && (
                <PlayIcon color="primary" fontSize="small" />
              )}
            </MenuItem>
          ))}

        {/* Variantes Alternativas */}
        {channelGroup.variants.some(v => v.isAlternative) && (
          <>
            <Divider sx={{ my: 1 }} />
            <Box sx={{ px: 2, py: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                🔄 Fontes Alternativas:
              </Typography>
            </Box>
            {channelGroup.variants
              .filter(v => v.isAlternative)
              .sort((a, b) => {
                const qualityOrder = { '4K': 4, 'FHD': 3, 'HD': 2, 'SD': 1 };
                return (qualityOrder[b.quality as keyof typeof qualityOrder] || 0) - 
                       (qualityOrder[a.quality as keyof typeof qualityOrder] || 0);
              })
              .map((variant) => (
                <MenuItem
                  key={variant.id}
                  onClick={() => handleVariantSelect(variant)}
                  selected={currentVariant.id === variant.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    py: 1,
                    bgcolor: alpha(theme.palette.warning.main, 0.05)
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                    {getQualityIcon(variant.quality)}
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {variant.quality} Alt
                    </Typography>
                    {variant.isH265 && (
                      <SpeedIcon fontSize="small" color="warning" />
                    )}
                  </Box>
                  {currentVariant.id === variant.id && (
                    <PlayIcon color="primary" fontSize="small" />
                  )}
                </MenuItem>
              ))}
          </>
        )}
      </Menu>
    </Box>
  );
};

export default ChannelQualitySelector;
