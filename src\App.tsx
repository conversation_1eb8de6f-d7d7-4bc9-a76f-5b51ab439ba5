import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import EnhancedAccess from './pages/EnhancedAccess';
import ContentSelection from './pages/ContentSelection';
import ContentPage from './pages/ContentPage';
import ChannelPage from './pages/ChannelPage';
import MoviesPage from './pages/MoviesPage';
import SeriesPage from './pages/SeriesPage';
import Player from './pages/Player';
import MainNavigation from './components/MainNavigation';
import WindowControls from './components/WindowControls';
import AppLayout from './components/AppLayout';
import TickerPreloader from './components/TickerPreloader';
import UpdateModal from './components/UpdateModal';
import { dbService } from './services/dbService';
import { UserInfo, ServerInfo } from './services/iptvService';

import { setupGlobalProxyInterceptor } from './utils/proxyUrl';
import { useUpdateDetection } from './hooks/useUpdateDetection';
import { ThemeProvider, createTheme, CssBaseline, AppBar, Toolbar } from '@mui/material';
import { styled } from '@mui/material/styles';

// Import utilities apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  import('./utils/testSupabaseConnection').catch(console.warn);
  import('./integrations/supabase/mcp-client').catch(console.warn);
  import('./services/supabaseAdminService').catch(console.warn);
}

// Estilizando o AppBar para a tela de login
const LoginAppBar = styled(AppBar)(({ theme }) => ({
  background: 'transparent',
  boxShadow: 'none',
  WebkitAppRegion: 'drag', // Torna a barra de título arrastável no Electron
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 1100,
}));

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#0ea5e9',
    },
    secondary: {
      main: '#38bdf8',
    },
    background: {
      default: '#0f172a',
      paper: '#1e293b',
    },
    warning: {
      main: '#facc15',
    },
    success: {
      main: '#22c55e',
    },
    error: {
      main: '#ef4444',
    },
  },
  typography: {
    fontFamily: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
      },
    },
  },
});

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const navigate = useNavigate();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkConnection = async () => {
      try {
        const connection = await dbService.getConnection();
        if (!connection?.url) {
          navigate('/', { replace: true });
        }
      } catch (error) {
        console.error('Error checking connection:', error);
        navigate('/', { replace: true });
      } finally {
        setIsChecking(false);
      }
    };
    checkConnection();
    
    // Cleanup não necessário para o novo sistema de atualizações
  }, [navigate]);

  if (isChecking) {
    return null;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [serverInfo, setServerInfo] = useState<ServerInfo | null>(null);
  const location = useLocation();

  // Sistema de detecção de atualizações IPTV
  const {
    hasUpdates,
    updates,
    showModal,
    loading: updateLoading,
    handleUpdate,
    dismissModal,
    forceCheck
  } = useUpdateDetection({
    autoCheck: true,
    checkOnMount: true,
    onUpdateDetected: (updates) => {
      console.log('🔄 Atualizações IPTV detectadas:', updates);
    },
    onUpdateComplete: () => {
      console.log('✅ Atualização IPTV concluída');
    }
  });
  
  // Inicializar interceptor global de proxy para Mixed Content
  useEffect(() => {
    setupGlobalProxyInterceptor();
  }, []);
  
  // Não mostrar a navegação na página de login
  const showNavigation = location.pathname !== '/';
  // Check if on content selection page
  const isContentPage = location.pathname === '/content';
  // Check if on login page
  const isLoginPage = location.pathname === '/';

  // Add appropriate classes when on special pages
  useEffect(() => {
    if (isContentPage) {
      document.body.classList.add('content-page');
      document.documentElement.style.overflow = 'auto';
      document.body.style.overflow = 'auto';
      document.getElementById('root')?.classList.add('content-page-root');
    } else if (isLoginPage) {
      document.body.classList.add('login-page');
      document.getElementById('root')?.classList.add('login-page-root');
    } else {
      document.body.classList.remove('content-page', 'login-page');
      document.documentElement.style.overflow = '';
      document.body.style.overflow = '';
      document.getElementById('root')?.classList.remove('content-page-root', 'login-page-root');
    }
    
    return () => {
      document.body.classList.remove('content-page', 'login-page');
      document.documentElement.style.overflow = '';
      document.body.style.overflow = '';
      document.getElementById('root')?.classList.remove('content-page-root', 'login-page-root');
    };
  }, [isContentPage, isLoginPage]);

  // Se estivermos na página de conteúdo, render apenas o conteúdo sem nenhum wrapper adicional
  if (isContentPage) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AppLayout userInfo={userInfo} serverInfo={serverInfo}>
          <ContentSelection />
        </AppLayout>
      </ThemeProvider>
    );
  }

  // Se estivermos na página de login, render apenas o componente de acesso sem wrappers adicionais
  if (isLoginPage) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <LoginAppBar>
          <Toolbar
            variant="dense"
            sx={{
              minHeight: { xs: 40, sm: 48 },
              justifyContent: 'flex-end',
              padding: { xs: '0 8px', sm: '0 16px' }
            }}
          >
            <WindowControls />
          </Toolbar>
        </LoginAppBar>
        <EnhancedAccess setUserInfo={setUserInfo} setServerInfo={setServerInfo} />
      </ThemeProvider>
    );
  }

  // Render normal para as outras páginas
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {/* Pré-carregador do ticker para melhor performance */}
      <TickerPreloader />
      <AppLayout userInfo={userInfo} serverInfo={serverInfo}>
        <Routes>
          <Route path="/content" element={<ContentSelection />} />
          <Route
            path="/channels"
            element={
              <ProtectedRoute>
                <ChannelPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/movies"
            element={
              <ProtectedRoute>
                <MoviesPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/series"
            element={
              <ProtectedRoute>
                <SeriesPage />
              </ProtectedRoute>
            }
          />
          <Route path="/player" element={<Player />} />
          <Route path="/access" element={<Navigate to="/" replace />} />
          <Route path="*" element={<Navigate to="/content" replace />} />
        </Routes>
      </AppLayout>

      {/* Modal de Atualização IPTV */}
      <UpdateModal
        open={showModal}
        onClose={dismissModal}
        onUpdate={handleUpdate}
        updates={updates}
        loading={updateLoading}
      />
    </ThemeProvider>
  );
};

export default App;
