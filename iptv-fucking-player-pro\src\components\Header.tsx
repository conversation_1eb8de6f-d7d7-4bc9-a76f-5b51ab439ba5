import React from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiGlobe } from 'react-icons/fi';

const Header: React.FC = () => {
  const { t, i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <header className="bg-secondary-800 border-b border-secondary-700 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center flex-1 max-w-md">
          <div className="relative w-full">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-4 w-4" />
            <input
              type="text"
              placeholder={t('common:search')}
              className="input-field pl-10 w-full"
            />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative">
            <select
              value={i18n.language}
              onChange={(e) => changeLanguage(e.target.value)}
              className="input-field pr-8 appearance-none cursor-pointer"
            >
              <option value="en-US">English</option>
              <option value="pt-BR">Português</option>
              <option value="es-ES">Español</option>
            </select>
            <FiGlobe className="absolute right-2 top-1/2 transform -translate-y-1/2 text-secondary-400 h-4 w-4 pointer-events-none" />
          </div>

          <button className="p-2 rounded-lg bg-secondary-700 hover:bg-secondary-600 transition-colors">
            <FiUser className="h-5 w-5 text-secondary-300" />
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;