/**
 * Utilitário para organizar e agrupar canais IPTV de forma inteligente
 */

export interface ChannelVariant {
  id: string;
  name: string;
  url: string;
  logo?: string;
  quality: 'SD' | 'HD' | 'FHD' | '4K';
  isAlternative: boolean;
  isH265: boolean;
  originalName: string;
}

export interface ChannelGroup {
  baseId: string;
  baseName: string;
  logo?: string;
  category: string;
  variants: ChannelVariant[];
  bestQuality: ChannelVariant;
  hasAlternatives: boolean;
}

/**
 * Extrai informações de qualidade do nome do canal
 */
export function extractQualityInfo(channelName: string): {
  baseName: string;
  quality: 'SD' | 'HD' | 'FHD' | '4K';
  isAlternative: boolean;
  isH265: boolean;
} {
  const name = channelName.trim();
  
  // Detectar qualidade
  let quality: 'SD' | 'HD' | 'FHD' | '4K' = 'SD';
  if (name.includes('4K')) quality = '4K';
  else if (name.includes('FHD')) quality = 'FHD';
  else if (name.includes('HD')) quality = 'HD';
  
  // Detectar se é alternativo
  const isAlternative = name.includes('[Alt]') || name.includes('Alt');
  
  // Detectar se é H265
  const isH265 = name.includes('[H265]') || name.includes('H265');
  
  // Extrair nome base removendo qualificadores
  const baseName = name
    .replace(/\s*(4K|FHD|HD|SD)\s*/gi, '')
    .replace(/\s*\[Alt\]\s*/gi, '')
    .replace(/\s*\[H265\]\s*/gi, '')
    .replace(/\s*Alt\s*/gi, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  return { baseName, quality, isAlternative, isH265 };
}

/**
 * Gera um ID base para agrupamento de canais similares
 */
export function generateBaseId(channelName: string, tvgId?: string): string {
  // Usar tvg-id se disponível e válido
  if (tvgId && tvgId.trim() && tvgId !== '') {
    return tvgId.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
  
  // Gerar ID baseado no nome limpo
  const { baseName } = extractQualityInfo(channelName);
  return baseName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '')
    .substring(0, 50);
}

/**
 * Organiza uma lista de canais em grupos inteligentes
 */
export function organizeChannels(channels: any[]): ChannelGroup[] {
  const groupsMap = new Map<string, ChannelGroup>();
  
  channels.forEach((channel, index) => {
    const channelName = channel.title || channel.name || `Canal ${index + 1}`;
    const tvgId = channel.tvgId || channel['tvg-id'];
    const logo = channel.logo || channel['tvg-logo'];
    const category = channel.group || channel['group-title'] || 'Sem Categoria';
    
    const qualityInfo = extractQualityInfo(channelName);
    const baseId = generateBaseId(channelName, tvgId);
    
    const variant: ChannelVariant = {
      id: `${baseId}_${qualityInfo.quality}_${qualityInfo.isAlternative ? 'alt' : 'main'}_${index}`,
      name: channelName,
      url: channel.url,
      logo,
      quality: qualityInfo.quality,
      isAlternative: qualityInfo.isAlternative,
      isH265: qualityInfo.isH265,
      originalName: channelName
    };
    
    if (groupsMap.has(baseId)) {
      // Adicionar variante ao grupo existente
      const group = groupsMap.get(baseId)!;
      group.variants.push(variant);
      group.hasAlternatives = group.variants.some(v => v.isAlternative);
      
      // Atualizar melhor qualidade
      if (getQualityScore(variant.quality) > getQualityScore(group.bestQuality.quality)) {
        group.bestQuality = variant;
      }
    } else {
      // Criar novo grupo
      const group: ChannelGroup = {
        baseId,
        baseName: qualityInfo.baseName,
        logo,
        category,
        variants: [variant],
        bestQuality: variant,
        hasAlternatives: false
      };
      groupsMap.set(baseId, group);
    }
  });
  
  return Array.from(groupsMap.values())
    .sort((a, b) => a.baseName.localeCompare(b.baseName));
}

/**
 * Retorna pontuação numérica para qualidade (maior = melhor)
 */
function getQualityScore(quality: string): number {
  switch (quality) {
    case '4K': return 4;
    case 'FHD': return 3;
    case 'HD': return 2;
    case 'SD': return 1;
    default: return 0;
  }
}

/**
 * Filtra grupos por categoria
 */
export function filterByCategory(groups: ChannelGroup[], category: string): ChannelGroup[] {
  if (!category || category === 'all') return groups;
  
  return groups.filter(group => 
    group.category.toLowerCase().includes(category.toLowerCase())
  );
}

/**
 * Busca grupos por nome
 */
export function searchGroups(groups: ChannelGroup[], searchTerm: string): ChannelGroup[] {
  if (!searchTerm.trim()) return groups;
  
  const term = searchTerm.toLowerCase();
  return groups.filter(group =>
    group.baseName.toLowerCase().includes(term) ||
    group.variants.some(variant => 
      variant.name.toLowerCase().includes(term)
    )
  );
}

/**
 * Obtém estatísticas dos grupos
 */
export function getChannelStats(groups: ChannelGroup[]): {
  totalGroups: number;
  totalVariants: number;
  qualityDistribution: Record<string, number>;
  categoryDistribution: Record<string, number>;
  alternativesCount: number;
} {
  const stats = {
    totalGroups: groups.length,
    totalVariants: 0,
    qualityDistribution: {} as Record<string, number>,
    categoryDistribution: {} as Record<string, number>,
    alternativesCount: 0
  };
  
  groups.forEach(group => {
    stats.totalVariants += group.variants.length;
    
    // Contar por categoria
    stats.categoryDistribution[group.category] = 
      (stats.categoryDistribution[group.category] || 0) + 1;
    
    // Contar alternativas
    if (group.hasAlternatives) {
      stats.alternativesCount++;
    }
    
    // Contar por qualidade
    group.variants.forEach(variant => {
      stats.qualityDistribution[variant.quality] = 
        (stats.qualityDistribution[variant.quality] || 0) + 1;
    });
  });
  
  return stats;
}

/**
 * Converte grupo de volta para formato de canal simples (compatibilidade)
 */
export function groupToChannel(group: ChannelGroup, preferredQuality?: string): any {
  let selectedVariant = group.bestQuality;
  
  // Tentar encontrar qualidade preferida
  if (preferredQuality) {
    const preferred = group.variants.find(v => 
      v.quality === preferredQuality && !v.isAlternative
    );
    if (preferred) selectedVariant = preferred;
  }
  
  return {
    id: group.baseId,
    title: group.baseName,
    name: group.baseName,
    url: selectedVariant.url,
    logo: group.logo,
    group: group.category,
    'group-title': group.category,
    'tvg-logo': group.logo,
    quality: selectedVariant.quality,
    hasAlternatives: group.hasAlternatives,
    variantCount: group.variants.length,
    variants: group.variants
  };
}
