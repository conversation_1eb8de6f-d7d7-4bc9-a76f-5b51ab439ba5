import { TickerConfig } from '../components/MessageTicker';
import { corsProxyService } from './corsProxyService';
import storageService from './storageService';

/**
 * Serviço para gerenciar mensagens do ticker via Pastebin Raw
 */
class MessageTickerService {
  private static instance: MessageTickerService;
  private cache: TickerConfig | null = null;
  private lastFetch: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutos
  private readonly STORAGE_KEY = 'neko_ticker_config';
  private readonly STORAGE_TIMESTAMP_KEY = 'neko_ticker_timestamp';

  // URL padrão do Pastebin Raw (mais confiável que GitHub para CORS)
  private readonly DEFAULT_CONFIG_URL = 'https://pastebin.com/raw/qfZnbqc9';

  // Flag para indicar se devemos usar apenas configuração local
  private useLocalOnly: boolean = false;

  // Promise para evitar múltiplas requisições simultâneas
  private fetchPromise: Promise<TickerConfig> | null = null;
  
  private constructor() {
    // Inicializa o cache de forma assíncrona
    this.loadFromStorage().catch(error => {
      console.error('Erro ao inicializar cache do ticker:', error);
    });
  }

  public static getInstance(): MessageTickerService {
    if (!MessageTickerService.instance) {
      MessageTickerService.instance = new MessageTickerService();
    }
    return MessageTickerService.instance;
  }

  /**
   * Carrega configuração do cache local
   */
  private async loadFromStorage(): Promise<void> {
    try {
      const stored = await storageService.get<string>(this.STORAGE_KEY);
      const timestamp = await storageService.get<string>(this.STORAGE_TIMESTAMP_KEY);
      
      if (stored && timestamp) {
        this.cache = JSON.parse(stored);
        this.lastFetch = parseInt(timestamp);
      }
    } catch (error) {
      console.warn('Erro ao carregar cache do ticker:', error);
      await this.clearStorage();
    }
  }

  /**
   * Salva configuração no cache local
   */
  private async saveToStorage(config: TickerConfig): Promise<void> {
    try {
      await storageService.set(this.STORAGE_KEY, JSON.stringify(config));
      await storageService.set(this.STORAGE_TIMESTAMP_KEY, Date.now().toString());
    } catch (error) {
      console.warn('Erro ao salvar cache do ticker:', error);
    }
  }

  /**
   * Limpa o cache local
   */
  private async clearStorage(): Promise<void> {
    await storageService.remove(this.STORAGE_KEY);
    await storageService.remove(this.STORAGE_TIMESTAMP_KEY);
    this.cache = null;
    this.lastFetch = 0;
  }

  /**
   * Verifica se o cache ainda é válido
   */
  private isCacheValid(): boolean {
    return this.cache !== null && (Date.now() - this.lastFetch) < this.CACHE_DURATION;
  }

  /**
   * Busca configuração local para desenvolvimento
   */
  private async fetchLocalConfig(): Promise<TickerConfig> {
    try {
      const response = await fetch('/ticker-config.json', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
        },
        cache: 'no-cache',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const config = await response.json();
      return config;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Configura o serviço para usar apenas configuração local
   */
  public setUseLocalOnly(value: boolean): void {
    this.useLocalOnly = value;
  }

  /**
   * Retorna se o serviço está configurado para usar apenas configuração local
   */
  public getUseLocalOnly(): boolean {
    return this.useLocalOnly;
  }

  /**
   * Busca configuração do Pastebin Raw - SEMPRE tenta remoto primeiro
   */
  private async fetchFromPastebin(url?: string): Promise<TickerConfig> {
    const configUrl = url || this.DEFAULT_CONFIG_URL;

    // Se configurado para usar apenas local, nem tenta remoto
    if (this.useLocalOnly) {
      return await this.fetchLocalConfig();
    }

    let config: TickerConfig;

    // Estratégia 1: Fetch direto (mais rápido quando funciona)
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // Aumentado para 5s

      const response = await fetch(`${configUrl}?t=${Date.now()}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache',
        },
        cache: 'no-cache',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        config = await response.json();
        if (this.validateConfig(config)) {
          return config;
        }
      }
      throw new Error(`HTTP ${response.status}`);
    } catch (directError) {
      // Estratégia 2: Proxy CORS (mais confiável para CORS)
      try {
        const response = await corsProxyService.fetchWithProxy(configUrl);
        config = await response.json();

        if (this.validateConfig(config)) {
          return config;
        }
        throw new Error('Configuração inválida do proxy');
      } catch (proxyError) {
        // Estratégia 3: Configuração local como último recurso
        try {
          const localConfig = await this.fetchLocalConfig();
          if (this.validateConfig(localConfig)) {
            return localConfig;
          }
        } catch (localError) {
          // Ignora erro local
        }

        // Se tudo falhou, lança erro
        throw new Error(`Falha em todas as tentativas: Direct(${directError}), Proxy(${proxyError})`);
      }
    }
  }

  /**
   * Valida a estrutura da configuração
   */
  private validateConfig(config: any): config is TickerConfig {
    return (
      typeof config === 'object' &&
      typeof config.enabled === 'boolean' &&
      Array.isArray(config.messages) &&
      config.messages.every((msg: any) => 
        typeof msg.id === 'string' &&
        typeof msg.text === 'string'
      )
    );
  }

  /**
   * Configuração padrão de fallback
   */
  private getDefaultConfig(): TickerConfig {
    return {
      enabled: true,
      messages: [
        {
          id: 'promocao',
          text: '🔥 Oferta especial! ',
          link: {
            text: 'Aproveite agora',
            url: 'https://nekotv.top/promocao',
            target: '_blank'
          },
          duration: 20,
          priority: 1,
          enabled: true
        },
        {
          id: 'welcome',
          text: '🎉 Bem-vindo ao Neko TV! ',
          link: {
            text: 'Site oficial',
            url: 'https://nekotv.vercel.app',
            target: '_blank'
          },
          suffix: ' - A melhor experiência de streaming!',
          duration: 15,
          priority: 2,
          enabled: true
        },
        {
          id: 'quality',
          text: '📺 Assista em 4K, Full HD e HD com a melhor qualidade!',
          duration: 12,
          priority: 3,
          enabled: true
        }
      ],
      defaultDuration: 15,
      allowClose: true
    };
  }

  /**
   * Obtém a configuração atual - SEMPRE tenta buscar do Pastebin primeiro
   */
  public async getConfig(forceRefresh: boolean = false, customUrl?: string): Promise<TickerConfig> {
    // SEMPRE tenta buscar do Pastebin primeiro (prioridade máxima)
    // Só usa cache se for explicitamente solicitado E válido
    if (!forceRefresh && this.isCacheValid() && this.cache) {
      // Mesmo com cache válido, tenta atualizar em background
      if (!this.fetchPromise) {
        this.fetchPromise = this.fetchFromPastebin(customUrl)
          .then(config => {
            this.cache = config;
            this.lastFetch = Date.now();
            this.saveToStorage(config).catch(() => {});
            return config;
          })
          .catch(() => {
            return this.cache!;
          })
          .finally(() => {
            this.fetchPromise = null;
          });
      }
      return this.cache;
    }

    // Se já existe uma requisição em andamento, aguarda ela
    if (this.fetchPromise) {
      return this.fetchPromise;
    }

    try {
      // SEMPRE tenta buscar do Pastebin primeiro
      this.fetchPromise = this.fetchFromPastebin(customUrl);
      const config = await this.fetchPromise;

      // Atualiza cache
      this.cache = config;
      this.lastFetch = Date.now();

      // Salva no storage
      this.saveToStorage(config).catch(() => {});

      return config;
    } catch (error) {
      // Se falhou, tenta cache como fallback
      if (this.cache) {
        return this.cache;
      }

      // Último recurso: configuração padrão
      const defaultConfig = this.getDefaultConfig();
      this.cache = defaultConfig;
      this.saveToStorage(defaultConfig).catch(() => {});

      return defaultConfig;
    } finally {
      this.fetchPromise = null;
    }
  }

  /**
   * Obtém configuração de forma segura com múltiplos fallbacks
   */
  public async getConfigSafe(): Promise<TickerConfig> {
    try {
      return await this.getConfig();
    } catch (error) {
      // Último recurso: configuração mínima
      return {
        enabled: false,
        messages: [],
        defaultDuration: 15,
        allowClose: true
      };
    }
  }

  /**
   * Pré-carrega configuração em background
   */
  public async preloadConfig(customUrl?: string): Promise<void> {
    try {
      await this.getConfig(false, customUrl);
    } catch (error) {
      // Silencioso - não é crítico
    }
  }

  /**
   * Força atualização do Pastebin (ignora cache)
   */
  public async forceUpdateFromPastebin(customUrl?: string): Promise<TickerConfig> {
    // Limpa cache para forçar busca
    this.cache = null;
    this.lastFetch = 0;

    // Busca diretamente do Pastebin
    return await this.getConfig(true, customUrl);
  }

  /**
   * Força atualização da configuração
   */
  public async refreshConfig(customUrl?: string): Promise<TickerConfig> {
    return this.getConfig(true, customUrl);
  }

  /**
   * Limpa cache e redefine para padrão
   */
  public async resetToDefault(): Promise<TickerConfig> {
    await this.clearStorage();
    const defaultConfig = this.getDefaultConfig();
    this.cache = defaultConfig;
    await this.saveToStorage(defaultConfig);
    return defaultConfig;
  }

  /**
   * Verifica se há atualizações disponíveis
   */
  public async checkForUpdates(customUrl?: string): Promise<boolean> {
    try {
      const newConfig = await this.fetchFromPastebin(customUrl);

      if (!this.cache) {
        return true;
      }

      // Compara versões simples (pode ser melhorado com hash)
      return JSON.stringify(newConfig) !== JSON.stringify(this.cache);
    } catch (error) {
      return false;
    }
  }

  /**
   * Obtém informações do cache
   */
  public getCacheInfo(): { hasCache: boolean; lastFetch: Date | null; isValid: boolean } {
    return {
      hasCache: this.cache !== null,
      lastFetch: this.lastFetch > 0 ? new Date(this.lastFetch) : null,
      isValid: this.isCacheValid()
    };
  }

  /**
   * Define URL customizada para configuração
   */
  public async setConfigUrl(url: string): Promise<void> {
    // Valida URL básica
    try {
      new URL(url);
      await storageService.set('neko_ticker_custom_url', url);
    } catch (error) {
      throw new Error('URL inválida fornecida');
    }
  }

  /**
   * Obtém URL customizada ou padrão
   */
  public getConfigUrl(): string {
    // Versão síncrona para compatibilidade com a interface
    try {
      const customUrl = localStorage.getItem('neko_ticker_custom_url');
      return customUrl || this.DEFAULT_CONFIG_URL;
    } catch (error) {
      return this.DEFAULT_CONFIG_URL;
    }
  }

  /**
   * Remove URL customizada
   */
  public async clearCustomUrl(): Promise<void> {
    await storageService.remove('neko_ticker_custom_url');
  }
}

// Exporta instância singleton
export const messageTickerService = MessageTickerService.getInstance();

// Função global para debug (disponível no console)
if (typeof window !== 'undefined') {
  (window as any).debugTicker = {
    forceUpdate: async () => {
      try {
        console.log('🔄 Forçando atualização do Pastebin...');
        const config = await messageTickerService.forceUpdateFromPastebin();
        console.log('✅ Configuração atualizada:', config);
        return config;
      } catch (error) {
        console.error('❌ Erro ao atualizar:', error);
        throw error;
      }
    },
    getCache: () => {
      const cacheInfo = messageTickerService.getCacheInfo();
      console.log('📦 Info do cache:', cacheInfo);
      return cacheInfo;
    },
    clearCache: () => {
      messageTickerService.clearCache();
      console.log('🗑️ Cache limpo');
    },
    testPastebin: async () => {
      try {
        console.log('🧪 Testando acesso ao Pastebin...');
        const response = await fetch('https://pastebin.com/raw/qfZnbqc9?t=' + Date.now());
        console.log('📡 Status:', response.status);
        if (response.ok) {
          const text = await response.text();
          console.log('📄 Conteúdo:', text);
          return JSON.parse(text);
        }
      } catch (error) {
        console.error('❌ Erro no teste:', error);
        throw error;
      }
    }
  };

  console.log('🛠️ Debug do ticker disponível:');
  console.log('debugTicker.forceUpdate() - Força atualização do Pastebin');
  console.log('debugTicker.getCache() - Mostra info do cache');
  console.log('debugTicker.clearCache() - Limpa cache');
  console.log('debugTicker.testPastebin() - Testa acesso direto ao Pastebin');
}
export default messageTickerService;
