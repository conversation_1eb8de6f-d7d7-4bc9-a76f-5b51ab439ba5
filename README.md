# Neko TV - App Pronto para Deploy

Esta pasta contém todos os arquivos necessários para fazer o build e deploy do Neko TV no Vercel.

## Como usar:

1. Instalar dependências:
```bash
npm install
```

2. Fazer build local (opcional):
```bash
npm run build
```

3. Deploy no Vercel:
```bash
npx vercel --prod
```

## Estrutura:
- `src/` - C<PERSON>digo fonte da aplicação
- `public/` - Assets públicos
- `scripts/` - Scripts de build
- `package.json` - Dependências e scripts
- `vercel.json` - Configuração do Vercel
- `vite.config.ts` - Configuração do Vite

## Build final esperado:
- Tamanho total: ~3-4MB
- Assets principais:
  - index.html: ~3.19 kB
  - CSS: ~84 kB (4.81 kB + 79.27 kB)
  - JS: ~3MB (577B + 516KB + 1.13MB + 1.31MB)

Criado automaticamente em 29/06/2025, 09:48:44
