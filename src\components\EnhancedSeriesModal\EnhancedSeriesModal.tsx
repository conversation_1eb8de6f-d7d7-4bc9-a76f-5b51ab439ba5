import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { keyframes } from '@emotion/react';
import './EnhancedSeriesModal.css';
import {
  Dialog,
  Box,
  Typography,
  IconButton,
  Card,
  CardMedia,
  CardContent,
  Button,
  Chip,
  useTheme,
  alpha,
  Slide,
  Stack,
  LinearProgress,
  Tooltip,
  Badge,
  Switch,
  FormControlLabel,
  Slider,
  Collapse,
  Grid,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  ListItemButton,
  Container,
  Tabs,
  Tab,
  AppBar,
  Fade,
  Zoom,
  ButtonGroup,
  Menu,
  MenuItem
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import CloseIcon from '@mui/icons-material/Close';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StarIcon from '@mui/icons-material/Star';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import ShareIcon from '@mui/icons-material/Share';
import VisibilityIcon from '@mui/icons-material/Visibility';
import TvIcon from '@mui/icons-material/Tv';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import SettingsIcon from '@mui/icons-material/Settings';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import FastForwardIcon from '@mui/icons-material/FastForward';
import PlaylistPlayIcon from '@mui/icons-material/PlaylistPlay';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import MovieIcon from '@mui/icons-material/Movie';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import SmartDisplayIcon from '@mui/icons-material/SmartDisplay';
import SpeedIcon from '@mui/icons-material/Speed';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import ViewListIcon from '@mui/icons-material/ViewList';
import GridViewIcon from '@mui/icons-material/GridView';
import FilterListIcon from '@mui/icons-material/FilterList';
import { Season, Episode as IPTVEpisode } from '../../services/iptvService';

// Tipos locais compatíveis com o modal
interface Episode extends Partial<IPTVEpisode> {
  id: string;
  title: string;
  episode: number;
  thumbnail?: string;
  duration?: number;
  description?: string;
  airDate?: string;
  rating?: number;
  watched?: boolean;
  progress?: number;
}

interface SeriesDetails {
  id: string;
  name: string;
  description?: string;
  overview?: string;
  thumbnail?: string;
  cover?: string;
  rating?: number;
  year?: string;
  genres?: string[];
  status?: string;
  network?: string;
  runtime?: number;
  totalSeasons?: number;
  totalEpisodes?: number;
}

interface AutoSkipSettings {
  enabled: boolean;
  skipIntroTime: number;
  skipCreditsTime: number;
  autoPlayNext: boolean;
}

interface EnhancedSeriesModalProps {
  open: boolean;
  onClose: () => void;
  series: SeriesDetails | null;
  seasons: Season[];
  selectedSeason: Season | null;
  episodes: Episode[];
  onSeasonSelect: (season: Season) => void | Promise<void>;
  onEpisodePlay: (episode: Episode) => void;
  loading?: boolean;
  autoSkipSettings?: AutoSkipSettings;
  onAutoSkipChange?: (settings: AutoSkipSettings) => void;
}

const MotionCard = motion.create(Card);
const MotionBox = motion.create(Box);
const MotionPaper = motion.create(Paper);
const MotionButton = motion.create(Button);

// Animações personalizadas
const glowAnimation = keyframes`
  0%, 100% {
    box-shadow: 0 0 5px rgba(14, 165, 233, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.6), 0 0 30px rgba(14, 165, 233, 0.4);
  }
`;

const slideInAnimation = keyframes`
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const floatAnimation = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
`;

const EnhancedSeriesModal: React.FC<EnhancedSeriesModalProps> = ({
  open,
  onClose,
  series,
  seasons,
  selectedSeason,
  episodes,
  onSeasonSelect,
  onEpisodePlay,
  loading = false,
  autoSkipSettings = {
    enabled: false,
    skipIntroTime: 10,
    skipCreditsTime: 30,
    autoPlayNext: true
  },
  onAutoSkipChange
}) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [selectedSeasonIndex, setSelectedSeasonIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showAutoSkipPanel, setShowAutoSkipPanel] = useState(false);
  const [localAutoSkipSettings, setLocalAutoSkipSettings] = useState<AutoSkipSettings>(autoSkipSettings);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  useEffect(() => {
    if (selectedSeason) {
      const seasonIndex = seasons.findIndex(s => s.id === selectedSeason.id);
      if (seasonIndex !== -1) {
        setSelectedSeasonIndex(seasonIndex);
      }
    }
  }, [selectedSeason, seasons]);

  useEffect(() => {
    setLocalAutoSkipSettings(autoSkipSettings);
  }, [autoSkipSettings]);

  // Otimização: Limitar episódios renderizados para melhor performance
  const maxEpisodesPerPage = 50;
  const [episodePage, setEpisodePage] = useState(0);

  // Todos os hooks devem vir ANTES de qualquer return condicional
  const handleSeasonChange = useCallback((seasonIndex: number) => {
    setSelectedSeasonIndex(seasonIndex);
    if (seasons[seasonIndex]) {
      onSeasonSelect(seasons[seasonIndex]);
    }
  }, [seasons, onSeasonSelect]);

  const handleAutoSkipSettingsChange = useCallback((newSettings: Partial<AutoSkipSettings>) => {
    const updatedSettings = { ...localAutoSkipSettings, ...newSettings };
    setLocalAutoSkipSettings(updatedSettings);
    onAutoSkipChange?.(updatedSettings);
  }, [localAutoSkipSettings, onAutoSkipChange]);

  // Memoizar cálculos custosos
  const watchedEpisodesCount = useMemo(() => {
    return episodes.filter(ep => ep.watched).length;
  }, [episodes]);

  const seriesProgress = useMemo(() => {
    const totalEpisodes = episodes.length;
    return totalEpisodes > 0 ? (watchedEpisodesCount / totalEpisodes) * 100 : 0;
  }, [episodes.length, watchedEpisodesCount]);

  const visibleEpisodes = useMemo(() => {
    const startIndex = episodePage * maxEpisodesPerPage;
    const endIndex = startIndex + maxEpisodesPerPage;
    return episodes.slice(startIndex, endIndex);
  }, [episodes, episodePage, maxEpisodesPerPage]);

  const totalPages = Math.ceil(episodes.length / maxEpisodesPerPage);
  const hasMoreEpisodes = episodes.length > maxEpisodesPerPage;

  // Return condicional APÓS todos os hooks
  if (!series) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      fullWidth
      sx={{
        zIndex: 50000,
        '& .MuiDialog-container': {
          zIndex: 50000
        },
        '& .MuiDialog-paper': {
          zIndex: 50000
        }
      }}
      slotProps={{
        paper: {
          sx: {
            width: '100vw',
            height: '100vh',
            maxWidth: 'none',
            maxHeight: 'none',
            borderRadius: 0,
            overflow: 'hidden',
            bgcolor: 'transparent',
            boxShadow: 'none',
            m: 0,
            zIndex: 50000
          }
        },
        backdrop: {
          sx: {
            bgcolor: 'rgba(0, 0, 0, 0.95)',
            backdropFilter: 'blur(20px)',
            zIndex: 49999
          }
        }
      }}
    >
      <Fade in={open} timeout={600}>
        <Box
          className={`enhanced-series-modal ${open ? 'open' : ''}`}
          sx={{
            width: '100%',
            height: '100%',
            background: `linear-gradient(135deg, 
              ${alpha(theme.palette.background.default, 0.95)} 0%, 
              ${alpha(theme.palette.background.paper, 0.98)} 50%,
              ${alpha(theme.palette.primary.dark, 0.1)} 100%)`,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Hero Background */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '35vh',
              backgroundImage: `url(${series.cover || series.thumbnail})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              filter: 'brightness(0.3) contrast(1.1)',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(to bottom, 
                  rgba(0,0,0,0.1) 0%, 
                  rgba(0,0,0,0.4) 30%,
                  rgba(0,0,0,0.8) 60%,
                  ${theme.palette.background.default} 100%)`
              }
            }}
          />

          {/* Header Controls */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 10,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              background: `linear-gradient(to bottom, 
                rgba(0,0,0,0.8) 0%, 
                rgba(0,0,0,0.4) 70%,
                transparent 100%)`
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <MotionBox
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring" }}
              >
                <IconButton
                  onClick={onClose}
                  sx={{
                    bgcolor: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(10px)',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.background.paper, 1),
                      transform: 'scale(1.1)'
                    },
                    transition: 'all 0.3s'
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </MotionBox>

              <Typography
                variant="h6"
                sx={{
                  color: 'white',
                  fontWeight: 600,
                  textShadow: '0 2px 10px rgba(0,0,0,0.8)'
                }}
              >
                Detalhes da Série
              </Typography>
            </Box>

            <Stack direction="row" spacing={1}>
              <Tooltip title={isFavorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}>
                <MotionButton
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsFavorite(!isFavorite)}
                  sx={{
                    bgcolor: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(10px)',
                    color: isFavorite ? theme.palette.error.main : 'inherit',
                    minWidth: 'auto',
                    px: 2
                  }}
                >
                  {isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                </MotionButton>
              </Tooltip>

              <Tooltip title={isBookmarked ? "Remover da lista" : "Adicionar à lista"}>
                <MotionButton
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsBookmarked(!isBookmarked)}
                  sx={{
                    bgcolor: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(10px)',
                    color: isBookmarked ? theme.palette.primary.main : 'inherit',
                    minWidth: 'auto',
                    px: 2
                  }}
                >
                  {isBookmarked ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                </MotionButton>
              </Tooltip>

              <Tooltip title="Mais opções">
                <MotionButton
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={(e) => setMenuAnchor(e.currentTarget)}
                  sx={{
                    bgcolor: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(10px)',
                    minWidth: 'auto',
                    px: 2
                  }}
                >
                  <MoreVertIcon />
                </MotionButton>
              </Tooltip>
            </Stack>
          </Box>

          {/* Main Content */}
          <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 2, pt: 16, pb: 4, height: '100%', overflow: 'auto' }}>
            <Grid container spacing={5} sx={{ minHeight: 'calc(100vh - 200px)', height: 'auto' }}>
              {/* Left Panel - Series Info */}
              <Grid item xs={12} md={4} lg={3} className="left-panel" sx={{ 
                height: 'fit-content',
                overflow: 'visible'
              }}>
                <MotionBox
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <Stack spacing={2} sx={{ width: '100%', alignItems: 'center' }}>
                    {/* Poster */}
                    <Card
                      className="series-poster fade-in"
                      sx={{
                        borderRadius: 2.5,
                        overflow: 'hidden',
                        boxShadow: `0 15px 30px ${alpha(theme.palette.common.black, 0.3)}`,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                        position: 'relative',
                        maxWidth: '200px',
                        mx: 'auto'
                      }}
                    >
                      <CardMedia
                        component="img"
                        image={series.thumbnail || series.cover}
                        alt={series.name}
                        sx={{
                          aspectRatio: '2/3',
                          objectFit: 'cover',
                          maxHeight: '280px',
                          width: '100%'
                        }}
                      />

                      {/* Progress Ring */}
                      {seriesProgress > 0 && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 12,
                            width: 50,
                            height: 50,
                            borderRadius: '50%',
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                            backdropFilter: 'blur(10px)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: `3px solid ${theme.palette.primary.main}`,
                            animation: `${glowAnimation} 3s ease-in-out infinite`
                          }}
                        >
                          <Typography variant="caption" sx={{ fontWeight: 700, fontSize: '0.8rem' }}>
                            {Math.round(seriesProgress)}%
                          </Typography>
                        </Box>
                      )}
                    </Card>

                    {/* Series Title */}
                    <Box>
                      <Typography
                        variant="h4"
                        className="series-title text-shadow"
                        sx={{
                          fontWeight: 700,
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                          mb: 1,
                          fontSize: { xs: '1.4rem', sm: '1.6rem', md: '1.8rem' },
                          textAlign: 'center',
                          lineHeight: 1.1
                        }}
                      >
                        {series.name}
                      </Typography>

                      {/* Rating and Year */}
                      <Stack direction="row" spacing={1.5} sx={{ mb: 2, justifyContent: 'center' }}>
                        {series.rating && (
                          <Chip
                            icon={<StarIcon />}
                            label={series.rating.toFixed(1)}
                            size="small"
                            sx={{
                              bgcolor: alpha(theme.palette.warning.main, 0.9),
                              color: 'white',
                              fontWeight: 700,
                              '& .MuiChip-icon': { color: 'white' }
                            }}
                          />
                        )}

                        {series.year && (
                          <Chip
                            icon={<CalendarTodayIcon />}
                            label={series.year}
                            size="small"
                            sx={{
                              bgcolor: alpha(theme.palette.info.main, 0.9),
                              color: 'white',
                              fontWeight: 600,
                              '& .MuiChip-icon': { color: 'white' }
                            }}
                          />
                        )}
                      </Stack>

                      {/* Genres */}
                      {series.genres && series.genres.length > 0 && (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.8, mb: 2, justifyContent: 'center' }}>
                          {series.genres.slice(0, 3).map((genre, index) => (
                            <Chip
                              key={index}
                              label={genre}
                              size="small"
                              variant="outlined"
                              sx={{
                                borderColor: alpha(theme.palette.primary.main, 0.5),
                                color: theme.palette.primary.main,
                                fontSize: '0.7rem',
                                height: '24px',
                                '&:hover': {
                                  borderColor: theme.palette.primary.main,
                                  bgcolor: alpha(theme.palette.primary.main, 0.1)
                                }
                              }}
                            />
                          ))}
                        </Box>
                      )}

                      {/* Description */}
                      <Typography
                        variant="body2"
                        sx={{
                          color: 'text.secondary',
                          lineHeight: 1.4,
                          fontSize: '0.85rem',
                          textAlign: 'center',
                          maxHeight: '80px',
                          overflow: 'hidden',
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical'
                        }}
                      >
                        {series.overview || series.description || 'Nenhuma descrição disponível.'}
                      </Typography>
                    </Box>

                    {/* Enhanced Auto-Skip Panel */}
                    <MotionPaper
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                      sx={{
                        p: 2,
                        borderRadius: 2.5,
                        bgcolor: localAutoSkipSettings.enabled
                          ? `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.15)}, ${alpha(theme.palette.primary.main, 0.1)})`
                          : alpha(theme.palette.background.paper, 0.8),
                        border: `1px solid ${localAutoSkipSettings.enabled
                            ? alpha(theme.palette.success.main, 0.4)
                            : alpha(theme.palette.divider, 0.1)
                          }`,
                        backdropFilter: 'blur(15px)',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.15)}`
                        },
                        '&::before': localAutoSkipSettings.enabled ? {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '2px',
                          background: `linear-gradient(90deg, ${theme.palette.success.main}, ${theme.palette.primary.main})`,
                          animation: `${slideInAnimation} 1s ease-out`
                        } : {}
                      }}
                      onClick={() => setShowAutoSkipPanel(!showAutoSkipPanel)}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.2 }}>
                          <Box
                            sx={{
                              position: 'relative',
                              p: 0.8,
                              borderRadius: 1.5,
                              bgcolor: localAutoSkipSettings.enabled 
                                ? alpha(theme.palette.success.main, 0.2)
                                : alpha(theme.palette.primary.main, 0.1),
                              border: `1px solid ${localAutoSkipSettings.enabled 
                                ? alpha(theme.palette.success.main, 0.3)
                                : alpha(theme.palette.primary.main, 0.2)
                              }`
                            }}
                          >
                            <Badge
                              variant="dot"
                              color="success"
                              invisible={!localAutoSkipSettings.enabled}
                              sx={{
                                '& .MuiBadge-badge': {
                                  animation: localAutoSkipSettings.enabled ? `${glowAnimation} 2s infinite` : 'none',
                                  width: 8,
                                  height: 8
                                }
                              }}
                            >
                              <AutoFixHighIcon 
                                sx={{ 
                                  color: localAutoSkipSettings.enabled 
                                    ? theme.palette.success.main 
                                    : theme.palette.primary.main,
                                  fontSize: '1.2rem'
                                }} 
                              />
                            </Badge>
                          </Box>
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 700, fontSize: '1rem', mb: 0.1 }}>
                              Auto-Skip Inteligente
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                              {localAutoSkipSettings.enabled
                                ? `Ativo • Pula ${localAutoSkipSettings.skipCreditsTime}s dos créditos`
                                : 'Clique para configurar'
                              }
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {localAutoSkipSettings.enabled && (
                            <Chip
                              label="ON"
                              size="small"
                              sx={{
                                bgcolor: theme.palette.success.main,
                                color: 'white',
                                fontWeight: 700,
                                fontSize: '0.7rem',
                                animation: `${glowAnimation} 3s infinite`
                              }}
                            />
                          )}
                          <IconButton size="small" sx={{ color: 'text.secondary' }}>
                            {showAutoSkipPanel ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                          </IconButton>
                        </Box>
                      </Box>

                      <Collapse in={showAutoSkipPanel}>
                        <Divider sx={{ mb: 2, opacity: 0.3 }} />
                        <Box sx={{ space: 2 }}>
                          {/* Master Switch */}
                          <Box sx={{ 
                            p: 2, 
                            borderRadius: 2, 
                            bgcolor: alpha(theme.palette.background.paper, 0.5),
                            mb: 2
                          }}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={localAutoSkipSettings.enabled}
                                  onChange={(e) => handleAutoSkipSettingsChange({ enabled: e.target.checked })}
                                  sx={{
                                    '& .MuiSwitch-thumb': {
                                      bgcolor: localAutoSkipSettings.enabled ? theme.palette.success.main : (theme.palette.grey?.[400] || '#9e9e9e')
                                    },
                                    '& .MuiSwitch-track': {
                                      bgcolor: localAutoSkipSettings.enabled 
                                        ? alpha(theme.palette.success.main, 0.3)
                                        : alpha(theme.palette.grey[400], 0.3)
                                    }
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Ativar Auto-Skip
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Habilita reprodução automática inteligente
                                  </Typography>
                                </Box>
                              }
                              sx={{ m: 0 }}
                            />
                          </Box>

                          {localAutoSkipSettings.enabled && (
                            <Stack spacing={2.5}>
                              {/* Skip Credits */}
                              <Box sx={{ 
                                p: 2, 
                                borderRadius: 2, 
                                bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                border: `1px solid ${alpha(theme.palette.secondary.main, 0.2)}`
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                                  <FastForwardIcon sx={{ color: theme.palette.secondary.main, fontSize: '1rem' }} />
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Pular Créditos Finais
                                  </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
                                  Pula automaticamente para o próximo episódio quando faltam {localAutoSkipSettings.skipCreditsTime} segundos
                                </Typography>
                                <Box sx={{ px: 1 }}>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                    <Typography variant="caption">10s</Typography>
                                    <Typography variant="caption" sx={{ fontWeight: 600 }}>
                                      {localAutoSkipSettings.skipCreditsTime}s
                                    </Typography>
                                    <Typography variant="caption">2min</Typography>
                                  </Box>
                                  <Slider
                                    value={localAutoSkipSettings.skipCreditsTime}
                                    onChange={(_, value) => handleAutoSkipSettingsChange({ skipCreditsTime: value as number })}
                                    min={10}
                                    max={120}
                                    step={5}
                                    size="small"
                                    sx={{
                                      '& .MuiSlider-thumb': {
                                        bgcolor: theme.palette.secondary.main,
                                        width: 16,
                                        height: 16,
                                        '&:hover': {
                                          boxShadow: `0 0 0 8px ${alpha(theme.palette.secondary.main, 0.16)}`
                                        }
                                      },
                                      '& .MuiSlider-track': {
                                        bgcolor: theme.palette.secondary.main,
                                        height: 4
                                      },
                                      '& .MuiSlider-rail': {
                                        bgcolor: alpha(theme.palette.secondary.main, 0.2),
                                        height: 4
                                      }
                                    }}
                                  />
                                </Box>
                              </Box>

                              {/* Auto Play Next */}
                              <Box sx={{ 
                                p: 2, 
                                borderRadius: 2, 
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
                              }}>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={localAutoSkipSettings.autoPlayNext}
                                      onChange={(e) => handleAutoSkipSettingsChange({ autoPlayNext: e.target.checked })}
                                      size="small"
                                      sx={{
                                        '& .MuiSwitch-thumb': {
                                          bgcolor: localAutoSkipSettings.autoPlayNext ? theme.palette.primary.main : theme.palette.grey[400]
                                        }
                                      }}
                                    />
                                  }
                                  label={
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <SkipNextIcon sx={{ color: theme.palette.primary.main, fontSize: '1rem' }} />
                                      <Box>
                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                          Próximo Episódio Automático
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                          Reproduz o próximo episódio automaticamente
                                        </Typography>
                                      </Box>
                                    </Box>
                                  }
                                  sx={{ m: 0, alignItems: 'flex-start' }}
                                />
                              </Box>

                              {/* Status Info */}
                              <Box sx={{ 
                                p: 2, 
                                borderRadius: 2, 
                                bgcolor: alpha(theme.palette.info.main, 0.1),
                                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                  <InfoOutlinedIcon sx={{ color: theme.palette.info.main, fontSize: '1rem' }} />
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Como Funciona
                                  </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary" sx={{ lineHeight: 1.4 }}>
                                  • Detecta automaticamente quando os créditos começam<br/>
                                  • Pula para o próximo episódio sem interrupção<br/>
                                  • Funciona apenas quando ativado pelo usuário<br/>
                                  • Configurações salvas por série
                                </Typography>
                              </Box>
                            </Stack>
                          )}
                        </Box>
                      </Collapse>
                    </MotionPaper>
                  </Stack>
                </MotionBox>
              </Grid>

              {/* Right Panel - Episodes */}
              <Grid item xs={12} md={8} lg={9} sx={{ height: 'fit-content', overflow: 'visible' }}>
                <MotionBox
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
                >
                  {/* Navigation Header */}
                  <Paper
                    sx={{
                      bgcolor: alpha(theme.palette.background.paper, 0.9),
                      backdropFilter: 'blur(20px)',
                      borderRadius: 2,
                      p: 1.2,
                      mb: 1.5,
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      flexShrink: 0
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.2 }}>
                      <Typography variant="h6" sx={{ fontWeight: 700, fontSize: '1.2rem' }}>
                        Episódios
                      </Typography>

                      <ButtonGroup size="small" variant="outlined">
                        <Button
                          onClick={() => setViewMode('grid')}
                          variant={viewMode === 'grid' ? 'contained' : 'outlined'}
                          sx={{ minWidth: 'auto', px: 1.5 }}
                        >
                          <GridViewIcon fontSize="small" />
                        </Button>
                        <Button
                          onClick={() => setViewMode('list')}
                          variant={viewMode === 'list' ? 'contained' : 'outlined'}
                          sx={{ minWidth: 'auto', px: 1.5 }}
                        >
                          <ViewListIcon fontSize="small" />
                        </Button>
                      </ButtonGroup>
                    </Box>

                    {/* Compact Season Selector */}
                    <Box sx={{ 
                      overflowX: 'auto', 
                      pb: 1,
                      '&::-webkit-scrollbar': { height: 4 },
                      '&::-webkit-scrollbar-track': { bgcolor: alpha(theme.palette.background.paper, 0.1) },
                      '&::-webkit-scrollbar-thumb': { 
                        bgcolor: alpha(theme.palette.primary.main, 0.3),
                        borderRadius: 2
                      }
                    }}>
                      <Stack direction="row" spacing={1} sx={{ minWidth: 'max-content' }}>
                        {seasons.map((season, index) => (
                          <Chip
                            key={season.id}
                            label={season.name}
                            variant={selectedSeasonIndex === index ? 'filled' : 'outlined'}
                            onClick={() => handleSeasonChange(index)}
                            size="small"
                            sx={{
                              borderRadius: 2,
                              fontWeight: 600,
                              px: 1.5,
                              height: 28,
                              fontSize: '0.8rem',
                              flexShrink: 0,
                              ...(selectedSeasonIndex === index && {
                                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                color: 'white',
                                boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.3)}`
                              }),
                              '&:hover': {
                                transform: 'translateY(-1px)',
                                boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                              },
                              transition: 'all 0.2s ease'
                            }}
                          />
                        ))}
                      </Stack>
                    </Box>
                  </Paper>

                  {/* Episodes Content */}
                  <Box sx={{ flex: 1, overflow: 'hidden' }}>
                    <AnimatePresence mode="wait">
                      <MotionBox
                        key={selectedSeasonIndex}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        sx={{ height: '100%' }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            overflow: 'auto',
                            pr: 1,
                            '&::-webkit-scrollbar': {
                              width: 8
                            },
                            '&::-webkit-scrollbar-track': {
                              bgcolor: alpha(theme.palette.background.paper, 0.1),
                              borderRadius: 4
                            },
                            '&::-webkit-scrollbar-thumb': {
                              bgcolor: alpha(theme.palette.primary.main, 0.3),
                              borderRadius: 4,
                              '&:hover': {
                                bgcolor: alpha(theme.palette.primary.main, 0.5)
                              }
                            }
                          }}
                        >
                          {viewMode === 'grid' ? (
                            <Grid container spacing={2}>
                              {visibleEpisodes.map((episode, index) => (
                                <Grid item xs={12} sm={6} lg={4} key={episode.id}>
                                  <MotionCard
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: Math.min(index * 0.05, 1) }}
                                    whileHover={{
                                      y: -8,
                                      boxShadow: `0 12px 30px ${alpha(theme.palette.primary.main, 0.2)}`
                                    }}
                                    sx={{
                                      borderRadius: 3,
                                      overflow: 'hidden',
                                      bgcolor: alpha(theme.palette.background.paper, 0.8),
                                      backdropFilter: 'blur(20px)',
                                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                      transition: 'all 0.3s ease',
                                      position: 'relative',
                                      cursor: 'pointer'
                                    }}
                                    onClick={() => onEpisodePlay(episode)}
                                  >
                                    {/* Episode Thumbnail */}
                                    <Box sx={{ position: 'relative' }}>
                                      <CardMedia
                                        component="img"
                                        height="160"
                                        image={episode.thumbnail || series.thumbnail}
                                        alt={episode.title}
                                        sx={{
                                          objectFit: 'cover',
                                          filter: 'brightness(0.9)'
                                        }}
                                      />

                                      {/* Episode Number Badge */}
                                      <Chip
                                        label={`EP ${episode.episode}`}
                                        size="small"
                                        sx={{
                                          position: 'absolute',
                                          top: 8,
                                          left: 8,
                                          bgcolor: alpha(theme.palette.background.paper, 0.95),
                                          backdropFilter: 'blur(10px)',
                                          fontWeight: 700,
                                          fontSize: '0.7rem',
                                          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
                                        }}
                                      />

                                      {/* Watched Indicator */}
                                      {episode.watched && (
                                        <Box
                                          sx={{
                                            position: 'absolute',
                                            top: 8,
                                            right: 8,
                                            bgcolor: alpha(theme.palette.success.main, 0.9),
                                            borderRadius: '50%',
                                            p: 0.5,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                          }}
                                        >
                                          <CheckCircleIcon sx={{ color: 'white', fontSize: '1rem' }} />
                                        </Box>
                                      )}

                                      {/* Play Button Overlay */}
                                      <Box
                                        sx={{
                                          position: 'absolute',
                                          top: 0,
                                          left: 0,
                                          right: 0,
                                          bottom: 0,
                                          bgcolor: alpha(theme.palette.common.black, 0.4),
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          opacity: 0,
                                          transition: 'opacity 0.3s ease',
                                          '&:hover': {
                                            opacity: 1
                                          }
                                        }}
                                      >
                                        <IconButton
                                          sx={{
                                            bgcolor: alpha(theme.palette.primary.main, 0.9),
                                            color: 'white',
                                            '&:hover': {
                                              bgcolor: theme.palette.primary.main,
                                              transform: 'scale(1.1)'
                                            }
                                          }}
                                        >
                                          <PlayArrowIcon sx={{ fontSize: '2rem' }} />
                                        </IconButton>
                                      </Box>

                                      {/* Progress Bar */}
                                      {episode.progress && episode.progress > 0 && (
                                        <LinearProgress
                                          variant="determinate"
                                          value={episode.progress}
                                          sx={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            height: 4,
                                            '& .MuiLinearProgress-bar': {
                                              bgcolor: theme.palette.primary.main
                                            }
                                          }}
                                        />
                                      )}
                                    </Box>

                                    {/* Episode Info */}
                                    <CardContent sx={{ p: 2 }}>
                                      <Typography
                                        variant="h6"
                                        sx={{
                                          fontWeight: 600,
                                          fontSize: '1rem',
                                          lineHeight: 1.3,
                                          mb: 1,
                                          display: '-webkit-box',
                                          WebkitLineClamp: 2,
                                          WebkitBoxOrient: 'vertical',
                                          overflow: 'hidden',
                                          textOverflow: 'ellipsis'
                                        }}
                                      >
                                        {episode.title}
                                      </Typography>

                                      {/* Episode Meta */}
                                      <Stack direction="row" spacing={1} sx={{ mb: 1, flexWrap: 'wrap' }}>
                                        {episode.duration && (
                                          <Chip
                                            icon={<AccessTimeIcon />}
                                            label={`${Math.floor(episode.duration / 60)}min`}
                                            size="small"
                                            variant="outlined"
                                            sx={{ fontSize: '0.7rem' }}
                                          />
                                        )}

                                        {episode.rating && (
                                          <Chip
                                            icon={<StarIcon />}
                                            label={episode.rating.toFixed(1)}
                                            size="small"
                                            sx={{
                                              bgcolor: alpha(theme.palette.warning.main, 0.1),
                                              color: theme.palette.warning.main,
                                              fontSize: '0.7rem'
                                            }}
                                          />
                                        )}
                                      </Stack>

                                      {/* Episode Description */}
                                      {episode.description && (
                                        <Typography
                                          variant="body2"
                                          color="text.secondary"
                                          sx={{
                                            fontSize: '0.8rem',
                                            lineHeight: 1.4,
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            mb: 2
                                          }}
                                        >
                                          {episode.description}
                                        </Typography>
                                      )}

                                      {/* Watch Button */}
                                      <Button
                                        variant="contained"
                                        fullWidth
                                        startIcon={<SmartDisplayIcon />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          onEpisodePlay(episode);
                                        }}
                                        sx={{
                                          borderRadius: 2,
                                          textTransform: 'none',
                                          fontWeight: 600,
                                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                          '&:hover': {
                                            transform: 'scale(1.02)',
                                            boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.4)}`
                                          },
                                          transition: 'all 0.3s ease'
                                        }}
                                      >
                                        Assistir
                                      </Button>
                                    </CardContent>
                                  </MotionCard>
                                </Grid>
                              ))}
                            </Grid>
                          ) : (
                            <List sx={{ p: 0 }}>
                              {visibleEpisodes.map((episode, index) => (
                                <MotionBox
                                  key={episode.id}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.3, delay: Math.min(index * 0.03, 0.6) }}
                                >
                                  <ListItem
                                    sx={{
                                      bgcolor: alpha(theme.palette.background.paper, 0.8),
                                      backdropFilter: 'blur(20px)',
                                      borderRadius: 2,
                                      mb: 1,
                                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                      transition: 'all 0.3s ease',
                                      '&:hover': {
                                        bgcolor: alpha(theme.palette.background.paper, 0.9),
                                        transform: 'translateX(8px)',
                                        boxShadow: `0 4px 15px ${alpha(theme.palette.primary.main, 0.1)}`
                                      }
                                    }}
                                  >
                                    <ListItemAvatar>
                                      <Avatar
                                        variant="rounded"
                                        src={episode.thumbnail || series.thumbnail}
                                        sx={{ width: 80, height: 45, mr: 2 }}
                                      >
                                        <MovieIcon />
                                      </Avatar>
                                    </ListItemAvatar>

                                    <ListItemText
                                      primary={
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                                          <Chip
                                            label={`EP ${episode.episode}`}
                                            size="small"
                                            sx={{ fontSize: '0.7rem', fontWeight: 700 }}
                                          />
                                          <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                                            {episode.title}
                                          </Typography>
                                          {episode.watched && (
                                            <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                                          )}
                                        </Box>
                                      }
                                      secondary={
                                        <Box>
                                          <Stack direction="row" spacing={2} sx={{ mb: 1 }}>
                                            {episode.duration && (
                                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                <AccessTimeIcon sx={{ fontSize: '0.9rem' }} />
                                                <Typography variant="caption">
                                                  {Math.floor(episode.duration / 60)}min
                                                </Typography>
                                              </Box>
                                            )}

                                            {episode.rating && (
                                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                <StarIcon sx={{ fontSize: '0.9rem', color: theme.palette.warning.main }} />
                                                <Typography variant="caption">
                                                  {episode.rating.toFixed(1)}
                                                </Typography>
                                              </Box>
                                            )}
                                          </Stack>

                                          {episode.description && (
                                            <Typography
                                              variant="body2"
                                              color="text.secondary"
                                              sx={{
                                                fontSize: '0.85rem',
                                                lineHeight: 1.4,
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis'
                                              }}
                                            >
                                              {episode.description}
                                            </Typography>
                                          )}
                                        </Box>
                                      }
                                      primaryTypographyProps={{ component: 'div' }}
                                      secondaryTypographyProps={{ component: 'div' }}
                                    />

                                    <Button
                                      variant="contained"
                                      startIcon={<SmartDisplayIcon />}
                                      onClick={() => onEpisodePlay(episode)}
                                      sx={{
                                        borderRadius: 2,
                                        textTransform: 'none',
                                        fontWeight: 600,
                                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                        '&:hover': {
                                          transform: 'scale(1.05)',
                                          boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.4)}`
                                        },
                                        transition: 'all 0.3s ease'
                                      }}
                                    >
                                      Assistir
                                    </Button>
                                  </ListItem>
                                </MotionBox>
                              ))}
                            </List>
                          )}

                          {/* Paginação para muitos episódios */}
                          {hasMoreEpisodes && (
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 3, gap: 2 }}>
                              <Button
                                variant="outlined"
                                disabled={episodePage === 0}
                                onClick={() => setEpisodePage(prev => Math.max(0, prev - 1))}
                                size="small"
                              >
                                Anterior
                              </Button>

                              <Typography variant="body2" color="text.secondary">
                                Página {episodePage + 1} de {totalPages} ({episodes.length} episódios)
                              </Typography>

                              <Button
                                variant="outlined"
                                disabled={episodePage >= totalPages - 1}
                                onClick={() => setEpisodePage(prev => Math.min(totalPages - 1, prev + 1))}
                                size="small"
                              >
                                Próxima
                              </Button>
                            </Box>
                          )}

                          {episodes.length === 0 && (
                            <Box
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                py: 8,
                                color: 'text.secondary'
                              }}
                            >
                              <MovieIcon sx={{ fontSize: '4rem', mb: 2, opacity: 0.5 }} />
                              <Typography variant="h6" sx={{ mb: 1 }}>
                                Nenhum episódio disponível
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Esta temporada ainda não possui episódios cadastrados
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </MotionBox>
                    </AnimatePresence>
                  </Box>
                </MotionBox>
              </Grid>
            </Grid>
          </Container>

          {/* Menu de opções */}
          <Menu
            anchorEl={menuAnchor}
            open={Boolean(menuAnchor)}
            onClose={() => setMenuAnchor(null)}
            PaperProps={{
              sx: {
                bgcolor: alpha(theme.palette.background.paper, 0.95),
                backdropFilter: 'blur(20px)',
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }
            }}
          >
            <MenuItem onClick={() => setMenuAnchor(null)}>
              <ShareIcon sx={{ mr: 1 }} />
              Compartilhar
            </MenuItem>
            <MenuItem onClick={() => setMenuAnchor(null)}>
              <ThumbUpIcon sx={{ mr: 1 }} />
              Avaliar
            </MenuItem>
            <MenuItem onClick={() => setMenuAnchor(null)}>
              <InfoOutlinedIcon sx={{ mr: 1 }} />
              Mais informações
            </MenuItem>
          </Menu>
        </Box>
      </Fade>
    </Dialog>
  );
};

export default EnhancedSeriesModal;