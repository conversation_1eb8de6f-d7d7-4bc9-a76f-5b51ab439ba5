/**
 * Utilitário para controlar logs em produção
 * Permite desabilitar logs desnecessários que poluem o console
 */

const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Configurações de log por categoria
const LOG_CONFIG = {
  ticker: {
    enabled: false, // Desabilita logs do ticker
    level: 'error' as 'log' | 'warn' | 'error'
  },
  cors: {
    enabled: false, // Desabilita logs do CORS
    level: 'error' as 'log' | 'warn' | 'error'
  },
  cache: {
    enabled: false, // Desabilita logs de cache
    level: 'warn' as 'log' | 'warn' | 'error'
  },
  performance: {
    enabled: isDevelopment, // Apenas em desenvolvimento
    level: 'log' as 'log' | 'warn' | 'error'
  },
  errors: {
    enabled: true, // Sempre habilitado
    level: 'error' as 'log' | 'warn' | 'error'
  }
};

/**
 * Logger controlado por categoria
 */
export const logger = {
  ticker: {
    log: (...args: any[]) => {
      if (LOG_CONFIG.ticker.enabled) {
        console.log('[TICKER]', ...args);
      }
    },
    warn: (...args: any[]) => {
      if (LOG_CONFIG.ticker.enabled) {
        console.warn('[TICKER]', ...args);
      }
    },
    error: (...args: any[]) => {
      if (LOG_CONFIG.ticker.enabled || LOG_CONFIG.ticker.level === 'error') {
        console.error('[TICKER]', ...args);
      }
    }
  },
  
  cors: {
    log: (...args: any[]) => {
      if (LOG_CONFIG.cors.enabled) {
        console.log('[CORS]', ...args);
      }
    },
    warn: (...args: any[]) => {
      if (LOG_CONFIG.cors.enabled) {
        console.warn('[CORS]', ...args);
      }
    },
    error: (...args: any[]) => {
      if (LOG_CONFIG.cors.enabled || LOG_CONFIG.cors.level === 'error') {
        console.error('[CORS]', ...args);
      }
    }
  },
  
  cache: {
    log: (...args: any[]) => {
      if (LOG_CONFIG.cache.enabled) {
        console.log('[CACHE]', ...args);
      }
    },
    warn: (...args: any[]) => {
      if (LOG_CONFIG.cache.enabled) {
        console.warn('[CACHE]', ...args);
      }
    },
    error: (...args: any[]) => {
      if (LOG_CONFIG.cache.enabled || LOG_CONFIG.cache.level === 'error') {
        console.error('[CACHE]', ...args);
      }
    }
  },
  
  performance: {
    log: (...args: any[]) => {
      if (LOG_CONFIG.performance.enabled) {
        console.log('[PERF]', ...args);
      }
    },
    warn: (...args: any[]) => {
      if (LOG_CONFIG.performance.enabled) {
        console.warn('[PERF]', ...args);
      }
    },
    error: (...args: any[]) => {
      if (LOG_CONFIG.performance.enabled || LOG_CONFIG.performance.level === 'error') {
        console.error('[PERF]', ...args);
      }
    }
  },
  
  error: (...args: any[]) => {
    if (LOG_CONFIG.errors.enabled) {
      console.error('[ERROR]', ...args);
    }
  }
};

/**
 * Função para configurar logs dinamicamente
 */
export const configureLogger = (category: keyof typeof LOG_CONFIG, enabled: boolean) => {
  LOG_CONFIG[category].enabled = enabled;
};

/**
 * Função para silenciar todos os logs não críticos
 */
export const silenceNonCriticalLogs = () => {
  LOG_CONFIG.ticker.enabled = false;
  LOG_CONFIG.cors.enabled = false;
  LOG_CONFIG.cache.enabled = false;
  LOG_CONFIG.performance.enabled = false;
};

/**
 * Função para habilitar todos os logs (debug)
 */
export const enableAllLogs = () => {
  Object.keys(LOG_CONFIG).forEach(key => {
    (LOG_CONFIG as any)[key].enabled = true;
  });
};

// Auto-configuração baseada no ambiente
if (isProduction) {
  silenceNonCriticalLogs();
}

export default logger;
