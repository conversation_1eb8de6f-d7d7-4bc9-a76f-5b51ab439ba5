<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/icons/xxxa_Epf_1.ico" />
    <!-- Viewport otimizado para todos os dispositivos -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover, interactive-widget=resizes-content" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0ea5e9" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#0ea5e9" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#0a0a0a" />
    <meta name="description" content="Neko TV - Player IPTV moderno e responsivo para Celular, Computador, TV Box e Smart TV" />
    <meta name="keywords" content="IPTV, streaming, canais, televisão, entretenimento, smart tv, tv box, android tv, celular, computador" />
    <meta name="author" content="Neko TV Team" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="pt-BR" />

    <!-- Apple Meta Tags (iOS, iPadOS, Apple TV) -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Neko TV" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <link rel="apple-touch-icon" href="/icons/icon-192.png" />
    <link rel="apple-touch-icon" sizes="48x48" href="/icons/icon-48.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72.png" />
    <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192.png" />
    <link rel="apple-touch-icon" sizes="256x256" href="/icons/icon-256.png" />
    <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384.png" />
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512.png" />
    <link rel="apple-touch-startup-image" href="/icons/icon-512.png" />

    <!-- Android/Chrome Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="Neko TV" />

    <!-- Microsoft Meta Tags (Windows, Xbox) -->
    <meta name="msapplication-TileColor" content="#0ea5e9" />
    <meta name="msapplication-TileImage" content="/icons/icon-144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="msapplication-square70x70logo" content="/icons/icon-72.png" />
    <meta name="msapplication-square150x150logo" content="/icons/icon-144.png" />
    <meta name="msapplication-wide310x150logo" content="/icons/icon-256.png" />
    <meta name="msapplication-square310x310logo" content="/icons/icon-512.png" />

    <!-- Smart TV Meta Tags -->
    <meta name="tv-app-capable" content="yes" />
    <meta name="tv-app-title" content="Neko TV" />

    <!-- Android TV Meta Tags -->
    <meta name="android-tv-app-capable" content="yes" />
    <meta name="android-tv-app-title" content="Neko TV" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Neko TV - Player IPTV Universal" />
    <meta property="og:description" content="Player IPTV moderno para Celular, Computador, TV Box e Smart TV. Assista seus canais favoritos em qualquer dispositivo." />
    <meta property="og:image" content="/icons/icon-512.png" />
    <meta property="og:image:width" content="512" />
    <meta property="og:image:height" content="512" />
    <meta property="og:url" content="/" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="Neko TV" />
    <meta property="og:locale" content="pt_BR" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Neko TV - Player IPTV Universal" />
    <meta name="twitter:description" content="Player IPTV moderno para todos os dispositivos" />
    <meta name="twitter:image" content="/icons/icon-512.png" />
    <meta name="twitter:image:alt" content="Neko TV Logo" />

    <!-- Schema.org Meta Tags -->
    <meta itemprop="name" content="Neko TV" />
    <meta itemprop="description" content="Player IPTV moderno e responsivo para todos os dispositivos" />
    <meta itemprop="image" content="/icons/icon-512.png" />

    <!-- Additional Device Support -->
    <!-- Samsung Smart TV -->
    <meta name="samsung-tv-app-capable" content="yes" />
    <meta name="samsung-tv-app-title" content="Neko TV" />

    <!-- LG webOS -->
    <meta name="webos-app-capable" content="yes" />
    <meta name="webos-app-title" content="Neko TV" />

    <!-- Roku -->
    <meta name="roku-app-capable" content="yes" />
    <meta name="roku-app-title" content="Neko TV" />

    <!-- Fire TV -->
    <meta name="fire-tv-app-capable" content="yes" />
    <meta name="fire-tv-app-title" content="Neko TV" />

    <!-- Chromecast -->
    <meta name="google-cast-app-id" content="CC1AD845" />

    <!-- Performance Hints -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="format-detection" content="date=no" />
    <meta name="format-detection" content="address=no" />
    <meta name="format-detection" content="email=no" />
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main.tsx" as="script" crossorigin />
    
    <title>IPTV Player - Stream Your Content</title>
    
    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Preconnect to Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Base styles otimizados para todos os dispositivos -->
    <style>
      :root {
        color-scheme: light dark;
        /* CSS Custom Properties para diferentes dispositivos */
        --base-font-size: 16px;
        --touch-target-size: 44px;
        --border-radius: 8px;
        --spacing-unit: 1rem;
      }

      /* Otimizações para Smart TVs e telas grandes */
      @media (min-width: 1920px) {
        :root {
          --base-font-size: 20px;
          --touch-target-size: 60px;
          --border-radius: 12px;
          --spacing-unit: 1.5rem;
        }
      }

      /* Otimizações para TV Boxes e Android TV */
      @media (min-width: 1280px) and (max-width: 1919px) {
        :root {
          --base-font-size: 18px;
          --touch-target-size: 52px;
          --border-radius: 10px;
          --spacing-unit: 1.25rem;
        }
      }

      /* Otimizações para tablets */
      @media (min-width: 768px) and (max-width: 1279px) {
        :root {
          --base-font-size: 17px;
          --touch-target-size: 48px;
          --border-radius: 9px;
          --spacing-unit: 1.125rem;
        }
      }

      /* Otimizações para celulares */
      @media (max-width: 767px) {
        :root {
          --base-font-size: 16px;
          --touch-target-size: 44px;
          --border-radius: 8px;
          --spacing-unit: 1rem;
        }
      }

      * {
        box-sizing: border-box;
      }

      html {
        font-size: var(--base-font-size);
        /* Prevent zoom on iOS */
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        text-size-adjust: 100%;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #0a0a0a;
        color: #ffffff;
        line-height: 1.5;

        /* Otimizações para touch */
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        touch-action: manipulation;

        /* Otimizações para TV */
        overflow-x: hidden;
        overscroll-behavior: none;
      }

      #root {
        min-height: 100vh;
        min-height: 100dvh; /* Dynamic viewport height */
        width: 100%;
        overflow-x: hidden;
      }

      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        min-height: 100dvh;
        background-color: #0a0a0a;
        flex-direction: column;
        gap: 1rem;
      }

      .loading svg {
        width: clamp(40px, 8vw, 80px);
        height: clamp(40px, 8vw, 80px);
        animation: spin 1s linear infinite;
        color: #0ea5e9;
      }

      .loading-text {
        color: #64748b;
        font-size: clamp(14px, 3vw, 18px);
        text-align: center;
        margin-top: 1rem;
      }

      /* Animações otimizadas */
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4); }
        70% { box-shadow: 0 0 0 6px rgba(14, 165, 233, 0); }
        100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
      }

      @keyframes slideRight {
        0%, 100% { transform: translateX(-3px) rotate(45deg); }
        50% { transform: translateX(3px) rotate(45deg); }
      }

      /* Tema claro */
      @media (prefers-color-scheme: light) {
        body {
          background-color: #ffffff;
          color: #1a1a1a;
        }

        .loading {
          background-color: #ffffff;
        }

        .loading-text {
          color: #64748b;
        }
      }

      /* Otimizações para redução de movimento */
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* Otimizações para alto contraste */
      @media (prefers-contrast: high) {
        body {
          background-color: #000000;
          color: #ffffff;
        }
      }

      /* Otimizações para orientação landscape em dispositivos móveis */
      @media (max-width: 767px) and (orientation: landscape) {
        body {
          font-size: 14px;
        }

        .loading svg {
          width: 40px;
          height: 40px;
        }
      }

      /* Otimizações específicas para Smart TVs */
      @media (min-width: 1920px) {
        body {
          cursor: none; /* Hide cursor on TV */
        }

        .loading svg {
          width: 100px;
          height: 100px;
        }
      }

      /* Focus styles para navegação por controle remoto */
      :focus-visible {
        outline: 3px solid #0ea5e9;
        outline-offset: 2px;
        border-radius: var(--border-radius);
      }

      /* Scrollbar customizada */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background: rgba(14, 165, 233, 0.6);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(14, 165, 233, 0.8);
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>JavaScript Required</h1>
        <p>Este aplicativo requer JavaScript para funcionar. Por favor, habilite JavaScript no seu navegador.</p>
      </div>
    </noscript>
    <div id="root">
      <div class="loading">
        <svg viewBox="0 0 50 50" aria-label="Carregando aplicação">
          <circle
            cx="25"
            cy="25"
            r="20"
            fill="none"
            stroke="currentColor"
            stroke-width="4"
            stroke-dasharray="80"
            stroke-dashoffset="60"
          />
        </svg>
        <div class="loading-text">
          <div>Neko TV</div>
          <div style="font-size: 0.8em; opacity: 0.7; margin-top: 0.5rem;">
            Carregando player universal...
          </div>
        </div>
      </div>
    </div>
    <!-- Cursor fix script for PWA -->
    <script src="/cursor-fix.js"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
