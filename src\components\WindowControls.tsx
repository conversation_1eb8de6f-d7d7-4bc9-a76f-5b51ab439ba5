import React from 'react';
import { Box, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CropSquareIcon from '@mui/icons-material/CropSquare';
import MinimizeIcon from '@mui/icons-material/Minimize';
import { styled } from '@mui/material/styles';

const WindowControlsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  WebkitAppRegion: 'no-drag',
  marginLeft: 'auto',
}));

const WindowButton = styled(IconButton)(({ theme }) => ({
  padding: 8,
  borderRadius: 0,
  transition: 'all 0.2s',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  '&.close-button:hover': {
    backgroundColor: '#e81123',
    color: 'white',
  },
}));

interface WindowControlsProps {
  platform?: string;
}

const WindowControls: React.FC<WindowControlsProps> = ({ platform = 'win32' }) => {
  // Check if running in Electron
  const isElectron = !!(window as any).electron;
  
  if (!isElectron) return null;
  
  const handleMinimize = () => {
    (window as any).electron.minimizeWindow();
  };
  
  const handleMaximize = () => {
    (window as any).electron.maximizeWindow();
  };
  
  const handleClose = () => {
    (window as any).electron.closeWindow();
  };

  return (
    <WindowControlsContainer>
      <WindowButton
        size="small"
        onClick={handleMinimize}
        aria-label="Minimize"
      >
        <MinimizeIcon fontSize="small" />
      </WindowButton>
      <WindowButton
        size="small"
        onClick={handleMaximize}
        aria-label="Maximize"
      >
        <CropSquareIcon fontSize="small" />
      </WindowButton>
      <WindowButton
        className="close-button"
        size="small"
        onClick={handleClose}
        aria-label="Close"
      >
        <CloseIcon fontSize="small" />
      </WindowButton>
    </WindowControlsContainer>
  );
};

export default WindowControls; 