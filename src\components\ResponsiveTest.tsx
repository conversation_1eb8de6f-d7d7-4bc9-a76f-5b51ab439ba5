import React from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent, Grid } from '@mui/material';
import { useResponsive } from '../hooks/useResponsive';
import ResponsiveGrid from './ui/ResponsiveGrid';
import ResponsiveCard from './ui/ResponsiveCard';

/**
 * Componente de teste para verificar a responsividade
 * Remove este arquivo após confirmar que tudo está funcionando
 */
const ResponsiveTest: React.FC = () => {
  const { 
    isMobile, 
    isTablet, 
    isDesktop, 
    isLandscape, 
    isPortrait, 
    screenWidth, 
    screenHeight,
    breakpoints 
  } = useResponsive();

  const testCards = Array.from({ length: 12 }, (_, i) => ({
    id: i + 1,
    title: `Teste Card ${i + 1}`,
    subtitle: `Subtítulo do card ${i + 1}`,
    imageUrl: `https://picsum.photos/300/200?random=${i + 1}`,
  }));

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom className="text-responsive h1">
        Teste de Responsividade
      </Typography>
      
      {/* Device Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Informações do Dispositivo
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2">
                <strong>Tipo:</strong> {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2">
                <strong>Orientação:</strong> {isLandscape ? 'Paisagem' : 'Retrato'}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2">
                <strong>Resolução:</strong> {screenWidth}x{screenHeight}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2">
                <strong>Breakpoint:</strong> {
                  breakpoints.xs ? 'XS' :
                  breakpoints.sm ? 'SM' :
                  breakpoints.md ? 'MD' :
                  breakpoints.lg ? 'LG' : 'XL'
                }
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Responsive Buttons */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Botões Responsivos
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button variant="contained" className="btn-responsive">
            Botão Principal
          </Button>
          <Button variant="outlined" className="btn-responsive">
            Botão Secundário
          </Button>
          <Button variant="text" className="btn-responsive">
            Botão Texto
          </Button>
        </Box>
      </Box>

      {/* Responsive Typography */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Tipografia Responsiva
        </Typography>
        <Typography className="text-responsive h1" gutterBottom>
          Título H1 Responsivo
        </Typography>
        <Typography className="text-responsive h2" gutterBottom>
          Título H2 Responsivo
        </Typography>
        <Typography className="text-responsive body" gutterBottom>
          Texto do corpo responsivo. Este texto se adapta ao tamanho da tela para melhor legibilidade.
        </Typography>
        <Typography className="text-responsive caption">
          Texto de legenda responsivo para informações secundárias.
        </Typography>
      </Box>

      {/* Responsive Grid with Auto-sizing */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Grid Responsivo (Auto-sizing)
        </Typography>
        <ResponsiveGrid minItemWidth={200} spacing={2} aspectRatio="16:9">
          {testCards.slice(0, 8).map((card) => (
            <ResponsiveCard
              key={card.id}
              title={card.title}
              subtitle={card.subtitle}
              imageUrl={card.imageUrl}
              variant="movie"
              onClick={() => console.log(`Clicked card ${card.id}`)}
              onFavoriteClick={() => console.log(`Favorited card ${card.id}`)}
              onInfoClick={() => console.log(`Info card ${card.id}`)}
            />
          ))}
        </ResponsiveGrid>
      </Box>

      {/* Responsive Grid with Fixed Columns */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Grid Responsivo (Colunas Fixas)
        </Typography>
        <ResponsiveGrid 
          maxColumns={{ xs: 2, sm: 3, md: 4, lg: 5 }}
          spacing={2}
        >
          {testCards.slice(0, 10).map((card) => (
            <ResponsiveCard
              key={card.id}
              title={card.title}
              subtitle={card.subtitle}
              imageUrl={card.imageUrl}
              variant="channel"
              isLive={card.id % 3 === 0}
              onClick={() => console.log(`Clicked card ${card.id}`)}
            />
          ))}
        </ResponsiveGrid>
      </Box>

      {/* Visibility Classes */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Classes de Visibilidade
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Typography className="show-mobile-only" color="primary">
            ✅ Visível apenas no mobile
          </Typography>
          <Typography className="show-tablet-only" color="secondary">
            ✅ Visível apenas no tablet
          </Typography>
          <Typography className="show-desktop-only" color="success.main">
            ✅ Visível apenas no desktop
          </Typography>
          <Typography className="hide-mobile" color="warning.main">
            ❌ Oculto no mobile
          </Typography>
          <Typography className="hide-tablet" color="error.main">
            ❌ Oculto no tablet
          </Typography>
          <Typography className="hide-desktop" color="info.main">
            ❌ Oculto no desktop
          </Typography>
        </Box>
      </Box>

      {/* CSS Grid Classes */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Grid CSS Responsivo
        </Typography>
        <Box className="grid-responsive grid-4">
          {Array.from({ length: 8 }, (_, i) => (
            <Card key={i} className="card-responsive">
              <CardContent>
                <Typography variant="h6">
                  Item {i + 1}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Grid item responsivo
                </Typography>
              </CardContent>
            </Card>
          ))}
        </Box>
      </Box>

      {/* Spacing Test */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Sistema de Espaçamento
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box className="spacing-responsive xs" sx={{ bgcolor: 'primary.light', p: 1 }}>
            Espaçamento XS
          </Box>
          <Box className="spacing-responsive sm" sx={{ bgcolor: 'secondary.light', p: 1 }}>
            Espaçamento SM
          </Box>
          <Box className="spacing-responsive md" sx={{ bgcolor: 'success.light', p: 1 }}>
            Espaçamento MD
          </Box>
          <Box className="spacing-responsive lg" sx={{ bgcolor: 'warning.light', p: 1 }}>
            Espaçamento LG
          </Box>
          <Box className="spacing-responsive xl" sx={{ bgcolor: 'error.light', p: 1 }}>
            Espaçamento XL
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ResponsiveTest;
