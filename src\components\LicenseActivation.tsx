import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Paper, 
  Alert, 
  IconButton, 
  Chip,
  useTheme,
  useMediaQuery,
  Snackbar,
  Divider
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SmartphoneIcon from '@mui/icons-material/Smartphone';
import DeviceService from '../services/deviceService';

interface LicenseActivationProps {
  onActivated: (iptvUrl: string) => void;
}

const LicenseActivation: React.FC<LicenseActivationProps> = ({ onActivated }) => {
  const [licenseKey, setLicenseKey] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isActivated, setIsActivated] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Verificar se já existe uma licença válida ao carregar o componente
  useEffect(() => {
    const checkExistingLicense = async () => {
      setIsLoading(true);
      try {
        const deviceId = DeviceService.getDeviceId();
        setDeviceId(DeviceService.formatDeviceId(deviceId));

        const hasValidLicense = await DeviceService.hasValidLicense();
        if (hasValidLicense) {
          setIsActivated(true);
          const license = await DeviceService.loadLicense();
          if (license?.iptvUrl) {
            setMessage('Licença ativa! Redirecionando...');
            setTimeout(() => {
              onActivated(license.iptvUrl || '');
            }, 1500);
          } else {
            setMessage('Licença ativa, mas sem URL IPTV configurada.');
            setIsError(true);
          }
        }
      } catch (error) {
        console.error('Erro ao verificar licença existente:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingLicense();
  }, [onActivated]);

  // Função para copiar o ID do dispositivo
  const copyDeviceId = async () => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(deviceId);
      } else {
        // Fallback para navegadores mais antigos ou contextos não-seguros
        const textArea = document.createElement('textarea');
        textArea.value = deviceId;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }
      
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 3000);
    } catch (error) {
      console.error('Erro ao copiar:', error);
      // Mostrar fallback manual
      alert(`Código do dispositivo: ${deviceId}\n\nCopie manualmente este código.`);
    }
  };

  // Função para ativar a licença
  const activateLicense = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!licenseKey.trim()) {
      setMessage('Por favor, insira uma chave de licença válida.');
      setIsError(true);
      return;
    }

    setIsLoading(true);
    setIsError(false);
    setMessage('Verificando licença...');

    try {
      const licenseData = await DeviceService.checkLicense(licenseKey.trim());
      
      if (licenseData.valid) {
        // Licença válida
        DeviceService.saveLicenseLocally(licenseData);
        setIsActivated(true);
        setMessage(licenseData.message || 'Licença ativada com sucesso!');
        
        // Redirecionar para o player IPTV se houver uma URL
        if (licenseData.iptvUrl) {
          setTimeout(() => {
            onActivated(licenseData.iptvUrl || '');
          }, 1500);
        } else {
          setMessage('Licença ativa, mas sem URL IPTV configurada.');
          setIsError(true);
        }
      } else {
        // Licença inválida
        setIsActivated(false);
        setIsError(true);
        setMessage(licenseData.message || 'Chave de licença inválida.');
      }
    } catch (error) {
      console.error('Erro ao ativar licença:', error);
      setIsError(true);
      setMessage('Ocorreu um erro ao ativar a licença. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1e3a8a, #3b82f6)',
        padding: isMobile ? 2 : 3,
      }}
    >
      <Paper
        elevation={10}
        sx={{
          width: '100%',
          maxWidth: isMobile ? '100%' : 500,
          padding: isMobile ? 3 : 4,
          borderRadius: 3,
          textAlign: 'center',
        }}
      >
        <Typography 
          variant={isMobile ? 'h4' : 'h3'} 
          component="h1" 
          sx={{ 
            color: 'primary.main', 
            fontWeight: 'bold',
            mb: 3 
          }}
        >
          IPTV Player
        </Typography>

        {isActivated ? (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <CheckCircleIcon 
              sx={{ 
                fontSize: 80, 
                color: 'success.main', 
                mb: 2 
              }} 
            />
            <Typography variant="h6" color="success.main">
              {message}
            </Typography>
          </Box>
        ) : (
          <>
            {/* Seção do ID do Dispositivo - Otimizada para Mobile */}
            <Paper
              elevation={2}
              sx={{
                bgcolor: 'grey.50',
                border: `2px dashed ${theme.palette.primary.main}`,
                borderRadius: 2,
                p: isMobile ? 2.5 : 2,
                mb: 3,
                position: 'relative'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                <SmartphoneIcon sx={{ color: 'primary.main', mr: 1 }} />
                <Typography variant="h6" color="primary.main" fontWeight="bold">
                  Código do Dispositivo
                </Typography>
              </Box>
              
              {/* Código do dispositivo em formato grande e legível */}
              <Box
                sx={{
                  bgcolor: 'white',
                  borderRadius: 2,
                  p: isMobile ? 2 : 1.5,
                  mb: 2,
                  border: '2px solid',
                  borderColor: 'primary.light',
                  position: 'relative'
                }}
              >
                <Typography 
                  variant={isMobile ? 'h5' : 'h6'}
                  component="div"
                  sx={{ 
                    fontFamily: 'monospace',
                    fontWeight: 'bold',
                    color: 'primary.dark',
                    letterSpacing: isMobile ? 2 : 1,
                    wordBreak: 'break-all',
                    lineHeight: 1.4
                  }}
                >
                  {deviceId}
                </Typography>
              </Box>

              {/* Botão de copiar otimizado para mobile */}
              <Button
                variant="contained"
                startIcon={<ContentCopyIcon />}
                onClick={copyDeviceId}
                size={isMobile ? 'large' : 'medium'}
                sx={{
                  width: isMobile ? '100%' : 'auto',
                  minWidth: isMobile ? 'auto' : 150,
                  fontSize: isMobile ? '1.1rem' : '1rem',
                  py: isMobile ? 1.5 : 1,
                  fontWeight: 'bold',
                  borderRadius: 2,
                  textTransform: 'none'
                }}
              >
                {isMobile ? 'Copiar Código' : 'Copiar'}
              </Button>
              
              <Typography 
                variant="caption" 
                color="text.secondary"
                sx={{ 
                  display: 'block', 
                  mt: 2,
                  fontSize: isMobile ? '0.9rem' : '0.75rem'
                }}
              >
                Envie este código para o administrador para ativar sua licença
              </Typography>
            </Paper>

            <Divider sx={{ mb: 3 }}>
              <Chip label="OU" color="primary" variant="outlined" />
            </Divider>

            {/* Formulário de ativação */}
            <Box component="form" onSubmit={activateLicense} sx={{ textAlign: 'left' }}>
              <TextField
                fullWidth
                label="Chave de Licença"
                placeholder="XXXX-XXXX-XXXX-XXXX"
                value={licenseKey}
                onChange={(e) => setLicenseKey(e.target.value)}
                disabled={isLoading}
                variant="outlined"
                size={isMobile ? 'medium' : 'medium'}
                sx={{ 
                  mb: 3,
                  '& .MuiInputBase-root': {
                    fontSize: isMobile ? '1.1rem' : '1rem'
                  }
                }}
              />

              {message && (
                <Alert 
                  severity={isError ? 'error' : 'info'} 
                  sx={{ mb: 3, textAlign: 'left' }}
                >
                  {message}
                </Alert>
              )}

              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={isLoading || !licenseKey.trim()}
                size={isMobile ? 'large' : 'medium'}
                sx={{
                  py: isMobile ? 2 : 1.5,
                  fontSize: isMobile ? '1.1rem' : '1rem',
                  fontWeight: 'bold',
                  textTransform: 'none',
                  borderRadius: 2
                }}
              >
                {isLoading ? 'Verificando...' : 'Ativar Licença'}
              </Button>
            </Box>

            <Typography 
              variant="body2" 
              color="text.secondary" 
              sx={{ 
                mt: 3,
                fontSize: isMobile ? '0.9rem' : '0.875rem'
              }}
            >
              Não tem uma licença? Entre em contato com o suporte.
            </Typography>
          </>
        )}

        {/* Snackbar para feedback de cópia */}
        <Snackbar
          open={copySuccess}
          autoHideDuration={3000}
          onClose={() => setCopySuccess(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert 
            onClose={() => setCopySuccess(false)} 
            severity="success" 
            variant="filled"
            sx={{ width: '100%' }}
          >
            Código copiado com sucesso!
          </Alert>
        </Snackbar>
      </Paper>
    </Box>
  );
};

export default LicenseActivation;