// Script de teste para o sistema de atualizações IPTV
// Execute no console do navegador

console.log('🧪 Testando sistema de atualizações IPTV...');

// Teste 1: Verificar se as funções de debug estão disponíveis
function testDebugFunctions() {
  console.log('\n🔧 Testando funções de debug...');
  
  if (typeof window.debugIptvUpdates === 'object') {
    console.log('✅ debugIptvUpdates disponível');
    
    const functions = ['checkUpdates', 'forceCheck', 'getSnapshot', 'clearCache', 'simulateUpdate'];
    functions.forEach(fn => {
      if (typeof window.debugIptvUpdates[fn] === 'function') {
        console.log(`✅ ${fn}() disponível`);
      } else {
        console.log(`❌ ${fn}() não encontrada`);
      }
    });
    
    return true;
  } else {
    console.log('❌ debugIptvUpdates não disponível');
    return false;
  }
}

// Teste 2: Verificar snapshot atual
async function testSnapshot() {
  console.log('\n📸 Testando snapshot...');
  
  try {
    const snapshot = debugIptvUpdates.getSnapshot();
    if (snapshot) {
      console.log('✅ Snapshot encontrado:', {
        timestamp: new Date(snapshot.timestamp).toLocaleString(),
        channels: snapshot.channels,
        movies: snapshot.movies,
        series: snapshot.series,
        categories: snapshot.categories,
        hash: snapshot.hash
      });
      return true;
    } else {
      console.log('⚠️ Nenhum snapshot encontrado (primeira execução?)');
      return false;
    }
  } catch (error) {
    console.error('❌ Erro ao obter snapshot:', error);
    return false;
  }
}

// Teste 3: Simular atualização
async function testSimulateUpdate() {
  console.log('\n🎭 Testando simulação de atualização...');
  
  try {
    debugIptvUpdates.simulateUpdate();
    console.log('✅ Simulação criada');
    
    // Aguarda um pouco e verifica atualizações
    setTimeout(async () => {
      try {
        const result = await debugIptvUpdates.checkUpdates();
        if (result.hasUpdates) {
          console.log('✅ Atualizações detectadas:', result.updates);
        } else {
          console.log('⚠️ Nenhuma atualização detectada');
        }
      } catch (error) {
        console.error('❌ Erro na verificação:', error);
      }
    }, 1000);
    
    return true;
  } catch (error) {
    console.error('❌ Erro na simulação:', error);
    return false;
  }
}

// Teste 4: Verificar modal de atualização
function testUpdateModal() {
  console.log('\n📱 Testando modal de atualização...');
  
  // Procura pelo modal no DOM
  const modal = document.querySelector('[role="dialog"]');
  if (modal) {
    console.log('✅ Modal encontrado no DOM');
    
    // Verifica se tem conteúdo de atualização
    const updateContent = modal.textContent;
    if (updateContent && updateContent.includes('Atualizações')) {
      console.log('✅ Modal contém conteúdo de atualização');
      return true;
    } else {
      console.log('⚠️ Modal não contém conteúdo de atualização');
      return false;
    }
  } else {
    console.log('⚠️ Modal não encontrado (normal se não há atualizações)');
    return false;
  }
}

// Teste 5: Verificar localStorage
function testLocalStorage() {
  console.log('\n💾 Testando localStorage...');
  
  try {
    const snapshot = localStorage.getItem('neko_iptv_snapshot');
    if (snapshot) {
      const parsed = JSON.parse(snapshot);
      console.log('✅ Snapshot no localStorage:', {
        timestamp: new Date(parsed.timestamp).toLocaleString(),
        hash: parsed.hash
      });
      return true;
    } else {
      console.log('⚠️ Nenhum snapshot no localStorage');
      return false;
    }
  } catch (error) {
    console.error('❌ Erro ao verificar localStorage:', error);
    return false;
  }
}

// Executar todos os testes
async function runAllTests() {
  console.log('🚀 Iniciando bateria de testes do sistema de atualizações IPTV...\n');
  
  const tests = [
    { name: 'Funções de Debug', fn: testDebugFunctions },
    { name: 'Snapshot Atual', fn: testSnapshot },
    { name: 'LocalStorage', fn: testLocalStorage },
    { name: 'Modal de Atualização', fn: testUpdateModal },
    { name: 'Simulação de Atualização', fn: testSimulateUpdate }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`🧪 Executando: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.error(`❌ Erro no teste ${test.name}:`, error);
      results.push({ name: test.name, passed: false, error });
    }
  }
  
  // Resumo
  console.log('\n📊 Resumo dos Testes:');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
  });
  
  console.log('='.repeat(50));
  console.log(`📈 Resultado: ${passed}/${total} testes passaram (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('🎉 Todos os testes passaram! Sistema funcionando perfeitamente.');
  } else {
    console.log('⚠️ Alguns testes falharam. Sistema pode estar em inicialização.');
  }
  
  return { passed, total, results };
}

// Executar automaticamente
runAllTests();

// Exportar para uso manual
window.testIptvUpdates = {
  runAllTests,
  testDebugFunctions,
  testSnapshot,
  testSimulateUpdate,
  testUpdateModal,
  testLocalStorage
};
