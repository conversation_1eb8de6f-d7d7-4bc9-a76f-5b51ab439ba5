import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Box, Typography, Link, useTheme, Fade } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import LaunchIcon from '@mui/icons-material/Launch';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import { handleExternalLinkClick } from '../utils/openExternal';
import type { TickerConfig, MessageConfig } from './MessageTicker';

// Animação otimizada para performance máxima
const scrollAnimation = keyframes`
  0% { transform: translate3d(100%, 0, 0); }
  100% { transform: translate3d(-100%, 0, 0); }
`;

// Container otimizado para inicialização rápida
const FastTickerContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '60px',
  zIndex: 10,
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.85), rgba(30, 41, 59, 0.85))'
    : 'linear-gradient(135deg, rgba(248, 250, 252, 0.85), rgba(241, 245, 249, 0.85))',
  backdropFilter: 'blur(8px)', // Reduzido para melhor performance
  borderTop: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
  borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  contain: 'layout style paint',
  willChange: 'auto',
  
  [theme.breakpoints.down('sm')]: {
    height: '56px',
  },
}));

// Conteúdo scrollável ultra-otimizado
const FastScrollingContent = styled(Box)<{ duration: number }>(({ duration }) => ({
  display: 'flex',
  alignItems: 'center',
  whiteSpace: 'nowrap',
  animation: `${scrollAnimation} ${duration}s linear infinite`,
  paddingLeft: '100%',
  willChange: 'transform',
  backfaceVisibility: 'hidden',
  
  '&:hover': {
    animationPlayState: 'paused',
  },
}));

// Link super otimizado para clicabilidade máxima
const FastStyledLink = styled(Link)(({ theme }) => ({
  zIndex: 1000,
  position: 'relative',
  color: '#ffffff',
  textDecoration: 'none !important',
  fontWeight: 700,
  padding: theme.spacing(0.8, 1.8),
  borderRadius: 8,
  background: 'rgba(59, 130, 246, 0.9)',
  border: '2px solid rgba(255, 255, 255, 0.4)',
  transition: 'all 0.2s ease',
  display: 'inline-flex !important',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  fontSize: '0.95rem',
  cursor: 'pointer !important',
  pointerEvents: 'auto !important',
  userSelect: 'none',
  minHeight: '32px',
  minWidth: '80px',
  justifyContent: 'center',

  '&:hover, &:focus': {
    background: 'rgba(59, 130, 246, 1)',
    transform: 'translateY(-2px) scale(1.05)',
    textDecoration: 'none !important',
  },
}));

interface FastMessageTickerProps {
  className?: string;
}

/**
 * Versão ultra-rápida do MessageTicker que inicializa instantaneamente
 * com configuração local e atualiza em background
 */
const FastMessageTicker: React.FC<FastMessageTickerProps> = ({ className }) => {
  const theme = useTheme();
  const [config, setConfig] = useState<TickerConfig | null>(null);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  // Configuração padrão para inicialização instantânea
  const defaultConfig: TickerConfig = useMemo(() => ({
    enabled: true,
    messages: [
      {
        id: 'welcome',
        text: '🎉 Bem-vindo ao Neko TV! ',
        link: {
          text: 'Site oficial',
          url: 'https://nekotv.vercel.app',
          target: '_blank'
        },
        suffix: ' - A melhor experiência de streaming!',
        duration: 15,
        priority: 1,
        enabled: true
      },
      {
        id: 'quality',
        text: '📺 Assista em 4K, Full HD e HD com a melhor qualidade!',
        duration: 12,
        priority: 2,
        enabled: true
      }
    ],
    defaultDuration: 15
  }), []);

  // Inicialização que SEMPRE tenta buscar do Pastebin
  useEffect(() => {
    // Usa configuração padrão imediatamente para não deixar vazio
    setConfig(defaultConfig);

    // Tenta buscar configuração do Pastebin SEMPRE
    const loadRemoteConfig = async () => {
      try {
        // Primeiro tenta buscar do serviço (que vai tentar Pastebin)
        const { messageTickerService } = await import('../services/messageTickerService');
        const remoteConfig = await messageTickerService.getConfig(true); // Force refresh
        setConfig(remoteConfig);
      } catch (error) {
        // Se falhar, tenta configuração local como fallback
        try {
          const response = await fetch('/ticker-config.json', { cache: 'no-cache' });
          if (response.ok) {
            const localConfig = await response.json();
            setConfig(localConfig);
          }
        } catch (localError) {
          // Mantém configuração padrão
        }
      }
    };

    // Executa imediatamente para buscar do Pastebin
    loadRemoteConfig();
  }, [defaultConfig]);

  // Rotação de mensagens otimizada
  const enabledMessages = useMemo(() => 
    config?.messages.filter(msg => msg.enabled !== false) || [], 
    [config?.messages]
  );

  useEffect(() => {
    if (enabledMessages.length > 1) {
      const currentMessage = enabledMessages[currentMessageIndex];
      const duration = (currentMessage?.duration || config?.defaultDuration || 15) * 1000;
      
      const timeoutId = setTimeout(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % enabledMessages.length);
      }, duration);

      return () => clearTimeout(timeoutId);
    }
  }, [currentMessageIndex, enabledMessages, config?.defaultDuration]);

  // Handler de clique otimizado
  const handleLinkClick = useCallback((url: string, target: string = '_blank') => {
    try {
      handleExternalLinkClick(url, target);
    } catch (error) {
      window.open(url, target, 'noopener,noreferrer');
    }
  }, []);

  // Early return se não habilitado
  if (!config?.enabled || !isVisible || enabledMessages.length === 0) {
    return null;
  }

  const currentMessage = enabledMessages[currentMessageIndex];
  const scrollDuration = currentMessage?.duration || config.defaultDuration || 15;
  const cleanUrl = currentMessage.link?.url.replace(/[`\s]/g, '') || '';

  return (
    <Fade in={isVisible} timeout={200}>
      <FastTickerContainer className={className}>
        <FastScrollingContent duration={scrollDuration}>
          <NotificationsActiveIcon 
            sx={{ 
              color: theme.palette.mode === 'dark' ? '#ffffff' : '#1f2937',
              marginRight: 1.5,
              fontSize: '1.4rem'
            }} 
          />
          <Typography
            variant="body1"
            component="span"
            sx={{
              color: theme.palette.mode === 'dark' ? '#ffffff' : '#1f2937',
              fontWeight: 700,
              fontSize: { xs: '1rem', sm: '1.1rem' },
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
            }}
          >
            {currentMessage.text}
            {currentMessage.link && (
              <FastStyledLink
                href={cleanUrl}
                target={currentMessage.link.target || '_blank'}
                rel="noopener noreferrer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleLinkClick(cleanUrl, currentMessage.link!.target);
                }}
                sx={{ mx: 1 }}
              >
                {currentMessage.link.text}
                <LaunchIcon sx={{ fontSize: 14, ml: 0.5 }} />
              </FastStyledLink>
            )}
            {currentMessage.suffix}
          </Typography>
        </FastScrollingContent>
      </FastTickerContainer>
    </Fade>
  );
};

export default FastMessageTicker;
