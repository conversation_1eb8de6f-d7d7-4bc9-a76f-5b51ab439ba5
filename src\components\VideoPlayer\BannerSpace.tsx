import React, { useState, useEffect } from 'react';
import { Box, Paper, Typography, Button, Card, CardContent, CardMedia, IconButton, Divider, Chip, Grid, Tooltip } from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
import InfoIcon from '@mui/icons-material/Info';
import WebIcon from '@mui/icons-material/Web';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { handleExternalLinkClick } from '../../utils/openExternal';

interface BannerItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  link?: string;
  type: 'feature' | 'ad' | 'announcement';
}

interface BannerSpaceProps {
  channelName?: string;
  children: React.ReactNode;
}

// Amostra de dados para banners
const sampleBanners: BannerItem[] = [
  {
    id: '1',
    title: 'Nova versão disponível',
    description: '<PERSON>eja as novidades da versão 2.0 do IPTV Player com suporte a múltiplos formatos.',
    imageUrl: 'https://via.placeholder.com/600x150/2196F3/FFFFFF?text=Nova+Versão+do+IPTV+Player',
    type: 'announcement'
  },
  {
    id: '2',
    title: 'Guia de configuração',
    description: 'Aprenda a configurar seu IPTV Player para obter a melhor experiência.',
    imageUrl: 'https://via.placeholder.com/600x150/4CAF50/FFFFFF?text=Configuração+do+IPTV+Player',
    link: '#',
    type: 'feature'
  },
  {
    id: '3',
    title: 'Recursos Pro',
    description: 'Conheça os recursos exclusivos da versão PRO do IPTV Player.',
    imageUrl: 'https://via.placeholder.com/600x150/FFC107/000000?text=Recursos+PRO+IPTV+Player',
    link: '#',
    type: 'ad'
  }
];

// Estilização para animações
const AnimatedBanner = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.shadows[6],
  },
}));

const BannerSpace: React.FC<BannerSpaceProps> = ({ channelName, children }) => {
  const theme = useTheme();
  const [banners, setBanners] = useState<BannerItem[]>(sampleBanners);
  const [activeBanner, setActiveBanner] = useState<number>(0);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Efeito para alternar automaticamente os banners
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveBanner((current) => (current + 1) % banners.length);
    }, 10000); // Alterna a cada 10 segundos

    return () => clearInterval(interval);
  }, [banners.length]);

  // Função para abrir o link do banner
  const handleBannerClick = (banner: BannerItem) => {
    if (banner.link) {
      handleExternalLinkClick(banner.link);
    }
  };

  // Renderiza um tipo de chip baseado no tipo do banner
  const renderBannerTypeChip = (type: BannerItem['type']) => {
    switch (type) {
      case 'feature':
        return (
          <Chip 
            icon={<InfoIcon />} 
            label="Recurso" 
            size="small" 
            color="primary" 
            sx={{ position: 'absolute', top: 8, right: 8 }}
          />
        );
      case 'ad':
        return (
          <Chip 
            icon={<WebIcon />} 
            label="Promoção" 
            size="small" 
            color="secondary" 
            sx={{ position: 'absolute', top: 8, right: 8 }}
          />
        );
      case 'announcement':
        return (
          <Chip 
            icon={<NewReleasesIcon />} 
            label="Novidade" 
            size="small" 
            color="success" 
            sx={{ position: 'absolute', top: 8, right: 8 }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box
      component="div"
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        backgroundColor: '#000',
        transition: 'all 0.3s ease',
        height: isCollapsed ? '56px' : '25vh',
        minHeight: isCollapsed ? '56px' : '150px',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
      }}
    >
      <Box 
        component="div" 
        sx={{ 
          flex: 1, 
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        {children}
      </Box>
      
      <Box
        component="div"
        sx={{
          position: 'absolute',
          bottom: -16,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1001,
        }}
      >
        <Tooltip title={isCollapsed ? "Expandir player" : "Minimizar player"}>
          <IconButton
            onClick={() => setIsCollapsed(!isCollapsed)}
            sx={{
              backgroundColor: 'primary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
              width: 28,
              height: 28,
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            }}
          >
            {isCollapsed ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />}
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default BannerSpace; 