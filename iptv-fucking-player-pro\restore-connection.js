// Script para restaurar a conexão IPTV
const connection = {
  id: 'dsadsa',
  name: 'dsad<PERSON>',
  url: 'https://pandatvuhd.cloud',
  username: '1Mtt2U',
  password: 'SUZ3aJ',
  isActive: true,
  createdAt: Date.now()
};

// Salvar no localStorage
localStorage.setItem('iptv_connections', JSON.stringify([connection]));
localStorage.setItem('active_connection_id', 'dsadsa');

console.log('✅ Conexão restaurada:', connection);
console.log('🔄 Recarregue a página para aplicar as mudanças');