import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, Card, CardMedia, CardContent, Grid, CircularProgress, Skeleton, useTheme, LinearProgress } from '@mui/material';
import { dbService } from '../../services/dbService';
import { useNavigate } from 'react-router-dom';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { Stream } from '../../services/iptvService';

interface ContinueWatchingProps {
  onMovieSelect?: (movie: Stream) => void;
  onMoviePlay?: (movie: Stream, startTime: number) => void;
  maxItems?: number;
}

interface WatchedMovie {
  id: string;
  name: string;
  thumbnail: string;
  cover?: string;
  progress: number;
  duration: number;
  lastWatched: number;
  categoryId?: string;
}

const ContinueWatching: React.FC<ContinueWatchingProps> = ({ onMovieSelect, onMoviePlay, maxItems = 6 }) => {
  const [watchedMovies, setWatchedMovies] = useState<WatchedMovie[]>([]);
  const [loading, setLoading] = useState(true);
  const theme = useTheme();
  const navigate = useNavigate();
  const watchedMoviesRef = useRef<WatchedMovie[]>([]);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const loadWatchedMovies = async () => {
    try {
      const movies = await dbService.getRecentlyWatchedMovies(maxItems);
      
      if (JSON.stringify(movies) !== JSON.stringify(watchedMoviesRef.current)) {
        setWatchedMovies(movies);
        watchedMoviesRef.current = movies;
        console.log('📺 Continue Watching: Updated with new data');
      }
    } catch (error) {
      console.error('Error loading watched movies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const initialLoad = async () => {
      setLoading(true);
      await loadWatchedMovies();
    };
    
    initialLoad();
  }, [maxItems]);

  useEffect(() => {
    const startRefreshInterval = () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      
      refreshIntervalRef.current = setInterval(() => {
        loadWatchedMovies();
      }, 30000);
    };
    
    const stopRefreshInterval = () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
    
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopRefreshInterval();
      } else {
        loadWatchedMovies();
        startRefreshInterval();
      }
    };
    
    loadWatchedMovies();
    startRefreshInterval();
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      stopRefreshInterval();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [maxItems]);

  const handleMovieClick = (movie: WatchedMovie) => {
    // Converter WatchedMovie para Stream
    const streamMovie: Stream = {
      id: movie.id,
      name: movie.name,
      thumbnail: movie.thumbnail,
      cover: movie.cover
    };
    
    if (onMoviePlay) {
      // Se temos um handler de reprodução direta, usamos ele com o tempo salvo
      onMoviePlay(streamMovie, movie.progress);
    } else if (onMovieSelect) {
      // Caso contrário, usamos o handler de seleção
      onMovieSelect(streamMovie);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimeLeft = (current: number, total: number) => {
    const remaining = total - current;
    if (remaining <= 0) return "Concluído";
    
    const minutes = Math.floor(remaining / 60);
    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m restantes`;
    }
    return `${minutes}m restantes`;
  };

  if (loading) {
    return (
      <Box sx={{ mt: 3 }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          Continue Assistindo
        </Typography>
        <Grid container spacing={2}>
          {[...Array(4)].map((_, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={index}>
              <Card sx={{ position: 'relative', borderRadius: 2, overflow: 'hidden', bgcolor: 'rgba(255,255,255,0.04)' }}>
                <Skeleton variant="rectangular" height={160} animation="wave" sx={{ bgcolor: 'rgba(255,255,255,0.08)' }} />
                <CardContent sx={{ height: 80 }}>
                  <Skeleton animation="wave" height={24} width="80%" sx={{ mb: 1, bgcolor: 'rgba(255,255,255,0.08)' }} />
                  <Skeleton animation="wave" height={16} width="60%" sx={{ bgcolor: 'rgba(255,255,255,0.08)' }} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (watchedMovies.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
        Continue Assistindo
      </Typography>
      <Grid container spacing={2}>
        {watchedMovies.map((movie) => {
          const progressPercent = (movie.progress / movie.duration) * 100;
          
          return (
            <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={movie.id}>
              <Card 
                sx={{ 
                  position: 'relative', 
                  borderRadius: 2, 
                  overflow: 'hidden',
                  transition: 'transform 0.2s',
                  bgcolor: 'rgba(18, 18, 18, 0.7)',
                  '&:hover': {
                    transform: 'scale(1.05)',
                    '& .MuiBox-root.play-button': {
                      opacity: 1
                    }
                  },
                  cursor: 'pointer'
                }}
                onClick={() => handleMovieClick(movie)}
              >
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height={160}
                    image={movie.cover || movie.thumbnail}
                    alt={movie.name}
                    sx={{ objectFit: 'cover' }}
                  />
                  <LinearProgress 
                    variant="determinate" 
                    value={progressPercent} 
                    sx={{ 
                      position: 'absolute', 
                      bottom: 0, 
                      left: 0, 
                      right: 0, 
                      height: 4,
                      borderRadius: 0,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: theme.palette.primary.main
                      }
                    }} 
                  />
                  <Box 
                    className="play-button"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(0,0,0,0.4)',
                      opacity: 0,
                      transition: 'opacity 0.2s'
                    }}
                  >
                    <Box 
                      sx={{ 
                        width: 50, 
                        height: 50, 
                        borderRadius: '50%', 
                        bgcolor: 'rgba(14, 165, 233, 0.85)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <PlayArrowIcon sx={{ color: 'white', fontSize: 30 }} />
                    </Box>
                  </Box>
                </Box>
                <CardContent sx={{ py: 1.5 }}>
                  <Typography variant="body1" noWrap sx={{ fontWeight: 500 }}>
                    {movie.name}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 0.5 }}>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {formatTime(movie.progress)} / {formatTime(movie.duration)}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {formatTimeLeft(movie.progress, movie.duration)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default ContinueWatching; 