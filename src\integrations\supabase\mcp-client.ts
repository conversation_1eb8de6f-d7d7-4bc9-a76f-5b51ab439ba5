import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Configuração MCP do Supabase com permissões completas
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://kzvhuqfkqoncgsyezaaq.supabase.co";
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt6dmh1cWZrcW9uY2dzeWV6YWFxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTI3NjcsImV4cCI6MjA2MjM2ODc2N30.L8agsQM1GDdkVt-ROWFfuHMsk7zGzjHzkW-JJQChXgs";
const SUPABASE_SERVICE_TOKEN = import.meta.env.VITE_SERVICE_TOKEN || "********************************************";

// Cliente MCP com permissões de administrador
export const supabaseMCP = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_TOKEN, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  },
  global: {
    headers: {
      'Content-Type': 'application/json',
      'Prefer': 'return=representation',
      'Authorization': `Bearer ${SUPABASE_SERVICE_TOKEN}`
    }
  }
});

// Cliente padrão para operações normais
export const supabaseClient = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    }
  }
});

// Interface para operações MCP
export interface MCPSupabaseOperations {
  // Operações de licenças com permissões completas
  createLicense: (data: any) => Promise<any>;
  updateLicense: (id: string, data: any) => Promise<any>;
  deleteLicense: (id: string) => Promise<boolean>;
  getAllLicenses: () => Promise<any[]>;
  
  // Operações de usuários
  createUser: (email: string, password: string) => Promise<any>;
  deleteUser: (userId: string) => Promise<boolean>;
  
  // Operações de banco de dados
  executeSQL: (query: string) => Promise<any>;
  
  // Operações de storage
  uploadFile: (bucket: string, path: string, file: File) => Promise<any>;
  deleteFile: (bucket: string, path: string) => Promise<boolean>;
  
  // Operações de configuração
  updateRLS: (table: string, policy: string) => Promise<any>;
  createFunction: (name: string, definition: string) => Promise<any>;
}

// Implementação das operações MCP
export const mcpOperations: MCPSupabaseOperations = {
  // Criar licença com permissões completas
  async createLicense(data: any) {
    try {
      const { data: result, error } = await supabaseMCP
        .from('licenses')
        .insert(data)
        .select()
        .single();
      
      if (error) throw error;
      return result;
    } catch (error) {
      console.error('MCP: Erro ao criar licença:', error);
      throw error;
    }
  },

  // Atualizar licença
  async updateLicense(id: string, data: any) {
    try {
      const { data: result, error } = await supabaseMCP
        .from('licenses')
        .update(data)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return result;
    } catch (error) {
      console.error('MCP: Erro ao atualizar licença:', error);
      throw error;
    }
  },

  // Deletar licença
  async deleteLicense(id: string) {
    try {
      const { error } = await supabaseMCP
        .from('licenses')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      return true;
    } catch (error) {
      console.error('MCP: Erro ao deletar licença:', error);
      return false;
    }
  },

  // Obter todas as licenças
  async getAllLicenses() {
    try {
      const { data, error } = await supabaseMCP
        .from('licenses')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('MCP: Erro ao obter licenças:', error);
      return [];
    }
  },

  // Criar usuário
  async createUser(email: string, password: string) {
    try {
      const { data, error } = await supabaseMCP.auth.admin.createUser({
        email,
        password,
        email_confirm: true
      });
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('MCP: Erro ao criar usuário:', error);
      throw error;
    }
  },

  // Deletar usuário
  async deleteUser(userId: string) {
    try {
      const { error } = await supabaseMCP.auth.admin.deleteUser(userId);
      
      if (error) throw error;
      return true;
    } catch (error) {
      console.error('MCP: Erro ao deletar usuário:', error);
      return false;
    }
  },

  // Executar SQL customizado
  async executeSQL(query: string) {
    try {
      const { data, error } = await supabaseMCP.rpc('execute_sql', { query });
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('MCP: Erro ao executar SQL:', error);
      throw error;
    }
  },

  // Upload de arquivo
  async uploadFile(bucket: string, path: string, file: File) {
    try {
      const { data, error } = await supabaseMCP.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('MCP: Erro ao fazer upload:', error);
      throw error;
    }
  },

  // Deletar arquivo
  async deleteFile(bucket: string, path: string) {
    try {
      const { error } = await supabaseMCP.storage
        .from(bucket)
        .remove([path]);
      
      if (error) throw error;
      return true;
    } catch (error) {
      console.error('MCP: Erro ao deletar arquivo:', error);
      return false;
    }
  },

  // Atualizar RLS
  async updateRLS(table: string, policy: string) {
    try {
      const { data, error } = await supabaseMCP.rpc('update_rls_policy', {
        table_name: table,
        policy_definition: policy
      });
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('MCP: Erro ao atualizar RLS:', error);
      throw error;
    }
  },

  // Criar função
  async createFunction(name: string, definition: string) {
    try {
      const { data, error } = await supabaseMCP.rpc('create_function', {
        function_name: name,
        function_definition: definition
      });
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('MCP: Erro ao criar função:', error);
      throw error;
    }
  }
};

// Função para testar conexão MCP
export async function testMCPConnection(): Promise<boolean> {
  try {
    console.log('🔄 Testando conexão MCP do Supabase...');
    
    // Teste 1: Verificar permissões de admin
    const { data: adminTest, error: adminError } = await supabaseMCP.auth.admin.listUsers();
    
    if (adminError) {
      console.error('❌ Erro nas permissões de admin:', adminError);
      return false;
    }
    
    console.log('✅ Permissões de admin funcionando');
    
    // Teste 2: Verificar acesso total ao banco
    const { data: dbTest, error: dbError } = await supabaseMCP
      .from('licenses')
      .select('count')
      .limit(1);
    
    if (dbError) {
      console.error('❌ Erro no acesso ao banco:', dbError);
      return false;
    }
    
    console.log('✅ Acesso total ao banco funcionando');
    
    // Teste 3: Verificar storage
    const { data: storageTest, error: storageError } = await supabaseMCP.storage.listBuckets();
    
    if (storageError) {
      console.warn('⚠️ Storage pode não estar configurado:', storageError);
    } else {
      console.log('✅ Acesso ao storage funcionando');
    }
    
    console.log('🎉 MCP do Supabase está funcionando com permissões completas!');
    return true;
    
  } catch (error) {
    console.error('❌ Erro geral no teste MCP:', error);
    return false;
  }
}

// Exportar para uso global
if (typeof window !== 'undefined') {
  (window as any).supabaseMCP = {
    client: supabaseMCP,
    operations: mcpOperations,
    testConnection: testMCPConnection
  };
  
  console.log('💡 MCP do Supabase disponível globalmente:');
  console.log('supabaseMCP.testConnection() - Testar conexão');
  console.log('supabaseMCP.operations - Operações com permissões completas');
}
