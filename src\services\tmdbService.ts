import axios from 'axios';

const TMDB_API_KEY = 'c9ec732b98be063ee34098ca95fc2341';
const BASE_URL = 'https://api.themoviedb.org/3';

interface TMDBSearchResult {
  results: {
    id: number;
    title?: string;
    name?: string;
    poster_path: string;
    release_date?: string;
    first_air_date?: string;
  }[];
  total_results: number;
  total_pages: number;
}

interface TMDBMovie {
  id: number;
  title: string;
  overview: string;
  poster_path: string;
  backdrop_path: string;
  vote_average: number;
  release_date: string;
  runtime: number;
  genres: { id: number; name: string }[];
  credits: {
    cast: Array<{
      name: string;
      character: string;
    }>;
    crew: Array<{
      name: string;
      job: string;
    }>;
  };
}

interface TMDBTVShow {
  id: number;
  name: string;
  overview: string;
  poster_path: string;
  backdrop_path: string;
  vote_average: number;
  first_air_date: string;
  last_air_date: string;
  status: string;
  number_of_seasons: number;
  number_of_episodes: number;
  episode_run_time: number[];
  genres: { id: number; name: string }[];
  credits: {
    cast: Array<{
      name: string;
      character: string;
    }>;
    crew: Array<{
      name: string;
      job: string;
    }>;
  };
  seasons: Array<{
    id: number;
    name: string;
    season_number: number;
    episode_count: number;
    poster_path: string;
    overview: string;
  }>;
  created_by: Array<{
    id: number;
    name: string;
  }>;
}

class TMDBService {
  private axios = axios.create({
    baseURL: BASE_URL,
    params: {
      api_key: TMDB_API_KEY,
      language: 'pt-BR'
    }
  });

  async searchMovie(query: string): Promise<TMDBSearchResult | null> {
    try {
      console.log('Searching for movie:', query);
      const response = await this.axios.get('/search/movie', {
        params: {
          query: query.replace(/\([^)]*\)/g, '').trim() // Remove content in parentheses
        }
      });

      console.log('Search results:', response.data.results);
      return response.data;
    } catch (error) {
      console.error('Error searching movie:', error);
      return null;
    }
  }

  async getMovieDetails(movieId: number): Promise<TMDBMovie | null> {
    try {
      console.log('Getting details for movie ID:', movieId);
      const response = await this.axios.get(`/movie/${movieId}`, {
        params: {
          append_to_response: 'credits'
        }
      });

      console.log('Movie details:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error getting movie details:', error);
      return null;
    }
  }

  async searchTV(query: string): Promise<TMDBSearchResult | null> {
    try {
      console.log('Searching for TV show:', query);
      const response = await this.axios.get('/search/tv', {
        params: {
          query: query.replace(/\([^)]*\)/g, '').trim() // Remove content in parentheses
        }
      });

      console.log('TV search results:', response.data.results);
      return response.data;
    } catch (error) {
      console.error('Error searching TV show:', error);
      return null;
    }
  }

  async getTVDetails(tvId: number): Promise<TMDBTVShow | null> {
    try {
      console.log('Getting details for TV ID:', tvId);
      const response = await this.axios.get(`/tv/${tvId}`, {
        params: {
          append_to_response: 'credits'
        }
      });

      console.log('TV details:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error getting TV details:', error);
      return null;
    }
  }

  getImageUrl(path: string, size: 'poster' | 'backdrop' = 'poster'): string {
    if (!path) return '';
    
    const baseUrl = 'https://image.tmdb.org/t/p';
    const sizes = {
      poster: 'w500',
      backdrop: 'original'
    };

    return `${baseUrl}/${sizes[size]}${path}`;
  }
}

export const tmdbService = new TMDBService(); 