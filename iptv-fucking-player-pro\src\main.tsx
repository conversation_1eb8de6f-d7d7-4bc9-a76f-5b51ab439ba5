import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserRouter } from 'react-router-dom';
import App from './App';
import './index.css';
import './i18n';

// Check if running in Electron
const isElectronApp = window.location.protocol === 'file:' || (window as any).isElectronApp === true;

// Use HashRouter for Electron and BrowserRouter for web
const Router = isElectronApp ? HashRouter : BrowserRouter;

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Router>
      <App />
    </Router>
  </React.StrictMode>
);