import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import 'react-toastify/dist/ReactToastify.css'

// Components
import Layout from './components/Layout'
import LanguageSelector from './components/LanguageSelector'
import AuthModal from './components/AuthModal'
import ConnectionTabs from './components/ConnectionTabs'
import Dashboard from './pages/Dashboard'
import LiveChannels from './pages/LiveChannels'
import Settings from './pages/Settings'

// Hooks
import { useFirstRun } from './hooks/useFirstRun'
import { useConnectionStore } from './stores/connectionStore'

function App() {
    const { i18n } = useTranslation()
    const { isFirstRun, markAsCompleted } = useFirstRun()
    
    const {
        connections,
        isAuthModalOpen,
        editingConnection,
        closeAuthModal,
        openAuthModal,
        addConnection,
        updateConnection,
        addTab
    } = useConnectionStore()

    const handleLanguageSelect = (language: string) => {
        i18n.changeLanguage(language)
        localStorage.setItem('preferred-language', language)
        markAsCompleted()
    }

    // Check if we need to show the auth modal on startup
    useEffect(() => {
        console.log('🔍 Checking connections on startup:', connections.length, 'connections found')
        if (!isFirstRun && connections.length === 0) {
            console.log('🔓 No connections found, opening auth modal')
            openAuthModal()
        }
    }, [isFirstRun, connections.length, openAuthModal])

    const handleAuthSuccess = (connectionData: any) => {
        console.log('🔐 Auth success, saving connection:', connectionData.name)
        
        if (editingConnection) {
            // Update existing connection
            console.log('📝 Updating existing connection')
            updateConnection(editingConnection.id, {
                ...connectionData,
                updatedAt: Date.now()
            })
        } else {
            // Add new connection
            console.log('➕ Adding new connection')
            const connectionId = addConnection(connectionData)
            const newConnection = { ...connectionData, id: connectionId }
            
            // Add as new tab
            addTab(newConnection as any)
            console.log('📋 Added new tab for connection')
        }
        
        closeAuthModal()
    }

    // Show loading while checking first run status
    if (isFirstRun === null) {
        return (
            <div className="min-h-screen bg-slate-900 flex items-center justify-center">
                <div className="text-white">Loading...</div>
            </div>
        )
    }

    // Show language selector on first run
    if (isFirstRun) {
        return <LanguageSelector onLanguageSelect={handleLanguageSelect} />
    }

    return (
        <div className="min-h-screen bg-slate-900 text-white">
            <div className="flex flex-col h-screen">
                {/* Connection Tabs */}
                <ConnectionTabs />
                
                {/* Main Content */}
                <div className="flex-1 overflow-hidden">
                    <Routes>
                        <Route path="/" element={<Layout />}>
                            <Route index element={<Dashboard />} />
                            <Route path="channels" element={<LiveChannels />} />
                            <Route path="settings" element={<Settings />} />
                        </Route>
                    </Routes>
                </div>
            </div>

            {/* Auth Modal */}
            <AuthModal
                isOpen={isAuthModalOpen}
                onClose={connections.length > 0 ? closeAuthModal : undefined}
                onSuccess={handleAuthSuccess}
                editConnection={editingConnection || undefined}
                isRequired={connections.length === 0}
            />

            <ToastContainer
                position="top-right"
                autoClose={3000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="dark"
            />
        </div>
    )
}

export default App