import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import moment from 'moment';

interface ProgressBarProps {
  startTime: string;
  endTime: string;
  showTime?: boolean;
  height?: number;
  showPercentage?: boolean;
  variant?: 'default' | 'compact';
  backgroundColor?: string;
  foregroundColor?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  startTime,
  endTime,
  showTime = true,
  height = 4,
  showPercentage = false,
  variant = 'default',
  backgroundColor,
  foregroundColor
}) => {
  const theme = useTheme();
  
  // Calcular a porcentagem do progresso
  const calculateProgress = (): number => {
    const now = moment();
    const start = moment(startTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    
    if (!start.isValid() || !end.isValid()) {
      console.warn('Invalid start or end time', { startTime, endTime });
      return 0;
    }
    
    if (!now.isBetween(start, end)) {
      if (now.isBefore(start)) return 0;
      if (now.isAfter(end)) return 100;
    }
    
    const totalDuration = end.diff(start);
    const elapsedDuration = now.diff(start);
    
    return Math.min(100, Math.max(0, (elapsedDuration / totalDuration) * 100));
  };
  
  // Formatar o tempo restante
  const formatTimeRemaining = (): string => {
    const now = moment();
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    
    if (!end.isValid()) return '?';
    
    const diff = end.diff(now, 'minutes');
    if (diff <= 0) return '0 min';
    
    return `${diff} min`;
  };
  
  // Formatar o tempo decorrido
  const formatTimeElapsed = (): string => {
    const now = moment();
    const start = moment(startTime, 'YYYY-MM-DD HH:mm:ss');
    
    if (!start.isValid()) return '?';
    
    const diff = now.diff(start, 'minutes');
    if (diff <= 0) return '0 min';
    
    return `${diff} min`;
  };
  
  // Formatar a duração total
  const formatDuration = (): string => {
    const start = moment(startTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(endTime, 'YYYY-MM-DD HH:mm:ss');
    
    if (!start.isValid() || !end.isValid()) return '? min';
    
    const duration = end.diff(start, 'minutes');
    return `${duration} min`;
  };
  
  // Formatar hora
  const formatTime = (time: string): string => {
    try {
      return moment(time, 'YYYY-MM-DD HH:mm:ss').format('HH:mm');
    } catch (e) {
      return '??:??';
    }
  };
  
  const progress = calculateProgress();
  const bgColor = backgroundColor || (theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.08)');
  const fgColor = foregroundColor || theme.palette.primary.main;
  
  const isCompact = variant === 'compact';
  
  return (
    <Box sx={{ width: '100%' }}>
      <Box
        sx={{
          position: 'relative',
          height,
          width: '100%',
          bgcolor: bgColor,
          borderRadius: height / 2,
          overflow: 'hidden'
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            left: 0,
            top: 0,
            height: '100%',
            width: `${progress}%`,
            bgcolor: fgColor,
            borderRadius: height / 2
          }}
        />
      </Box>
      
      {showTime && (
        <Box 
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mt: 0.5
          }}
        >
          {isCompact ? (
            <>
              <Typography 
                variant="caption" 
                color="text.secondary" 
                sx={{ fontSize: '0.65rem' }}
              >
                {formatTimeElapsed()}
              </Typography>
              
              {showPercentage && (
                <Typography 
                  variant="caption" 
                  color="text.secondary" 
                  sx={{ fontSize: '0.65rem' }}
                >
                  {Math.round(progress)}%
                </Typography>
              )}
              
              <Typography 
                variant="caption" 
                color="text.secondary" 
                sx={{ fontSize: '0.65rem' }}
              >
                {formatDuration()}
              </Typography>
            </>
          ) : (
            <>
              <Typography variant="caption" color="text.secondary">
                {formatTime(startTime)}
              </Typography>
              
              {showPercentage && (
                <Typography variant="caption" color="text.secondary">
                  {Math.round(progress)}%
                </Typography>
              )}
              
              <Typography variant="caption" color="text.secondary">
                {formatTime(endTime)} ({formatTimeRemaining()} restantes)
              </Typography>
            </>
          )}
        </Box>
      )}
    </Box>
  );
};

export default ProgressBar; 