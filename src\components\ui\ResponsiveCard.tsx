import React from 'react';
import { 
  Card, 
  CardContent, 
  CardMedia, 
  Typography, 
  Box, 
  IconButton,
  Chip,
  useTheme,
  useMediaQuery 
} from '@mui/material';
import { styled } from '@mui/material/styles';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import InfoIcon from '@mui/icons-material/Info';

interface ResponsiveCardProps {
  title: string;
  subtitle?: string;
  imageUrl?: string;
  aspectRatio?: '16:9' | '4:3' | '1:1' | 'auto';
  variant?: 'channel' | 'movie' | 'series' | 'episode';
  isFavorite?: boolean;
  isLive?: boolean;
  duration?: string;
  rating?: number;
  year?: string;
  onClick?: () => void;
  onFavoriteClick?: () => void;
  onInfoClick?: () => void;
  className?: string;
}

// Base card with responsive design
const StyledCard = styled(Card)(({ theme }) => ({
  position: 'relative',
  borderRadius: 12,
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    '& .card-overlay': {
      opacity: 1,
    },
    '& .card-media': {
      transform: 'scale(1.05)',
    }
  },
  
  [theme.breakpoints.down('sm')]: {
    borderRadius: 8,
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[4],
    }
  }
}));

// Responsive card media
const StyledCardMedia = styled(CardMedia)<{ aspectRatio: string }>(({ theme, aspectRatio }) => ({
  position: 'relative',
  transition: 'transform 0.3s ease',
  backgroundColor: theme.palette.grey[900],
  
  ...(aspectRatio !== 'auto' && {
    aspectRatio: aspectRatio,
  }),
  
  '&.auto-height': {
    height: 200,
    [theme.breakpoints.down('sm')]: {
      height: 150,
    }
  }
}));

// Overlay for hover effects
const CardOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(0deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, transparent 100%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 0,
  transition: 'opacity 0.3s ease',
  zIndex: 2,
}));

// Action buttons container
const ActionButtons = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  zIndex: 3,
}));

// Live indicator
const LiveIndicator = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  left: theme.spacing(1),
  backgroundColor: '#ff1744',
  color: 'white',
  fontSize: '0.7rem',
  height: 24,
  zIndex: 3,
  
  '& .MuiChip-label': {
    padding: '0 8px',
    fontWeight: 600,
  }
}));

// Content area with responsive typography
const StyledCardContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(1.5),
  
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(2),
  },
  
  '&:last-child': {
    paddingBottom: theme.spacing(1.5),
    
    [theme.breakpoints.up('sm')]: {
      paddingBottom: theme.spacing(2),
    }
  }
}));

const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  title,
  subtitle,
  imageUrl,
  aspectRatio = '16:9',
  variant = 'movie',
  isFavorite = false,
  isLive = false,
  duration,
  rating,
  year,
  onClick,
  onFavoriteClick,
  onInfoClick,
  className
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.();
  };
  
  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFavoriteClick?.();
  };
  
  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onInfoClick?.();
  };
  
  return (
    <StyledCard className={className} onClick={handleCardClick}>
      {/* Media section */}
      <Box sx={{ position: 'relative' }}>
        <StyledCardMedia
          className={`card-media ${aspectRatio === 'auto' ? 'auto-height' : ''}`}
          image={imageUrl || '/placeholder-image.jpg'}
          title={title}
          aspectRatio={aspectRatio}
        />
        
        {/* Live indicator */}
        {isLive && <LiveIndicator label="AO VIVO" size="small" />}
        
        {/* Action buttons */}
        <ActionButtons>
          {onFavoriteClick && (
            <IconButton
              size="small"
              onClick={handleFavoriteClick}
              sx={{
                backgroundColor: 'rgba(0,0,0,0.6)',
                color: isFavorite ? '#ff1744' : 'white',
                '&:hover': {
                  backgroundColor: 'rgba(0,0,0,0.8)',
                }
              }}
            >
              {isFavorite ? <FavoriteIcon fontSize="small" /> : <FavoriteBorderIcon fontSize="small" />}
            </IconButton>
          )}
          
          {onInfoClick && (
            <IconButton
              size="small"
              onClick={handleInfoClick}
              sx={{
                backgroundColor: 'rgba(0,0,0,0.6)',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(0,0,0,0.8)',
                }
              }}
            >
              <InfoIcon fontSize="small" />
            </IconButton>
          )}
        </ActionButtons>
        
        {/* Hover overlay */}
        <CardOverlay className="card-overlay">
          <IconButton
            size="large"
            sx={{
              backgroundColor: 'rgba(255,255,255,0.9)',
              color: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: 'white',
              }
            }}
          >
            <PlayArrowIcon fontSize="large" />
          </IconButton>
        </CardOverlay>
      </Box>
      
      {/* Content section */}
      <StyledCardContent>
        <Typography
          variant="h6"
          component="h3"
          sx={{
            fontSize: { xs: '0.9rem', sm: '1rem' },
            fontWeight: 600,
            lineHeight: 1.3,
            marginBottom: 0.5,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
          }}
        >
          {title}
        </Typography>
        
        {subtitle && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              fontSize: { xs: '0.75rem', sm: '0.8rem' },
              marginBottom: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {subtitle}
          </Typography>
        )}
        
        {/* Metadata */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
          {year && (
            <Typography variant="caption" color="text.secondary">
              {year}
            </Typography>
          )}
          
          {duration && (
            <Typography variant="caption" color="text.secondary">
              {duration}
            </Typography>
          )}
          
          {rating && (
            <Typography 
              variant="caption" 
              sx={{ 
                color: rating >= 7 ? 'success.main' : rating >= 5 ? 'warning.main' : 'error.main',
                fontWeight: 600 
              }}
            >
              ★ {rating.toFixed(1)}
            </Typography>
          )}
        </Box>
      </StyledCardContent>
    </StyledCard>
  );
};

export default ResponsiveCard;
