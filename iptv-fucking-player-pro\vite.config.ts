import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { resolve } from 'path';

export default defineConfig(({ command, mode }) => {
  const isElectron = process.env.ELECTRON === 'true';
  const isProduction = mode === 'production';
  
  console.log(`Building for ${isElectron ? 'Electron' : 'Web'}, mode: ${mode}`);

  return {
    plugins: [
      react({
        fastRefresh: true
      })
    ],
    base: './',
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@utils': resolve(__dirname, './src/utils'),
        '@types': resolve(__dirname, './src/types'),
        '@hooks': resolve(__dirname, './src/hooks'),
        '@contexts': resolve(__dirname, './src/contexts'),
        '@services': resolve(__dirname, './src/services'),
        '@assets': resolve(__dirname, './src/assets'),
        '@styles': resolve(__dirname, './src/styles'),
        '@stores': resolve(__dirname, './src/stores'),
        '@i18n': resolve(__dirname, './src/i18n')
      }
    },
    server: {
      port: 4000,
      host: true,
      strictPort: true,
      watch: {
        usePolling: true
      }
    },
    preview: {
      port: 4000,
      host: true,
      strictPort: true
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      chunkSizeWarningLimit: 2000,
      assetsDir: 'assets',
      emptyOutDir: true,
      minify: isProduction ? 'esbuild' : false,
      target: 'es2015',
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
                return 'react-vendor';
              }
              if (id.includes('hls.js') || id.includes('video.js')) {
                return 'video-vendor';
              }
              if (id.includes('framer-motion')) {
                return 'animation-vendor';
              }
            }
          },
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              return 'assets/[name]-[hash].css';
            }
            return 'assets/[name]-[hash].[ext]';
          }
        },
        external: isElectron ? ['better-sqlite3'] : undefined,
        onwarn(warning, warn) {
          if (warning.code === 'MODULE_LEVEL_DIRECTIVE') return;
          if (warning.code === 'SOURCEMAP_ERROR') return;
          warn(warning);
        }
      }
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'hls.js',
        'video.js',
        'framer-motion'
      ],
      exclude: isElectron ? ['better-sqlite3'] : []
    },
    define: {
      'process.env': {},
      ...(isElectron ? { 'window.isElectronApp': true } : {})
    }
  };
});