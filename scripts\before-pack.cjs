// Este script é executado pelo electron-builder antes do empacotamento
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

module.exports = async function(context) {
  console.log('Executando script before-pack...');
  
  try {
    // Verificar se o contexto é válido
    if (!context || !context.appDir) {
      console.log('Contexto inválido ou não possui appDir. Usando diretório atual.');
      context = {
        appDir: process.cwd()
      };
    }
    
    // Verificar se o node_modules está no lugar certo
    const nodeModulesPath = path.join(context.appDir, 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
      console.log('Criando pasta node_modules...');
      fs.mkdirSync(nodeModulesPath, { recursive: true });
    }
    
    // Copiar o módulo node-machine-id para o diretório de build
    const sourcePath = path.resolve('node_modules/node-machine-id');
    const targetPath = path.join(nodeModulesPath, 'node-machine-id');
    
    if (fs.existsSync(sourcePath)) {
      console.log(`Copiando node-machine-id de ${sourcePath} para ${targetPath}`);
      
      // Garantir que o diretório de destino exista
      fs.mkdirSync(targetPath, { recursive: true });
      
      // Copiar os arquivos necessários
      copyFolderRecursiveSync(sourcePath, nodeModulesPath);
      
      console.log('node-machine-id copiado com sucesso!');
    } else {
      console.error('Módulo node-machine-id não encontrado em node_modules!');
    }
    
    // Copiar electron-store também
    const electronStorePath = path.resolve('node_modules/electron-store');
    const electronStoreTargetPath = path.join(nodeModulesPath, 'electron-store');
    
    if (fs.existsSync(electronStorePath)) {
      console.log(`Copiando electron-store de ${electronStorePath} para ${electronStoreTargetPath}`);
      
      // Garantir que o diretório de destino exista
      fs.mkdirSync(electronStoreTargetPath, { recursive: true });
      
      // Copiar os arquivos necessários
      copyFolderRecursiveSync(electronStorePath, nodeModulesPath);
      
      console.log('electron-store copiado com sucesso!');
    } else {
      console.error('Módulo electron-store não encontrado em node_modules!');
    }
    
    console.log('Script before-pack concluído com sucesso!');
  } catch (error) {
    console.error('Erro no script before-pack:', error);
  }
};

// Funções auxiliares
function copyFolderRecursiveSync(source, target) {
  const targetFolder = path.join(target, path.basename(source));
  
  // Criar pasta de destino se não existir
  if (!fs.existsSync(targetFolder)) {
    fs.mkdirSync(targetFolder, { recursive: true });
  }
  
  // Copiar todos os arquivos recursivamente
  if (fs.lstatSync(source).isDirectory()) {
    const files = fs.readdirSync(source);
    
    files.forEach(file => {
      const currentSource = path.join(source, file);
      
      if (fs.lstatSync(currentSource).isDirectory()) {
        copyFolderRecursiveSync(currentSource, targetFolder);
      } else {
        const targetFile = path.join(targetFolder, file);
        fs.copyFileSync(currentSource, targetFile);
      }
    });
  }
} 