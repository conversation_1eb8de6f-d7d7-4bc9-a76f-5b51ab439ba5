import React from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const Player: React.FC = () => {
  const { type, id } = useParams<{ type: string; id: string }>();
  const { t } = useTranslation();

  return (
    <div className="h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-white mb-4">
          Video Player
        </h1>
        <p className="text-secondary-300">
          Playing {type} with ID: {id}
        </p>
        <p className="text-secondary-400 mt-2">
          Video player will be implemented in the next tasks.
        </p>
      </div>
    </div>
  );
};

export default Player;