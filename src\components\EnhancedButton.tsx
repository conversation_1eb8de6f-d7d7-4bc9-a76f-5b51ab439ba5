import React from 'react';
import {
  Button,
  CircularProgress,
  Box,
  useTheme,
  alpha
} from '@mui/material';
import { motion } from 'framer-motion';

interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
}

const MotionButton = motion.create(Button);

const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  loading = false,
  disabled = false,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  startIcon,
  endIcon,
  type = 'button'
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  const getButtonStyles = () => {
    const baseStyles = {
      borderRadius: 3,
      textTransform: 'none' as const,
      fontWeight: 600,
      fontSize: size === 'small' ? '0.875rem' : size === 'large' ? '1.125rem' : '1rem',
      padding: size === 'small' 
        ? '8px 16px' 
        : size === 'large' 
          ? '16px 32px' 
          : '12px 24px',
      position: 'relative' as const,
      overflow: 'hidden' as const,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '-100%',
        width: '100%',
        height: '100%',
        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
        transition: 'left 0.5s ease',
        zIndex: 1
      },
      '&:hover::before': {
        left: '100%'
      }
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, 
            ${theme.palette.primary.main}, 
            ${theme.palette.primary.dark})`,
          color: '#ffffff',
          boxShadow: `0 4px 15px ${alpha(theme.palette.primary.main, 0.4)}`,
          border: 'none',
          '&:hover': {
            background: `linear-gradient(135deg, 
              ${theme.palette.primary.dark}, 
              ${theme.palette.primary.main})`,
            boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.6)}`,
            transform: 'translateY(-2px)'
          },
          '&:active': {
            transform: 'translateY(0px)',
            boxShadow: `0 2px 10px ${alpha(theme.palette.primary.main, 0.4)}`
          },
          '&:disabled': {
            background: alpha(theme.palette.primary.main, 0.3),
            boxShadow: 'none',
            transform: 'none'
          }
        };

      case 'secondary':
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, 
            ${theme.palette.secondary.main}, 
            ${theme.palette.secondary.dark})`,
          color: '#ffffff',
          boxShadow: `0 4px 15px ${alpha(theme.palette.secondary.main, 0.4)}`,
          border: 'none',
          '&:hover': {
            background: `linear-gradient(135deg, 
              ${theme.palette.secondary.dark}, 
              ${theme.palette.secondary.main})`,
            boxShadow: `0 6px 20px ${alpha(theme.palette.secondary.main, 0.6)}`,
            transform: 'translateY(-2px)'
          },
          '&:active': {
            transform: 'translateY(0px)',
            boxShadow: `0 2px 10px ${alpha(theme.palette.secondary.main, 0.4)}`
          },
          '&:disabled': {
            background: alpha(theme.palette.secondary.main, 0.3),
            boxShadow: 'none',
            transform: 'none'
          }
        };

      case 'outline':
        return {
          ...baseStyles,
          background: 'transparent',
          color: theme.palette.primary.main,
          border: `2px solid ${theme.palette.primary.main}`,
          boxShadow: 'none',
          '&:hover': {
            background: alpha(theme.palette.primary.main, 0.1),
            borderColor: theme.palette.primary.dark,
            color: theme.palette.primary.dark,
            transform: 'translateY(-1px)'
          },
          '&:active': {
            transform: 'translateY(0px)',
            background: alpha(theme.palette.primary.main, 0.2)
          },
          '&:disabled': {
            borderColor: alpha(theme.palette.primary.main, 0.3),
            color: alpha(theme.palette.primary.main, 0.3),
            transform: 'none'
          }
        };

      case 'ghost':
        return {
          ...baseStyles,
          background: 'transparent',
          color: theme.palette.text.primary,
          border: 'none',
          boxShadow: 'none',
          '&:hover': {
            background: alpha(theme.palette.primary.main, 0.1),
            color: theme.palette.primary.main,
            transform: 'translateY(-1px)'
          },
          '&:active': {
            transform: 'translateY(0px)',
            background: alpha(theme.palette.primary.main, 0.2)
          },
          '&:disabled': {
            color: alpha(theme.palette.text.primary, 0.3),
            transform: 'none'
          }
        };

      default:
        return baseStyles;
    }
  };

  return (
    <MotionButton
      onClick={onClick}
      disabled={disabled || loading}
      fullWidth={fullWidth}
      type={type}
      startIcon={!loading && startIcon}
      endIcon={!loading && endIcon}
      sx={getButtonStyles()}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {loading ? (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CircularProgress 
            size={20} 
            sx={{ 
              color: variant === 'outline' || variant === 'ghost' 
                ? theme.palette.primary.main 
                : '#ffffff' 
            }} 
          />
          Carregando...
        </Box>
      ) : (
        children
      )}
    </MotionButton>
  );
};

export default EnhancedButton;