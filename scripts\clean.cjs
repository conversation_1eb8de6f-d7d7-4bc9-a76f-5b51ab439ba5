const fs = require('fs');
const path = require('path');

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
}

const distElectronPath = path.join(__dirname, '..', 'dist_electron');
console.log('Limpando pasta dist_electron...');
deleteFolderRecursive(distElectronPath);
console.log('Pasta dist_electron limpa com sucesso!'); 