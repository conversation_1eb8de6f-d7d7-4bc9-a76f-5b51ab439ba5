# Dependencies que devem ser ignoradas
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Electron (não precisa no Vercel)
dist_electron/
electron/
*.exe
*.dmg
*.AppImage
*.deb

# Archives
*.rar
*.zip
*.tar.gz

# Temp files
temp_content.txt

# Large directories não necessários para o build web
proxy-server/
resources/
giga/
NEKO\ FUNCIONANDO\ APK/
neko-tv-mobile/
examples/
como\ um\ proxy\ funciona/

# Documentation (manter apenas README)
*.md
!README.md

# Arquivos Docker (não necessários no Vercel)
docker-compose.yml
Dockerfile
nginx.conf
cleanup_duplicates.bat

# Database
*.sql

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Scripts de desenvolvimento que não são necessários para build
scripts/clean.cjs
scripts/after-pack.cjs
scripts/before-pack.cjs

# Electron specific
electron-builder.cjs

# Arquivos de exemplo e temporários
admin-panel-example.html
admin-panel-example.js
admin.html
bkneko.rar

# Logs CSV
*.csv

# Testes
**/*.test.*
**/*.spec.*
vitest.config.ts

# Cache do Node
.npm/
.pnpm-store/

# Cache do build local
.vite/
.next/
.nuxt/
.output/

# Logs
logs/
*.log

# Arquivo de playlist muito grande
tv_channels_aAp2N8_plus.m3u 