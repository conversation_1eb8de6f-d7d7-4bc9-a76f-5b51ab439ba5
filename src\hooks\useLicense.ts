import { useState, useEffect, useCallback } from 'react';
import { LicenseService, LicenseCheckResult } from '../services/licenseService';
import type { License } from '../integrations/supabase/types';

export interface UseLicenseReturn {
  license: License | null;
  isValid: boolean;
  isLoading: boolean;
  error: string | null;
  checkLicense: (licenseKey: string, deviceId: string) => Promise<LicenseCheckResult>;
  refreshLicense: () => Promise<void>;
}

/**
 * Hook para gerenciar licenças
 */
export function useLicense(licenseKey?: string, deviceId?: string): UseLicenseReturn {
  const [license, setLicense] = useState<License | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkLicense = useCallback(async (key: string, device: string): Promise<LicenseCheckResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await LicenseService.checkLicense(key, device);
      setIsValid(result.valid);
      
      if (result.valid) {
        // Buscar dados completos da licença
        const licenseData = await LicenseService.getLicenseByKey(key);
        setLicense(licenseData);
      } else {
        setLicense(null);
        setError(result.message);
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      setIsValid(false);
      setLicense(null);
      
      return {
        valid: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshLicense = useCallback(async () => {
    if (licenseKey && deviceId) {
      await checkLicense(licenseKey, deviceId);
    }
  }, [licenseKey, deviceId, checkLicense]);

  // Verificar licença automaticamente quando os parâmetros mudarem
  useEffect(() => {
    if (licenseKey && deviceId) {
      checkLicense(licenseKey, deviceId);
    }
  }, [licenseKey, deviceId, checkLicense]);

  return {
    license,
    isValid,
    isLoading,
    error,
    checkLicense,
    refreshLicense
  };
}

/**
 * Hook para listar todas as licenças (para admin)
 */
export function useAllLicenses() {
  const [licenses, setLicenses] = useState<License[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLicenses = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await LicenseService.getAllLicenses();
      setLicenses(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar licenças';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateLicense = useCallback(async (clientName?: string, iptvUrl?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await LicenseService.generateLicense(clientName, iptvUrl);
      if (result) {
        // Recarregar a lista
        await fetchLicenses();
        return result;
      } else {
        throw new Error('Falha ao gerar licença');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao gerar licença';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [fetchLicenses]);

  const toggleLicense = useCallback(async (licenseKey: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const success = await LicenseService.toggleLicense(licenseKey);
      if (success) {
        // Recarregar a lista
        await fetchLicenses();
        return true;
      } else {
        throw new Error('Falha ao alterar status da licença');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao alterar licença';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [fetchLicenses]);

  const deleteLicense = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const success = await LicenseService.deleteLicense(id);
      if (success) {
        // Recarregar a lista
        await fetchLicenses();
        return true;
      } else {
        throw new Error('Falha ao deletar licença');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao deletar licença';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [fetchLicenses]);

  useEffect(() => {
    fetchLicenses();
  }, [fetchLicenses]);

  return {
    licenses,
    isLoading,
    error,
    fetchLicenses,
    generateLicense,
    toggleLicense,
    deleteLicense
  };
}
