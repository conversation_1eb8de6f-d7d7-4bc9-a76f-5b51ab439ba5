// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables for the keys (vite uses import.meta.env)
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Check if environment variables are available
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.warn(
    "⚠️ WARNING: Supabase environment variables not found. Using fallback values. " +
    "This is not recommended for production environments. " +
    "Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file."
  );
}

// Fallback only if environment variables are not available
const supabaseUrl = SUPABASE_URL || "https://kzvhuqfkqoncgsyezaaq.supabase.co";
const supabaseKey = SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt6dmh1cWZrcW9uY2dzeWV6YWFxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTI3NjcsImV4cCI6MjA2MjM2ODc2N30.L8agsQM1GDdkVt-ROWFfuHMsk7zGzjHzkW-JJQChXgs";

// Create the client instance
export const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Configuration object
export const supabaseConfig = {
  url: supabaseUrl,
  anonKey: supabaseKey
};

// Function to get client instance
export const getSupabaseClient = () => supabase;

// Export the client as default
export default supabase; 