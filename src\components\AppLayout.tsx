import React from 'react';
import { Box, useTheme, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles';
import MainNavigation from './MainNavigation';

interface AppLayoutProps {
  children: React.ReactNode;
  userInfo?: any;
  serverInfo?: any;
  showTitle?: boolean;
  fullscreen?: boolean;
}

// Responsive container with proper spacing
const ResponsiveContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  minHeight: '100vh',
  width: '100%',
  overflow: 'hidden',

  [theme.breakpoints.down('sm')]: {
    minHeight: '100vh',
    height: '100vh',
  }
}));

// Main content area with responsive padding
const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  overflow: 'auto',

  // Desktop spacing - o MainNavigation controla o espaçamento
  paddingTop: 0,

  // Remove padding for fullscreen content
  '&.fullscreen': {
    paddingTop: 0,
    height: '100vh',
    overflow: 'hidden',
  }
}));

/**
 * Layout principal do aplicativo que inclui a navegação fixa no topo
 * e aplica o espaçamento apropriado para o conteúdo principal
 */
const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  userInfo = null,
  serverInfo = null,
  showTitle = true,
  fullscreen = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <ResponsiveContainer>
      {/* Navegação fixa no topo - oculta em fullscreen */}
      {!fullscreen && (
        <MainNavigation
          showTitle={showTitle}
          userInfo={userInfo}
          serverInfo={serverInfo}
        />
      )}

      {/* Conteúdo principal com espaçamento responsivo */}
      <MainContent
        component="main"
        className={fullscreen ? 'fullscreen' : ''}
      >
        {children}
      </MainContent>
    </ResponsiveContainer>
  );
};

export default AppLayout; 