/* Estilos para o toast de próximo episódio */
@keyframes slideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.next-episode-toast {
  background-color: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  border-left: 6px solid #2196f3 !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4) !important;
  padding: 16px !important;
  min-height: 90px !important;
  max-width: 500px !important;
  min-width: 350px !important;
  animation: slideIn 0.4s ease-out forwards !important;
  margin-bottom: 16px !important;
  z-index: 99999 !important;
}

/* Ajuste para o container do toast quando posicionado no topo */
.Toastify__toast-container--top-center {
  top: 15% !important;
  z-index: 99999 !important;
  width: auto !important;
  max-width: 80% !important;
  margin: 0 auto !important;
}

.Toastify__toast-body {
  padding: 0 !important;
  margin: 0 !important;
  font-family: 'Roboto', sans-serif !important;
}

.Toastify__progress-bar--info {
  background: linear-gradient(to right, #2196f3, #21d4fd) !important;
  height: 6px !important;
  border-radius: 0 0 6px 6px !important;
}

.Toastify__toast-container {
  padding: 0 !important;
  width: auto !important;
} 