import React, { useEffect, useRef } from 'react';
import { Box, useTheme, alpha } from '@mui/material';

interface BackgroundEffectsProps {
  type?: 'default' | 'movies' | 'series';
  intensity?: 'low' | 'medium' | 'high';
}

const BackgroundEffects: React.FC<BackgroundEffectsProps> = ({ 
  type = 'default',
  intensity = 'medium'
}) => {
  const theme = useTheme();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const isDarkMode = theme.palette.mode === 'dark';
  
  // Efeito para animações usando canvas
  useEffect(() => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Configurar tamanho do canvas para preencher a tela
    const updateCanvasSize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    // Atualizar tamanho inicial e adicionar listener para quando a janela for redimensionada
    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    
    // Configurações de cores baseadas no tema
    const primaryColor = isDarkMode ? '#0ea5e9' : '#0284c7';
    
    // Ajustar o alpha com base na intensidade selecionada
    const getAlpha = (baseAlpha: number): number => {
      switch(intensity) {
        case 'low': return baseAlpha * 0.5;
        case 'high': return baseAlpha * 1.5;
        default: return baseAlpha;
      }
    };
    
    // Partículas para o efeito fluido
    const particles: {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      opacity: number;
      color: string;
    }[] = [];
    
    // Inicializar partículas com base no tipo
    const initParticles = () => {
      particles.length = 0;
      
      // Número de partículas com base na intensidade
      const particleCount = {
        low: 30,
        medium: 50,
        high: 80
      }[intensity];
      
      // Diferentes configurações baseadas no tipo de conteúdo
      for (let i = 0; i < particleCount; i++) {
        const baseColor = type === 'movies' 
          ? '#e11d48' // Vermelho para filmes
          : type === 'series' 
            ? '#7c3aed' // Roxo para séries
            : primaryColor; // Azul para o padrão
            
        // Cores secundárias para misturar
        const secondaryColor = type === 'movies'
          ? '#f43f5e' // Rosa para filmes
          : type === 'series'
            ? '#8b5cf6' // Lilás para séries
            : '#38bdf8'; // Azul claro para o padrão
        
        // Escolher entre cor primária e secundária aleatoriamente
        const color = Math.random() > 0.5 ? baseColor : secondaryColor;
        
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 4 + 0.5,
          speedX: (Math.random() - 0.5) * 0.3,
          speedY: (Math.random() - 0.5) * 0.3,
          opacity: getAlpha(Math.random() * 0.25 + 0.05),
          color
        });
      }
    };
    
    // Inicializar partículas
    initParticles();
    
    // Variável para armazenar o ID do frame de animação
    let animationFrameId: number;
    
    // Função para animar partículas
    const animate = () => {
      // Limpar o canvas com um fundo transparente
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Desenhar cada partícula
      particles.forEach((particle) => {
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = `${particle.color}${Math.floor(particle.opacity * 255).toString(16).padStart(2, '0')}`;
        ctx.fill();
        
        // Mover partícula
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Verificar bordas e "rebater" partículas
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.speedX *= -1;
        }
        
        if (particle.y < 0 || particle.y > canvas.height) {
          particle.speedY *= -1;
        }
      });
      
      // Continuar animação
      animationFrameId = requestAnimationFrame(animate);
    };
    
    // Iniciar animação
    animate();
    
    // Limpar quando o componente for desmontado
    return () => {
      window.removeEventListener('resize', updateCanvasSize);
      cancelAnimationFrame(animationFrameId);
    };
  }, [type, intensity, isDarkMode]);
  
  // Gerar gradiente baseado no tipo
  const getBackgroundGradient = () => {
    if (isDarkMode) {
      switch (type) {
        case 'movies':
          return 'radial-gradient(ellipse at top right, rgba(225, 29, 72, 0.15) 0%, rgba(0, 0, 0, 0) 50%)';
        case 'series':
          return 'radial-gradient(ellipse at top right, rgba(124, 58, 237, 0.15) 0%, rgba(0, 0, 0, 0) 50%)';
        default:
          return 'radial-gradient(ellipse at top right, rgba(14, 165, 233, 0.15) 0%, rgba(0, 0, 0, 0) 50%)';
      }
    } else {
      switch (type) {
        case 'movies':
          return 'radial-gradient(ellipse at top right, rgba(225, 29, 72, 0.08) 0%, rgba(255, 255, 255, 0) 50%)';
        case 'series':
          return 'radial-gradient(ellipse at top right, rgba(124, 58, 237, 0.08) 0%, rgba(255, 255, 255, 0) 50%)';
        default:
          return 'radial-gradient(ellipse at top right, rgba(14, 165, 233, 0.08) 0%, rgba(255, 255, 255, 0) 50%)';
      }
    }
  };

  // Gerar padrão baseado no tipo
  const getPatternStyle = () => {
    if (isDarkMode) {
      const patternColor = type === 'movies' 
        ? alpha('#e11d48', 0.12)
        : type === 'series'
          ? alpha('#7c3aed', 0.12)
          : alpha('#0ea5e9', 0.12);
      
      return {
        backgroundImage: `radial-gradient(${patternColor} 1px, transparent 1px)`,
        backgroundSize: '30px 30px'
      };
    } else {
      const patternColor = type === 'movies' 
        ? alpha('#e11d48', 0.08)
        : type === 'series'
          ? alpha('#7c3aed', 0.08)
          : alpha('#0ea5e9', 0.08);
      
      return {
        backgroundImage: `radial-gradient(${patternColor} 1px, transparent 1px)`,
        backgroundSize: '30px 30px'
      };
    }
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: -1,
        overflow: 'hidden',
        background: isDarkMode
          ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)'
          : 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)',
        ...getPatternStyle(),
        pointerEvents: 'none', // Para não interferir nos clicks
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
        }}
      />
    </Box>
  );
};

export default BackgroundEffects; 