import React from 'react';
import { useTranslation } from 'react-i18next';
import { useConnectionStore } from '../stores/connectionStore';
import { FiPlus, FiSettings } from 'react-icons/fi';

const Settings: React.FC = () => {
  const { t } = useTranslation();
  const { connections, openAuthModal } = useConnectionStore();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-white">
        Settings
      </h1>
      
      <div className="bg-slate-800 border border-slate-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-white">IPTV Connections</h2>
          <button
            onClick={() => {
              console.log('🔘 Add Connection button clicked')
              openAuthModal()
            }}
            className="flex items-center px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
          >
            <FiPlus className="mr-2" />
            Add Connection
          </button>
        </div>
        
        {connections.length > 0 ? (
          <div className="space-y-3">
            {connections.map((connection) => (
              <div key={connection.id} className="bg-slate-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-white">{connection.name}</h3>
                    <p className="text-sm text-slate-400">{connection.url}</p>
                    <p className="text-xs text-slate-500">
                      Last used: {new Date(connection.lastUsed).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {connection.isActive && (
                      <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
                        Active
                      </span>
                    )}
                    <button
                      onClick={() => openAuthModal(connection)}
                      className="p-2 text-slate-400 hover:text-white transition-colors"
                    >
                      <FiSettings className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-slate-400 mb-4">No IPTV connections configured</p>
            <button
              onClick={() => openAuthModal()}
              className="px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
            >
              Add Your First Connection
            </button>
          </div>
        )}
      </div>

      <div className="bg-slate-800 border border-slate-700 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-white mb-4">Application Settings</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Language
            </label>
            <select className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-sky-500">
              <option value="en-US">English</option>
              <option value="pt-BR">Português (Brasil)</option>
              <option value="es-ES">Español</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Theme
            </label>
            <select className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-sky-500">
              <option value="dark">Dark</option>
              <option value="light">Light</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;