import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useInView } from 'react-intersection-observer';
import styled from 'styled-components';

interface MediaItemProps {
  id: string;
  title: string;
  type: 'channel' | 'movie' | 'series';
  thumbnail?: string;
  logo?: string;
  epg?: {
    current?: string;
    next?: string;
  };
  progress?: number;
}

interface MediaGridProps {
  items: MediaItemProps[];
  type: 'channel' | 'movie' | 'series';
  loading: boolean;
  onLoadMore: () => void;
  hasMore: boolean;
}

const StyledMediaCard = styled.div<{ type: string }>`
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #1a1a1a;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .media-thumbnail {
    position: relative;
    padding-top: ${props => props.type === 'channel' ? '56.25%' : '150%'};
    background: #000;
  }

  .media-thumbnail img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.2);
  }

  .progress {
    height: 100%;
    background: #007bff;
    transition: width 0.3s;
  }

  .media-info {
    padding: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .media-info h3 {
    margin: 0;
    font-size: 1rem;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .epg-info {
    margin-top: 8px;
    font-size: 0.875rem;
  }

  .epg-info .current {
    color: #fff;
    margin: 0;
  }

  .epg-info .next {
    color: #999;
    margin: 4px 0 0;
    font-size: 0.8125rem;
  }
`;

const StyledMediaGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  padding: 20px;

  .load-more {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    padding: 20px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
    padding: 12px;
  }
`;

const MediaCard: React.FC<MediaItemProps> = ({ id, title, type, thumbnail, logo, epg, progress }) => {
  const navigate = useNavigate();
  const defaultThumbnail = type === 'channel' 
    ? '/images/default-channel.jpg'
    : type === 'movie'
    ? '/images/default-movie.jpg'
    : '/images/default-series.jpg';

  const handleClick = () => {
    navigate(`/player/${id}`, { 
      state: { 
        item: {
          id,
          title,
          type,
          thumbnail,
          logo
        }
      }
    });
  };

  return (
    <StyledMediaCard 
      type={type}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === 'Enter' && handleClick()}
    >
      <div className="media-thumbnail">
        <img 
          src={thumbnail || logo || defaultThumbnail} 
          alt={title}
          loading="lazy"
        />
        {progress !== undefined && progress > 0 && (
          <div className="progress-bar">
            <div 
              className="progress" 
              style={{ width: `${progress}%` }}
              role="progressbar"
              aria-valuenow={progress}
              aria-valuemin={0}
              aria-valuemax={100}
            />
          </div>
        )}
      </div>
      <div className="media-info">
        <h3>{title}</h3>
        {type === 'channel' && epg && (
          <div className="epg-info">
            <p className="current">{epg.current}</p>
            {epg.next && <p className="next">Next: {epg.next}</p>}
          </div>
        )}
      </div>
    </StyledMediaCard>
  );
};

const MediaGrid: React.FC<MediaGridProps> = ({ items, type, loading, onLoadMore, hasMore }) => {
  const { ref, inView } = useInView({
    threshold: 0,
    triggerOnce: false
  });

  React.useEffect(() => {
    if (inView && hasMore && !loading) {
      onLoadMore();
    }
  }, [inView, hasMore, loading, onLoadMore]);

  return (
    <StyledMediaGrid>
      {items.map((item) => (
        <MediaCard key={item.id} {...item} type={type} />
      ))}
      {hasMore && (
        <div ref={ref} className="load-more">
          {loading && <div className="loading-spinner" />}
        </div>
      )}
    </StyledMediaGrid>
  );
};

export default MediaGrid; 