import React, { memo, useCallback } from 'react';
import { List, ListItem, ListItemButton, ListItemText, useTheme, useMediaQuery, Tooltip } from '@mui/material';
import { Category } from '../services/iptvService';

interface CategoryListProps {
  categories: Category[];
  selectedCategoryId: string;
  onCategorySelect: (categoryId: string) => void;
}

const CategoryList: React.FC<CategoryListProps> = memo(({
  categories,
  selectedCategoryId,
  onCategorySelect,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Memoiza o handler para evitar re-criação em cada render
  const handleCategoryClick = useCallback((categoryId: string) => {
    onCategorySelect(categoryId);
  }, [onCategorySelect]);

  return (
    <List sx={{
      width: '100%',
      p: 0,
      '& .MuiListItemButton-root': {
        borderRadius: 0,
        py: isMobile ? 1 : 1.5,
        px: isMobile ? 1 : 2,
        minHeight: isMobile ? '44px' : '48px',
        '&.Mui-selected': {
          bgcolor: theme.palette.primary.main,
          '&:hover': {
            bgcolor: theme.palette.primary.dark,
          },
        },
        '&:hover': {
          bgcolor: theme.palette.action.hover,
        },
      },
    }}>
      {categories.map((category) => (
        <ListItem key={category.id} disablePadding>
          <Tooltip
            title={category.name}
            placement="right"
            disableHoverListener={!isMobile}
            arrow
          >
            <ListItemButton
              selected={selectedCategoryId === category.id}
              onClick={() => handleCategoryClick(category.id)}
              sx={{
                width: '100%'
              }}
            >
              <ListItemText
                primary={category.name}
                primaryTypographyProps={{
                  noWrap: !isMobile,
                  sx: {
                    fontWeight: selectedCategoryId === category.id ? 'bold' : 'normal',
                    color: selectedCategoryId === category.id ? 'common.white' : 'text.primary',
                    fontSize: isMobile ? '0.85rem' : '0.9rem',
                    lineHeight: isMobile ? 1.2 : 1.4,
                    wordBreak: isMobile ? 'break-word' : 'normal',
                    whiteSpace: isMobile ? 'normal' : 'nowrap',
                    overflow: isMobile ? 'visible' : 'hidden',
                    textOverflow: isMobile ? 'unset' : 'ellipsis'
                  }
                }}
              />
            </ListItemButton>
          </Tooltip>
        </ListItem>
      ))}
    </List>
  );
});

// Adiciona displayName para debugging
CategoryList.displayName = 'CategoryList';

export default CategoryList;