import React from 'react';
import { useTranslation } from 'react-i18next';

const Movies: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-white">
        {t('navigation:movies')}
      </h1>
      
      <div className="card p-6">
        <p className="text-secondary-300">
          Movies will be displayed here once you configure your IPTV connection.
        </p>
      </div>
    </div>
  );
};

export default Movies;