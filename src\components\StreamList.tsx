import React from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardMedia, Typography } from '@mui/material';

interface Stream {
  id: string;
  name: string;
  url: string;
  thumbnail?: string;
}

interface StreamListProps {
  streams: Stream[];
}

const StreamList: React.FC<StreamListProps> = ({ streams }) => {
  return (
    <Grid container spacing={2}>
      {streams.map((stream) => (
        <Grid item xs={12} sm={6} md={4} key={stream.id}>
          <Card sx={{ height: '100%' }}>
            {stream.thumbnail && (
              <CardMedia
                component="img"
                height="140"
                image={stream.thumbnail}
                alt={stream.name}
              />
            )}
            <CardContent>
              <Typography variant="h6" component="h3" noWrap>
                {stream.name}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default StreamList; 