import React from 'react';
import { Box, CircularProgress, Typography, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';

interface EnhancedLoadingProps {
  message?: string;
  size?: number;
  showMessage?: boolean;
}

const MotionBox = motion.create(Box);

const EnhancedLoading: React.FC<EnhancedLoadingProps> = ({
  message = "Carregando...",
  size = 40,
  showMessage = true
}) => {
  const theme = useTheme();

  return (
    <MotionBox
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        p: 3
      }}
    >
      {/* Container do loading com efeito de brilho */}
      <Box
        sx={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {/* Efeito de brilho de fundo */}
        <Box
          sx={{
            position: 'absolute',
            width: size + 20,
            height: size + 20,
            borderRadius: '50%',
            background: `radial-gradient(circle, 
              ${alpha(theme.palette.primary.main, 0.3)} 0%, 
              transparent 70%)`,
            animation: 'pulse 2s ease-in-out infinite'
          }}
        />
        
        {/* Círculo de progresso principal */}
        <CircularProgress
          size={size}
          thickness={4}
          sx={{
            color: theme.palette.primary.main,
            filter: `drop-shadow(0 0 10px ${alpha(theme.palette.primary.main, 0.5)})`,
            position: 'relative',
            zIndex: 1
          }}
        />
        
        {/* Círculo de progresso secundário (efeito de camadas) */}
        <CircularProgress
          size={size - 8}
          thickness={2}
          variant="determinate"
          value={25}
          sx={{
            color: alpha(theme.palette.secondary.main, 0.3),
            position: 'absolute',
            animation: 'rotate 3s linear infinite reverse'
          }}
        />
      </Box>

      {/* Mensagem de loading */}
      {showMessage && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              fontWeight: 500,
              textAlign: 'center',
              background: `linear-gradient(45deg, 
                ${theme.palette.text.primary}, 
                ${theme.palette.primary.main})`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundSize: '200% 200%',
              animation: 'shimmer 2s ease-in-out infinite'
            }}
          >
            {message}
          </Typography>
        </motion.div>
      )}

      {/* Pontos decorativos */}
      {[...Array(3)].map((_, i) => (
        <Box
          key={i}
          sx={{
            position: 'absolute',
            width: 4,
            height: 4,
            borderRadius: '50%',
            backgroundColor: theme.palette.primary.main,
            opacity: 0.6,
            animation: `float 2s ease-in-out infinite ${i * 0.3}s`,
            left: `${30 + i * 20}%`,
            top: `${60 + Math.sin(i) * 10}%`
          }}
        />
      ))}

      <style>
        {`
          @keyframes pulse {
            0%, 100% {
              transform: scale(1);
              opacity: 0.3;
            }
            50% {
              transform: scale(1.1);
              opacity: 0.6;
            }
          }
          
          @keyframes rotate {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
          
          @keyframes shimmer {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }
          
          @keyframes float {
            0%, 100% {
              transform: translateY(0px);
              opacity: 0.6;
            }
            50% {
              transform: translateY(-10px);
              opacity: 1;
            }
          }
        `}
      </style>
    </MotionBox>
  );
};

export default EnhancedLoading;