import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  alpha,
  useTheme
} from '@mui/material';
import {
  CloudSync as CloudSyncIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Link as LinkIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Save as SaveIcon,
  RestoreFromTrash as RestoreIcon
} from '@mui/icons-material';
import { messageTickerService } from '../services/messageTickerService';
import { useMessageTicker } from '../hooks/useMessageTicker';
import type { TickerConfig, MessageConfig } from '../components/MessageTicker';

interface TickerConfigPanelProps {
  open: boolean;
  onClose: () => void;
}

const TickerConfigPanel: React.FC<TickerConfigPanelProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const [configUrl, setConfigUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [previewConfig, setPreviewConfig] = useState<TickerConfig | null>(null);
  
  const { config, refresh, resetToDefault, cacheInfo } = useMessageTicker({
    autoLoad: false
  });

  useEffect(() => {
    if (open) {
      // Carrega URL atual
      const currentUrl = messageTickerService.getConfigUrl();
      setConfigUrl(currentUrl);
      setError(null);
      setSuccess(null);
    }
  }, [open]);

  const handleTestConnection = async () => {
    // Não precisamos mais deste método, pois removemos a interface de URL remota
    // Mantemos apenas para compatibilidade
    setError(null);
    setSuccess('✅ Usando configuração local.');
  };

  const handleSaveConfig = async () => {
    if (!configUrl.trim()) {
      setError('Por favor, insira uma URL de configuração');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Salva a URL de configuração
      messageTickerService.setConfigUrl(configUrl);
      
      // Atualiza a configuração
      await refresh();
      
      setSuccess('✅ Configuração salva e aplicada com sucesso!');
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Erro ao salvar configuração:', error);
      setError(`❌ Erro ao salvar configuração: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleResetToDefault = () => {
    resetToDefault();
    setConfigUrl(messageTickerService.getConfigUrl());
    setSuccess('✅ Configuração resetada para o padrão!');
    setPreviewConfig(null);
  };

  const handleRefresh = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Atualiza a configuração
      await refresh();
      setSuccess('✅ Configuração atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar configuração:', error);
      setError(`❌ Erro ao atualizar: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(18, 18, 18, 0.95), rgba(28, 28, 28, 0.95))'
            : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95))',
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 2,
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <CloudSyncIcon color="primary" />
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Configuração do Ticker - Remoto
        </Typography>
        <Chip
          icon={<SettingsIcon />}
          label={cacheInfo.isValid ? 'Cache Válido' : 'Cache Expirado'}
          color={cacheInfo.isValid ? 'success' : 'warning'}
          size="small"
        />
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Status do Cache */}
          <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.05) }}>
            <Typography variant="subtitle2" gutterBottom>
              📊 Status da Configuração
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                size="small"
                label={`Cache: ${cacheInfo.hasCache ? 'Ativo' : 'Vazio'}`}
                color={cacheInfo.hasCache ? 'success' : 'default'}
              />
              <Chip
                size="small"
                label={`Última atualização: ${cacheInfo.lastFetch?.toLocaleString() || 'Nunca'}`}
                color="info"
              />
              <Chip
                size="small"
                label={`Mensagens: ${config?.messages?.length || 0}`}
                color="primary"
              />
            </Box>
          </Paper>

          {/* Configuração Local */}
          <Box>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SettingsIcon color="primary" />
              Configuração Local
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={messageTickerService.getUseLocalOnly()}
                  onChange={(e) => messageTickerService.setUseLocalOnly(e.target.checked)}
                  color="primary"
                />
              }
              label="Usar apenas configuração local"
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Quando ativado, o sistema usará apenas a configuração local do ticker, ignorando qualquer fonte remota.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
              >
                Atualizar
              </Button>
            </Box>
          </Box>

          {/* Alertas */}
          {error && (
            <Alert severity="error" onClose={() => setError(null)}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" onClose={() => setSuccess(null)}>
              {success}
            </Alert>
          )}

          {/* Preview da Configuração */}
          {previewConfig && (
            <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.05) }}>
              <Typography variant="subtitle2" gutterBottom>
                🔍 Preview da Configuração
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip
                  size="small"
                  label={`Habilitado: ${previewConfig.enabled ? 'Sim' : 'Não'}`}
                  color={previewConfig.enabled ? 'success' : 'default'}
                />
                <Chip
                  size="small"
                  label={`${previewConfig.messages.length} mensagens`}
                  color="primary"
                />
              </Box>
              <List dense>
                {previewConfig.messages.slice(0, 3).map((msg, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={`${msg.text}${msg.link?.text || ''}${msg.suffix || ''}`}
                      secondary={`Duração: ${msg.duration || previewConfig.defaultDuration}s`}
                    />
                  </ListItem>
                ))}
                {previewConfig.messages.length > 3 && (
                  <ListItem>
                    <ListItemText
                      primary={`... e mais ${previewConfig.messages.length - 3} mensagens`}
                      sx={{ fontStyle: 'italic', color: 'text.secondary' }}
                    />
                  </ListItem>
                )}
              </List>
            </Paper>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
        <Button
          startIcon={<RestoreIcon />}
          onClick={handleResetToDefault}
          color="warning"
        >
          Resetar Padrão
        </Button>
        <Box sx={{ flexGrow: 1 }} />
        <Button onClick={onClose}>
          Cancelar
        </Button>
        <Button
          variant="contained"
          startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
          onClick={handleSaveConfig}
          disabled={loading}
        >
          Salvar e Aplicar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TickerConfigPanel;
