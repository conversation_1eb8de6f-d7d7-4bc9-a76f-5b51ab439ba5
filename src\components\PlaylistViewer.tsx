import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { playlistService, Playlist } from '../services/playlistService';
import { createIPTVService } from '../services/iptvService';
import { useInView } from 'react-intersection-observer';
import './PlaylistViewer.css';
import { dbService } from '../services/dbService';

interface M3UItem {
  title: string;
  url: string;
  type: 'channel' | 'movie' | 'series';
  group: string;
  logo?: string;
}

interface PlaylistViewerProps {
  filter?: string;
  groupFilter?: string;
  typeFilter?: 'channel' | 'movie' | 'series';
}

const ITEMS_PER_PAGE = 12;

type LoadingMethod = 'url' | 'file' | 'saved';

const PlaylistViewer: React.FC<PlaylistViewerProps> = ({ filter, groupFilter, typeFilter }) => {
  const [playlist, setPlaylist] = useState<M3UItem[]>([]);
  const [savedPlaylists, setSavedPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentChunk, setCurrentChunk] = useState(0);
  const [totalChunks, setTotalChunks] = useState(0);
  const [items, setItems] = useState<any[]>([]);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<LoadingMethod>('url');

  const { ref, inView } = useInView({
    threshold: 0.5,
  });

  useEffect(() => {
    loadSavedPlaylists();
  }, []);

  const loadSavedPlaylists = async () => {
    try {
      const playlists = await playlistService.getPlaylists();
      setSavedPlaylists(playlists);
    } catch (err) {
      console.error('Error loading saved playlists:', err);
      setError('Failed to load saved playlists');
    }
  };

  const loadChunk = useCallback(async (chunkIndex: number) => {
    try {
      setLoading(true);
      const iptvService = createIPTVService();
      const chunk = await iptvService.getPlaylistChunk(chunkIndex);
      
      // Apply filters
      const filteredChunk = chunk.filter((item: any) => {
        const matchesFilter = !filter || 
          item.title.toLowerCase().includes(filter.toLowerCase());
        const matchesGroup = !groupFilter || 
          item.group.toLowerCase() === groupFilter.toLowerCase();
        const matchesType = !typeFilter || 
          item.type === typeFilter;
        
        return matchesFilter && matchesGroup && matchesType;
      });

      setItems(prev => [...prev, ...filteredChunk]);
      setCurrentChunk(chunkIndex);
    } catch (error) {
      console.error('Error loading chunk:', error);
      setError('Failed to load content');
    } finally {
      setLoading(false);
    }
  }, [filter, groupFilter, typeFilter]);

  // Initialize playlist info
  useEffect(() => {
    const initPlaylist = async () => {
      try {
        const iptvService = createIPTVService();
        const info = await iptvService.getPlaylistInfo();
        if (info) {
          setTotalChunks(info.totalChunks);
          // Reset items when filters change
          setItems([]);
          setCurrentChunk(0);
          // Load first chunk
          await loadChunk(0);
        }
      } catch (error) {
        console.error('Error initializing playlist:', error);
        setError('Failed to initialize playlist');
      }
    };

    initPlaylist();
  }, [filter, groupFilter, typeFilter, loadChunk]);

  // Load more items when scrolling
  useEffect(() => {
    if (inView && !loading && currentChunk < totalChunks - 1) {
      loadChunk(currentChunk + 1);
    }
  }, [inView, loading, currentChunk, totalChunks, loadChunk]);

  const loadPlaylist = async (url: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch playlist: ${response.status} ${response.statusText}`);
      }
      const text = await response.text();
      
      // Validação básica do formato M3U
      if (!text.trim().startsWith('#EXTM3U')) {
        throw new Error('Invalid M3U file format: Missing #EXTM3U header');
      }

      const items = parseM3U(text);
      
      // Validação adicional do conteúdo
      if (items.length === 0) {
        throw new Error('No valid channels found in the playlist');
      }

      setPlaylist(items);

      // Save to IndexedDB
      await dbService.setPlaylistInfo({
        totalItems: items.length,
        totalChunks: Math.ceil(items.length / ITEMS_PER_PAGE),
        chunkSize: ITEMS_PER_PAGE,
        lastUpdated: Date.now()
      });

      // Save chunks
      const chunks = [];
      for (let i = 0; i < items.length; i += ITEMS_PER_PAGE) {
        const chunk = items.slice(i, i + ITEMS_PER_PAGE);
        chunks.push(chunk);
        await dbService.setPlaylistChunk(Math.floor(i / ITEMS_PER_PAGE), chunk);
      }

      await loadSavedPlaylists(); // Reload saved playlists
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load playlist');
      console.error('Error loading playlist:', err);
    } finally {
      setLoading(false);
    }
  };

  const parseM3U = (content: string): M3UItem[] => {
    const lines = content.split('\n');
    const items: M3UItem[] = [];
    let currentItem: Partial<M3UItem> | null = null;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('#EXTINF:')) {
        try {
          const info = trimmedLine.split(',')[1];
          const groupMatch = trimmedLine.match(/group-title="([^"]+)"/);
          const logoMatch = trimmedLine.match(/tvg-logo="([^"]+)"/);
          
          currentItem = {
            title: info?.trim() || 'Unknown',
            group: groupMatch?.[1] || 'Uncategorized',
            logo: logoMatch?.[1],
            type: 'channel' // Default type, will be refined based on group
          };
        } catch (error) {
          console.warn('Error parsing EXTINF line:', error);
          currentItem = null;
        }
      } else if (trimmedLine && !trimmedLine.startsWith('#') && currentItem) {
        try {
          // Validar URL
          new URL(trimmedLine);
          
          currentItem.url = trimmedLine;
          
          // Determine type based on group
          const group = currentItem.group?.toLowerCase() || '';
          if (group.includes('movie')) {
            currentItem.type = 'movie';
          } else if (group.includes('series')) {
            currentItem.type = 'series';
          }
          
          items.push(currentItem as M3UItem);
        } catch (error) {
          console.warn('Invalid URL in playlist:', trimmedLine);
        }
        currentItem = null;
      }
    }

    return items;
  };

  const handlePlaylistUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const content = e.target?.result as string;
          
          // Validação básica do formato M3U
          if (!content.trim().startsWith('#EXTM3U')) {
            throw new Error('Invalid M3U file format: Missing #EXTM3U header');
          }

          const items = parseM3U(content);
          
          // Validação adicional do conteúdo
          if (items.length === 0) {
            throw new Error('No valid channels found in the playlist');
          }

          setPlaylist(items);

          // Save to IndexedDB
          await dbService.setPlaylistInfo({
            totalItems: items.length,
            totalChunks: Math.ceil(items.length / ITEMS_PER_PAGE),
            chunkSize: ITEMS_PER_PAGE,
            lastUpdated: Date.now()
          });

          // Save chunks
          const chunks = [];
          for (let i = 0; i < items.length; i += ITEMS_PER_PAGE) {
            const chunk = items.slice(i, i + ITEMS_PER_PAGE);
            chunks.push(chunk);
            await dbService.setPlaylistChunk(Math.floor(i / ITEMS_PER_PAGE), chunk);
          }

          await loadSavedPlaylists(); // Reload saved playlists
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to parse playlist');
          console.error('Error processing playlist:', error);
        }
      };
      reader.onerror = () => {
        setError('Error reading file');
      };
      reader.readAsText(file);
    }
  };

  const handleUrlSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const url = new FormData(event.currentTarget).get('url') as string;
    if (url) {
      loadPlaylist(url);
    }
  };

  const handleDeletePlaylist = async (id: string) => {
    try {
      await playlistService.deletePlaylist(id);
      await loadSavedPlaylists();
    } catch (err) {
      console.error('Error deleting playlist:', err);
      setError('Failed to delete playlist');
    }
  };

  const handleLoadSavedPlaylist = (playlist: Playlist) => {
    setPlaylist(playlist.items);
  };

  const handleItemClick = (item: M3UItem) => {
    navigate(`/player/${encodeURIComponent(item.url)}`, { state: { item } });
  };

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="playlist-viewer">
      <div className="tabs">
        <button
          className={`tab-button ${activeTab === 'url' ? 'active' : ''}`}
          onClick={() => setActiveTab('url')}
        >
          URL Playlist
        </button>
        <button
          className={`tab-button ${activeTab === 'file' ? 'active' : ''}`}
          onClick={() => setActiveTab('file')}
        >
          Local File
        </button>
        <button
          className={`tab-button ${activeTab === 'saved' ? 'active' : ''}`}
          onClick={() => setActiveTab('saved')}
        >
          Saved Playlists
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'url' && (
          <div className="url-tab">
            <form onSubmit={handleUrlSubmit} className="url-form">
              <input
                type="url"
                name="url"
                placeholder="Enter M3U playlist URL"
                className="url-input"
              />
              <button type="submit" className="load-button">Load</button>
            </form>
          </div>
        )}

        {activeTab === 'file' && (
          <div className="file-tab">
            <div className="file-upload">
              <input
                type="file"
                accept=".m3u,.m3u8"
                onChange={handlePlaylistUpload}
                id="file-upload"
              />
              <label htmlFor="file-upload" className="file-upload-label">
                <svg className="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M4 16l4 4h10l4-4M4 16v-8a2 2 0 012-2h12a2 2 0 012 2v8M12 12v6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Drop your M3U file here or click to browse</span>
              </label>
            </div>
          </div>
        )}

        {activeTab === 'saved' && (
          <div className="saved-tab">
            {savedPlaylists.length > 0 ? (
              <div className="saved-playlists">
                <div className="saved-playlists-grid">
                  {savedPlaylists.map((savedPlaylist) => (
                    <div key={savedPlaylist.id} className="saved-playlist-item">
                      <h3>{savedPlaylist.name}</h3>
                      <p>Last updated: {new Date(savedPlaylist.lastUpdated).toLocaleDateString()}</p>
                      <div className="saved-playlist-actions">
                        <button
                          onClick={() => handleLoadSavedPlaylist(savedPlaylist)}
                          className="load-saved-button"
                        >
                          Load
                        </button>
                        <button
                          onClick={() => savedPlaylist.id && handleDeletePlaylist(savedPlaylist.id)}
                          className="delete-button"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="no-saved-playlists">
                <p>No saved playlists found. Add a playlist using URL or File upload.</p>
              </div>
            )}
        </div>
        )}
      </div>

      {(items.length > 0 || loading) && (
        <>
          <div className="filters">
        <input
          type="text"
              placeholder="Search..."
              value={filter}
              onChange={(e) => {
                // Implement search term change handling
              }}
          className="search-input"
        />

          <select
              value={groupFilter}
              onChange={(e) => {
                // Implement group filter change handling
              }}
              className="filter-select"
          >
            <option value="">All Groups</option>
              {/* Add group filter options here */}
            </select>

            <select
              value={typeFilter}
              onChange={(e) => {
                // Implement type filter change handling
              }}
              className="filter-select"
            >
              <option value="">All Types</option>
              <option value="channel">Channels</option>
              <option value="movie">Movies</option>
              <option value="series">Series</option>
          </select>
      </div>

          <div className="items-grid">
            {items.map((item, index) => (
              <div
                key={`${item.title}-${index}`}
                className="item-card"
                onClick={() => handleItemClick(item as M3UItem)}
              >
                <div className="item-image">
                  {item.logo ? (
                    <img src={item.logo} alt={item.title} />
                  ) : (
                    <div className="placeholder-image">
                      {item.type === 'channel' ? '📺' : item.type === 'movie' ? '🎬' : '📺'}
                    </div>
              )}
            </div>
                <div className="item-info">
                  <h3>{item.title}</h3>
                  <span className="item-group">{item.group}</span>
            </div>
          </div>
        ))}
      </div>

          {loading && (
            <div className="loading-indicator">
              <div className="loading-spinner" />
              <span>Loading more content...</span>
        </div>
          )}
          
          <div ref={ref} style={{ height: '20px' }} />
        </>
      )}

      <style>{`
        .playlist-viewer {
          padding: 1rem;
        }

        .tabs {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 1.5rem;
          border-bottom: 1px solid #e2e8f0;
          padding-bottom: 0.5rem;
        }

        .tab-button {
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem 0.5rem 0 0;
          font-weight: 500;
          color: #64748b;
          transition: all 150ms ease;
          border: 1px solid transparent;
          background: transparent;
        }

        .tab-button:hover {
          color: #2563eb;
        }

        .tab-button.active {
          color: #2563eb;
          border-bottom: 2px solid #2563eb;
        }

        .tab-content {
          background: white;
          border-radius: 0.5rem;
          padding: 1.5rem;
          margin-bottom: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .url-tab, .file-tab, .saved-tab {
          min-height: 150px;
        }

        .url-form {
          display: flex;
          gap: 0.5rem;
        }

        .url-input {
          flex: 1;
          padding: 0.75rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          font-size: 0.875rem;
        }

        .load-button {
          padding: 0.75rem 1.5rem;
          background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
          color: white;
          border-radius: 0.5rem;
          font-weight: 500;
        }

        .file-upload {
          border: 2px dashed #e2e8f0;
          border-radius: 0.5rem;
          padding: 2rem;
          text-align: center;
          transition: all 150ms ease;
        }

        .file-upload:hover {
          border-color: #2563eb;
          background: rgba(37, 99, 235, 0.05);
        }

        .file-upload input[type="file"] {
          display: none;
        }

        .file-upload-label {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          cursor: pointer;
          color: #64748b;
        }

        .upload-icon {
          width: 48px;
          height: 48px;
          stroke: #2563eb;
        }

        .no-saved-playlists {
          text-align: center;
          color: #64748b;
          padding: 2rem;
        }

        .filters {
          display: flex;
          gap: 0.5rem;
        }

        .search-input {
          flex: 1;
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.25rem;
        }

        .filter-select {
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.25rem;
        }

        .items-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 1rem;
        }

        .item-card {
          background: white;
          border-radius: 0.5rem;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: transform 150ms ease;
        }

        .item-card:hover {
          transform: translateY(-2px);
        }

        .item-image {
          aspect-ratio: 16/9;
          background: #f1f5f9;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .item-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .placeholder-image {
          font-size: 2rem;
        }

        .item-info {
          padding: 1rem;
        }

        .item-info h3 {
          margin: 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1e293b;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .item-group {
          font-size: 0.75rem;
          color: #64748b;
        }

        .loading-indicator {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          padding: 2rem;
          color: #64748b;
        }

        .error-message {
          padding: 1rem;
          background: #fef2f2;
          color: #ef4444;
          border-radius: 0.5rem;
          text-align: center;
          margin: 1rem;
        }
      `}</style>
    </div>
  );
};

export default PlaylistViewer;
