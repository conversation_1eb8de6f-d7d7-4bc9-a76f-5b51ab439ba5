import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Box, Paper, CircularProgress, Typography, Grid, useTheme, useMediaQuery, Alert, IconButton, Chip, Modal, Fade, Button, alpha, Tooltip, Snackbar, Tabs, Tab, Dialog, DialogTitle, DialogContent, DialogActions, Divider, keyframes } from '@mui/material';
import CategoryList from '../components/CategoryList';
import ChannelList from '../components/ChannelList';
import LoadMoreButton from '../components/LoadMoreButton';
import VideoPlayer from '../components/VideoPlayer/VideoPlayer';
import EPGGuide from '../components/VideoPlayer/EPGGuide';
import { Category, Stream, EPGChannel, createIPTVService } from '../services/iptvService';
import SearchBar from '../components/SearchBar';
import { Searchable } from '../services/searchService';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import RefreshIcon from '@mui/icons-material/Refresh';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import PetsIcon from '@mui/icons-material/Pets'; // Ícone de pata de gato
import { useFavorites } from '../contexts/FavoritesContext';
import LoadingOverlay from '../components/LoadingOverlay';

// Animações temáticas de gato
const pawAnimation = keyframes`
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  75% { transform: scale(1.1) rotate(5deg); }
`;

const purringGlow = keyframes`
  0%, 100% { box-shadow: 0 0 5px rgba(14, 165, 233, 0.3); }
  50% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.6), 0 0 30px rgba(56, 189, 248, 0.4); }
`;

const catEyesBlink = keyframes`
  0%, 90%, 100% { opacity: 1; }
  95% { opacity: 0.3; }
`;

const whiskerTwitch = keyframes`
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(2px); }
`;

// Animação de patas flutuantes no fundo
const floatingPaws = keyframes`
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.03;
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
    opacity: 0.08;
  }
`;

// Animação de ondas suaves
const gentleWave = keyframes`
  0%, 100% {
    transform: translateX(0px) translateY(0px);
    opacity: 0.02;
  }
  33% {
    transform: translateX(30px) translateY(-10px);
    opacity: 0.05;
  }
  66% {
    transform: translateX(-20px) translateY(10px);
    opacity: 0.03;
  }
`;

// Constantes de paginação
const CHANNELS_PER_PAGE = 20; // Número de canais por página
const INITIAL_LOAD = 15; // Carregamento inicial menor

// Interface para compatibilizar os tipos do EPGGuide
interface Program {
  title: string;
  startTime: string;
  endTime: string;
  description?: string;
}

const ChannelPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [channels, setChannels] = useState<Stream[]>([]);
  const [allChannelsInCategory, setAllChannelsInCategory] = useState<Stream[]>([]);
  const [displayedChannels, setDisplayedChannels] = useState<Stream[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreChannels, setHasMoreChannels] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<Stream | null>(null);
  const [epgData, setEpgData] = useState<EPGChannel[] | null>(null);
  const [channelEpg, setChannelEpg] = useState<EPGChannel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingEpg, setLoadingEpg] = useState(true); // Set to true por default
  const isLoadingRef = useRef(false); // Ref para controlar loading
  const [epgError, setEpgError] = useState<boolean>(false);
  const [epgLoadingText, setEpgLoadingText] = useState<string>('Carregando guia de programação...');
  
  // Novos estados para a pesquisa
  const [searchTerm, setSearchTerm] = useState('');
  const [globalSearch, setGlobalSearch] = useState(false);
  const [searchResults, setSearchResults] = useState<Stream[]>([]);
  const [searching, setSearching] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [allChannels, setAllChannels] = useState<Stream[]>([]);
  
  // Adicionar estados para o botão de atualização de EPG
  const [refreshingEpg, setRefreshingEpg] = useState(false);
  const [epgRefreshSuccess, setEpgRefreshSuccess] = useState(false);
  
  // Adicionar estados para favoritos
  const [activeTab, setActiveTab] = useState<'categories' | 'favorites'>('categories');
  const [visibleFavorites, setVisibleFavorites] = useState<number>(20); // Número inicial de favoritos visíveis
  
  // Estado para controlar expansão do EPG (recolhido por padrão no mobile)
  const [epgExpanded, setEpgExpanded] = useState<boolean>(false);
  
  // Estado para programa selecionado
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  
  // Estado para forçar layout landscape (para testes)
  const [forceLandscape, setForceLandscape] = useState<boolean>(false);

  
  const { favorites } = useFavorites();
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'lg'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  
  // Detectar orientação landscape em mobile - mais amplo
  const isMobileLandscape = useMediaQuery('(max-height: 600px) and (orientation: landscape)');
  const isPortrait = useMediaQuery('(orientation: portrait)');

  // Define loadEPGData first
  const loadEPGData = useCallback(async () => {
    try {
      setLoadingEpg(true);
      setEpgLoadingText('Carregando guia de programação...');
      
      // Desabilitar o cache temporariamente para forçar uma nova busca
      localStorage.removeItem('epg_last_loaded');
      
      const iptvService = await createIPTVService();
      
      // Passos do carregamento com intervalos para feedback visual
      setEpgLoadingText('Conectando ao servidor de EPG...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setEpgLoadingText('Baixando informações de programação...');
      const epgData = await iptvService.getEPG();
      
      setEpgLoadingText('Processando dados do guia...');
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Salvar timestamp para controle
      localStorage.setItem('epg_last_loaded', Date.now().toString());
      
      setEpgData(epgData);
      console.log(`✅ EPG carregado com sucesso: ${epgData.length} canais com informações de programação`);
    } catch (error) {
      console.error('Error loading EPG data:', error);
      setError('Failed to load program guide');
      setEpgError(true);
    } finally {
      setLoadingEpg(false);
    }
  }, []);

  // Adicionar função para forçar a atualização do EPG
  const handleForceEpgRefresh = async () => {
    try {
      setRefreshingEpg(true);
      
      // Criar o serviço IPTV
      const iptvService = await createIPTVService();
      
      // Limpar o cache do EPG
      await iptvService.refreshCache('epg');
      
      // Recarregar os dados do EPG
      const freshEpgData = await iptvService.getEPG();
      setEpgData(freshEpgData);
      
      // Exibir mensagem de sucesso
      setEpgRefreshSuccess(true);
      
      // Se um canal estiver selecionado, atualizar o EPG do canal
      if (selectedChannel) {
        findChannelEpg(selectedChannel.name);
      }
    } catch (error) {
      console.error('Error forcing EPG refresh:', error);
      setError('Falha ao atualizar o guia de programação');
    } finally {
      setRefreshingEpg(false);
    }
  };

  // Fechar notificação de sucesso
  const handleCloseSuccessMessage = () => {
    setEpgRefreshSuccess(false);
  };

  // Load initial data function with useCallback
  const loadInitialData = useCallback(async () => {
    try {
      setLoading(true);
      const iptvService = await createIPTVService();
      const categoriesData = await iptvService.getLiveCategories();
      setCategories(categoriesData);
      if (categoriesData.length > 0) {
        setSelectedCategoryId(categoriesData[0].id);
      }
      
      // Load EPG data when component mounts
      loadEPGData();
      
      // Carregar todos os canais para a pesquisa global
      if (categoriesData.length > 0) {
        loadAllChannels(iptvService, categoriesData);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      setError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  }, [loadEPGData]);

  // Função para carregar todos os canais para pesquisa global
  const loadAllChannels = async (iptvService: any, categoriesData: Category[]) => {
    try {
      const allChannelsPromises = categoriesData.map(category => 
        iptvService.getLiveStreams(category.id)
      );
      
      const results = await Promise.all(allChannelsPromises);
      const combinedChannels: Stream[] = [];
      
      results.forEach(channelsInCategory => {
        combinedChannels.push(...channelsInCategory);
      });
      
      setAllChannels(combinedChannels);
    } catch (error) {
      console.error('Error loading all channels:', error);
    }
  };

  // Load categories on mount
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const loadChannels = useCallback(async (reset = true) => {
    if (!selectedCategoryId) return;

    // Evita múltiplas chamadas simultâneas usando ref
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      const iptvService = await createIPTVService();
      const channelsData = await iptvService.getLiveStreams(selectedCategoryId);

      // Armazena todos os canais da categoria
      setAllChannelsInCategory(channelsData);

      if (reset) {
        // Reset: carrega apenas os primeiros canais
        const initialChannels = channelsData.slice(0, INITIAL_LOAD);
        setChannels(initialChannels);
        setDisplayedChannels(initialChannels);
        setCurrentPage(1);
        setHasMoreChannels(channelsData.length > INITIAL_LOAD);
      } else {
        // Carrega mais canais
        const startIndex = currentPage * CHANNELS_PER_PAGE;
        const endIndex = startIndex + CHANNELS_PER_PAGE;
        const newChannels = channelsData.slice(0, endIndex);

        setChannels(newChannels);
        setDisplayedChannels(newChannels);
        setHasMoreChannels(channelsData.length > endIndex);
      }
    } catch (error) {
      console.error('Error loading channels:', error);
      setError('Failed to load channels');
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [selectedCategoryId, currentPage]);

  // Função para carregar mais canais
  const loadMoreChannels = useCallback(() => {
    if (!hasMoreChannels || loading || allChannelsInCategory.length === 0) return;

    const startIndex = channels.length;
    const endIndex = startIndex + CHANNELS_PER_PAGE;
    const newChannels = allChannelsInCategory.slice(startIndex, endIndex);

    if (newChannels.length > 0) {
      setChannels(prev => [...prev, ...newChannels]);
      setDisplayedChannels(prev => [...prev, ...newChannels]);
      setCurrentPage(prev => prev + 1);
      setHasMoreChannels(allChannelsInCategory.length > endIndex);
    }
  }, [hasMoreChannels, loading, channels.length, allChannelsInCategory]);

  // Load channels when category is selected (com debounce)
  useEffect(() => {
    if (selectedCategoryId) {
      // Debounce para evitar múltiplas chamadas rápidas
      const timeoutId = setTimeout(() => {
        loadChannels(true); // sempre reset quando categoria muda
      }, 100); // 100ms de delay

      return () => clearTimeout(timeoutId);
    }
  }, [selectedCategoryId]); // Remove loadChannels da dependência

  // Atualiza o EPG quando o canal é selecionado
  useEffect(() => {
    if (selectedChannel && epgData) {
      findChannelEpg(selectedChannel.name);
    } else {
      setChannelEpg(null);
    }
  }, [selectedChannel, epgData]);

  // Efeito para carregar o canal favorito selecionado anteriormente
  useEffect(() => {
    const loadLastSelectedChannel = async () => {
      const lastSelectedChannelId = localStorage.getItem('lastSelectedChannelId');
      if (lastSelectedChannelId && favorites.length > 0) {
        const savedChannel = favorites.find(channel => channel.id === lastSelectedChannelId);
        if (savedChannel) {
          setSelectedChannel(savedChannel);
        }
      }
    };

    if (favorites.length > 0) {
      loadLastSelectedChannel();
    }
  }, [favorites]);

  const handleCategorySelect = useCallback((categoryId: string) => {
    // Evita re-renderização desnecessária se a mesma categoria for selecionada
    if (categoryId === selectedCategoryId) return;

    // Limpa o estado de loading anterior e reseta paginação
    setLoading(false);
    setSelectedCategoryId(categoryId);
    setSelectedChannel(null);
    setError(null);
    setCurrentPage(1);
    setChannels([]);
    setDisplayedChannels([]);
    setAllChannelsInCategory([]);
    setHasMoreChannels(false);
  }, [selectedCategoryId]);

  const handleChannelSelect = useCallback((channel: Stream) => {
    // Performance optimization: only update if it's a different channel
    if (!selectedChannel || selectedChannel.id !== channel.id) {
      setSelectedChannel(channel);
      // Salvar o canal selecionado no localStorage
      localStorage.setItem('lastSelectedChannelId', channel.id);
    }
  }, [selectedChannel]);

  // Funções para normalizar nomes de canais e encontrar correspondências no EPG
  const normalizeChannelName = (name: string): string => {
    // Convert to lowercase
    let normalized = name.toLowerCase();
    
    // Remove quality indicators and special variations
    normalized = normalized
      .replace(/\[.*?\]/g, '') // Remove content in brackets
      .replace(/\(.*?\)/g, '') // Remove content in parentheses
      .replace(/\bfhd\b|\bhd\b|\bsd\b|\bh265\b|\bhevc\b|\b4k\b/gi, '') // Remove quality indicators as whole words
      .replace(/\+/g, 'plus') // Replace + with 'plus' to avoid confusion
      .replace(/\&/g, 'and') // Replace & with 'and'
      .replace(/\s+/g, '') // Remove all whitespace
      .replace(/[^a-z0-9]/g, ''); // Remove special characters
    
    // Keep at least 4 characters to avoid overly short strings
    return normalized.length < 4 ? name.toLowerCase().replace(/\s+/g, '') : normalized;
  };

  // Cache for normalized channel names
  const normalizedNameCache = useMemo(() => new Map<string, string>(), []);
  
  // Get normalized name with caching
  const getNormalizedName = useCallback((name: string) => {
    if (!normalizedNameCache.has(name)) {
      normalizedNameCache.set(name, normalizeChannelName(name));
    }
    return normalizedNameCache.get(name)!;
  }, []);

  const findChannelEpg = useCallback((channelName: string) => {
    if (!epgData || epgData.length === 0) return;
    
    const normalizedName = getNormalizedName(channelName);
    
    // Tentativa 1: Correspondência exata
    let match = epgData.find(ch => 
      getNormalizedName(ch.name) === normalizedName
    );

    // Tentativa 2: Correspondência parcial com regras mais rígidas
    if (!match) {
      match = epgData.find(ch => {
        const epgNormalized = getNormalizedName(ch.name);
        
        // Só considera match se um nome contém o outro completamente E compartilham pelo menos 70% de similaridade
        const includesOther = normalizedName.includes(epgNormalized) || epgNormalized.includes(normalizedName);
        
        if (!includesOther) return false;
        
        // Calcula o ratio de similaridade (Jaccard similarity nos conjuntos de caracteres)
        const set1 = new Set(normalizedName);
        const set2 = new Set(epgNormalized);
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        const similarity = intersection.size / union.size;
        return similarity >= 0.7; // Pelo menos 70% similar
      });
    }

    // Tentativa 3: somente como último recurso, palavras-chave com alta similaridade
    if (!match) {
      // Dividir o nome normalizado em palavras e procurar por canais que contenham essas palavras
      const keywords = normalizedName.split(/\s+/).filter(word => word.length > 3);
      if (keywords.length > 0) {
        for (const keyword of keywords) {
          const potentialMatches = epgData.filter(ch => {
            const epgNormalized = getNormalizedName(ch.name);
            return epgNormalized.includes(keyword);
          });
          
          // Se encontrou matches potenciais, escolhe o com maior similaridade
          if (potentialMatches.length > 0) {
            const bestMatch = potentialMatches.reduce((best, current) => {
              const bestNormalized = getNormalizedName(best.name);
              const currentNormalized = getNormalizedName(current.name);
              
              // Calcula similaridades
              const set1 = new Set(normalizedName);
              const setBest = new Set(bestNormalized);
              const setCurrent = new Set(currentNormalized);
              
              const intersectionBest = new Set([...set1].filter(x => setBest.has(x)));
              const unionBest = new Set([...set1, ...setBest]);
              const similarityBest = intersectionBest.size / unionBest.size;
              
              const intersectionCurrent = new Set([...set1].filter(x => setCurrent.has(x)));
              const unionCurrent = new Set([...set1, ...setCurrent]);
              const similarityCurrent = intersectionCurrent.size / unionCurrent.size;
              
              return similarityCurrent > similarityBest ? current : best;
            }, potentialMatches[0]);
            
            // Verifica se o melhor match tem similaridade suficiente
            const bestNormalized = getNormalizedName(bestMatch.name);
            const set1 = new Set(normalizedName);
            const set2 = new Set(bestNormalized);
            const intersection = new Set([...set1].filter(x => set2.has(x)));
            const union = new Set([...set1, ...set2]);
            const similarity = intersection.size / union.size;
            
            // Só considera match se tiver pelo menos 60% de similaridade
            if (similarity >= 0.6) {
              match = bestMatch;
              break;
            }
          }
        }
      }
    }

    if (match) {
      console.log(`✅ EPG encontrado para o canal: ${channelName} → ${match.name}`);
      setChannelEpg(match);
    } else {
      console.log(`⚠️ EPG não encontrado para o canal: ${channelName}`);
      setChannelEpg(null);
    }
  }, [epgData, getNormalizedName]);

  // Função para renderizar o EPG do canal selecionado
  const renderEpgGuide = () => {
    if (loadingEpg) {
      return (
        <Box sx={{ 
          p: 3, 
          textAlign: 'center',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          minHeight: 200
        }}>
          <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
            <CircularProgress 
              size={50} 
              thickness={4} 
              sx={{ 
                color: theme.palette.primary.main,
                animation: 'pulse 1.5s ease-in-out infinite',
                '@keyframes pulse': {
                  '0%': { opacity: 0.6 },
                  '50%': { opacity: 1 },
                  '100%': { opacity: 0.6 }
                }
              }} 
            />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <LiveTvIcon sx={{ fontSize: 22, color: 'white' }} />
            </Box>
          </Box>
          <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1 }}>
            {epgLoadingText}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Isso pode levar alguns instantes dependendo do seu provedor IPTV
          </Typography>
        </Box>
      );
    }
    
    if (epgError) {
      return (
        <Alert 
          severity="warning" 
          sx={{ 
            m: 2, 
            borderRadius: 2,
            alignItems: 'center',
          }}
          action={
            <Button 
              color="warning"
              size="small"
              startIcon={<RefreshIcon />}
              onClick={handleForceEpgRefresh}
              disabled={refreshingEpg}
            >
              Tentar novamente
            </Button>
          }
        >
          Não foi possível carregar o guia de programação
        </Alert>
      );
    }
    
    if (!channelEpg || channelEpg.programs.length === 0) {
      return (
        <Alert severity="info" sx={{ my: 2 }}>
          Nenhuma programação disponível para este canal.
        </Alert>
      );
    }
    
    return (
      <EPGGuide 
        epgData={{
          programs: channelEpg.programs.map(prog => ({
            title: prog.title,
            startTime: prog.startTime,
            endTime: prog.endTime,
            description: prog.description
          })),
          name: channelEpg.name,
          id: channelEpg.id
        }}
        onRefreshEpg={handleForceEpgRefresh}
      />
    );
  };

  // Handle key presses for diagnostics
  useEffect(() => {
    const handleKeyDown = async (e: KeyboardEvent) => {
      // Alt+D to diagnose EPG matching for the selected channel
      if (e.altKey && e.key === 'd' && selectedChannel) {
        e.preventDefault();
        
        try {
          const iptvService = await createIPTVService();
          console.log('🔍 EPG Diagnostic for:', selectedChannel.name);
          
          const diagnosticResult = await iptvService.diagnoseEPGMatching(selectedChannel.name);
          console.log('📊 EPG Matching Diagnostic Results:', diagnosticResult);
          
          // Log a formatted table of matches for easier reading
          console.log('Possible EPG matches (sorted by similarity):');
          console.table(diagnosticResult.possibleMatches.map((match: {
            epgName: string;
            normalizedEpgName: string;
            similarity: number;
            wouldMatch: boolean;
          }) => ({
            'EPG Channel': match.epgName,
            'Normalized EPG': match.normalizedEpgName,
            'Similarity': (match.similarity * 100).toFixed(2) + '%',
            'Would Match': match.wouldMatch ? '✅' : '❌'
          })));
          
          alert(`EPG Diagnostic results for "${selectedChannel.name}" logged to console. Press F12 to view details.`);
        } catch (error) {
          console.error('Error running EPG diagnostic:', error);
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedChannel]);

  // Manipulador de pesquisa
  const handleSearch = useCallback((results: Searchable[], query: string) => {
    setSearchTerm(query);
    setSearchResults(results as Stream[]);
    
    if (query && results.length > 0) {
      setSearchModalOpen(true);
    } else if (query && results.length === 0) {
      // Se não houver resultados mas a busca foi realizada, também mostre o modal
      setSearchModalOpen(true);
    } else {
      setSearchModalOpen(false);
    }
  }, []);

  // Manipular busca global
  const handleGlobalSearchToggle = (enabled: boolean) => {
    setGlobalSearch(enabled);
  };
  
  // Manipular seleção de resultado de busca
  const handleSelectSearchResult = (channelId: string) => {
    setSearchModalOpen(false);
    const channelToSelect = channels.find(c => c.id === channelId) || allChannels.find(c => c.id === channelId);
    if (channelToSelect) {
      handleChannelSelect(channelToSelect);
    }
  };
  
  // Fechar modal de busca
  const closeSearchModal = () => {
    setSearchModalOpen(false);
  };

  // Handler para mudança de tab
  const handleTabChange = (event: React.SyntheticEvent, newValue: 'categories' | 'favorites') => {
    setActiveTab(newValue);
    // Resetar seleção ao mudar de tab
    setSelectedChannel(null);
  };
  
  // Função para carregar mais favoritos
  const handleLoadMoreFavorites = () => {
    setVisibleFavorites(prev => Math.min(prev + 20, favorites.length));
  };



  if (error) {
    return (
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 2,
          p: 4
        }}
      >
        <Box sx={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <PetsIcon sx={{
            fontSize: 64,
            color: 'error.main',
            animation: `${catEyesBlink} 2s infinite`
          }} />
        </Box>
        <Typography
          variant="h6"
          sx={{
            color: 'error.main',
            textAlign: 'center',
            fontWeight: 600
          }}
        >
          😾 Neko encontrou um problema!
        </Typography>
        <Typography
          color="text.secondary"
          sx={{
            textAlign: 'center',
            maxWidth: 400
          }}
        >
          {error}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => window.location.reload()}
          sx={{
            mt: 2,
            borderColor: 'primary.main',
            color: 'primary.main',
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              animation: `${whiskerTwitch} 0.5s ease-in-out`
            }
          }}
        >
          🐾 Tentar novamente
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{
      height: isMobile ? '100vh' : 'calc(100vh - 120px)',
      minHeight: isMobile ? '100vh' : 'auto',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      px: isMobile ? 1 : 2,
      pt: 1,
      pb: isMobile ? 1 : 2,
      gap: isMobile ? 1 : 2,
      position: 'relative'
    }}>
      {/* Plano de fundo temático de gato */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -1,
          overflow: 'hidden',
          pointerEvents: 'none',
          background: theme.palette.mode === 'dark'
            ? `radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.03) 0%, transparent 50%),
               radial-gradient(circle at 80% 20%, rgba(56, 189, 248, 0.02) 0%, transparent 50%),
               radial-gradient(circle at 40% 40%, rgba(14, 165, 233, 0.01) 0%, transparent 50%)`
            : `radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
               radial-gradient(circle at 80% 20%, rgba(56, 189, 248, 0.015) 0%, transparent 50%),
               radial-gradient(circle at 40% 40%, rgba(14, 165, 233, 0.008) 0%, transparent 50%)`,
        }}
      >
        {/* Patas flutuantes decorativas */}
        {[...Array(6)].map((_, i) => (
          <PetsIcon
            key={i}
            sx={{
              position: 'absolute',
              fontSize: { xs: 24, md: 32 },
              color: 'primary.main',
              opacity: 0.03,
              left: `${15 + i * 15}%`,
              top: `${20 + (i % 3) * 25}%`,
              animation: `${floatingPaws} ${4 + i * 0.5}s ease-in-out infinite`,
              animationDelay: `${i * 0.8}s`,
              transform: `rotate(${i * 30}deg)`,
            }}
          />
        ))}

        {/* Ondas suaves de fundo */}
        {[...Array(4)].map((_, i) => (
          <Box
            key={`wave-${i}`}
            sx={{
              position: 'absolute',
              width: { xs: 100, md: 150 },
              height: { xs: 100, md: 150 },
              borderRadius: '50%',
              background: theme.palette.mode === 'dark'
                ? `radial-gradient(circle, rgba(14, 165, 233, 0.02) 0%, transparent 70%)`
                : `radial-gradient(circle, rgba(14, 165, 233, 0.015) 0%, transparent 70%)`,
              left: `${10 + i * 25}%`,
              top: `${30 + (i % 2) * 40}%`,
              animation: `${gentleWave} ${6 + i * 0.7}s ease-in-out infinite`,
              animationDelay: `${i * 1.2}s`,
            }}
          />
        ))}

        {/* Padrão sutil de linhas */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: theme.palette.mode === 'dark'
              ? `repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 100px,
                  rgba(14, 165, 233, 0.005) 100px,
                  rgba(14, 165, 233, 0.005) 102px
                )`
              : `repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 120px,
                  rgba(14, 165, 233, 0.003) 120px,
                  rgba(14, 165, 233, 0.003) 122px
                )`,
          }}
        />
      </Box>
      {/* Barra de pesquisa no topo com tema de gato */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: isMobile ? 1 : 2,
          gap: 2,
          position: 'relative'
        }}
      >
        {/* Pata de gato decorativa */}
        <Box
          sx={{
            position: 'absolute',
            left: -10,
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 0,
            opacity: 0.1,
            animation: `${pawAnimation} 3s ease-in-out infinite`,
            display: { xs: 'none', md: 'block' }
          }}
        >
          <PetsIcon sx={{ fontSize: 40, color: 'primary.main' }} />
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flexGrow: 1,
            maxWidth: isMobile ? '100%' : 500,
            position: 'relative',
            zIndex: 1,
            '&:hover': {
              '& .search-container': {
                animation: `${purringGlow} 2s ease-in-out infinite`,
              }
            }
          }}
        >
          <Box className="search-container" sx={{ width: '100%' }}>
            <SearchBar
              items={globalSearch ? allChannels : channels}
              placeholder="🐱 Miau! Buscar canais..."
              onSearch={handleSearch}
              onSelectItem={(channel) => handleSelectSearchResult(channel.id)}
              fullWidth
              globalSearchToggle
              isGlobalSearch={globalSearch}
              onGlobalSearchChange={handleGlobalSearchToggle}
            />
          </Box>
        </Box>

        {/* Indicador de status com tema de gato */}
        {channels.length > 0 && (
          <Chip
            icon={<PetsIcon sx={{ animation: `${catEyesBlink} 4s infinite` }} />}
            label={`${channels.length} canais`}
            variant="outlined"
            size="small"
            sx={{
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                animation: `${whiskerTwitch} 0.5s ease-in-out`
              }
            }}
          />
        )}
      </Box>
      
            {/* Botão de teste para layout landscape */}
      <Box sx={{ 
        position: 'fixed', 
        top: 10, 
        right: 10, 
        zIndex: 9999,
        display: { xs: 'block', md: 'none' }
      }}>
        <Button 
          variant="contained" 
          size="small"
          onClick={() => setForceLandscape(!forceLandscape)}
          sx={{ fontSize: '0.7rem' }}
        >
          {forceLandscape ? 'Normal' : 'Landscape'}
        </Button>
      </Box>

      {/* Layout Mobile - Landscape SIMPLIFICADO */}
      {(isMobileLandscape || forceLandscape) ? (
        <Box sx={{ 
          display: 'flex', 
          height: '100vh',
          width: '100vw',
          position: 'fixed',
          top: 0,
          left: 0,
          bgcolor: 'background.default',
          zIndex: 1000
        }}>
          {/* Player grande - 70% da tela */}
          <Box sx={{ 
            width: '70%', 
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Header compacto */}
            <Box sx={{ 
              p: 0.5,
              bgcolor: 'primary.main',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              minHeight: '40px'
            }}>
              <Typography variant="caption" sx={{ fontSize: '0.8rem', fontWeight: 'bold' }}>
                📺 {selectedChannel?.name || 'Neko TV'}
              </Typography>
              <IconButton 
                size="small" 
                sx={{ color: 'white' }}
                onClick={() => window.location.reload()}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
            
            {/* Player */}
            <Box sx={{ 
              flex: 1,
              bgcolor: 'black',
              width: '100%'
            }}>
              {selectedChannel ? (
                <VideoPlayer
                  url={selectedChannel.url || ''}
                  title={selectedChannel.name}
                  style={{ width: '100%', height: '100%' }}
                  autoPlay={true}
                />
              ) : (
                <Box sx={{ 
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white'
                }}>
                  <Typography>Selecione um canal →</Typography>
                </Box>
              )}
            </Box>
          </Box>

          {/* Lista lateral simples - 30% */}
          <Box sx={{ 
            width: '30%',
            height: '100%',
            bgcolor: 'background.paper',
            borderLeft: 1,
            borderColor: 'divider',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Categorias - metade superior */}
            <Box sx={{ 
              height: '50%',
              borderBottom: 1,
              borderColor: 'divider'
            }}>
              <Box sx={{ 
                p: 0.5,
                bgcolor: 'primary.main',
                color: 'white',
                textAlign: 'center'
              }}>
                <Typography variant="caption" sx={{ fontSize: '0.7rem', fontWeight: 'bold' }}>
                  CATEGORIAS
                </Typography>
              </Box>
              <Box sx={{ 
                height: 'calc(100% - 32px)',
                overflow: 'auto'
              }}>
                {categories.map((category) => (
                  <Box
                    key={category.id}
                    onClick={() => setSelectedCategoryId(category.id)}
                    sx={{
                      p: 0.5,
                      cursor: 'pointer',
                      bgcolor: selectedCategoryId === category.id ? 'primary.main' : 'transparent',
                      color: selectedCategoryId === category.id ? 'white' : 'text.primary',
                      fontSize: '0.7rem',
                      borderBottom: '1px solid',
                      borderColor: 'divider',
                      '&:hover': {
                        bgcolor: selectedCategoryId === category.id ? 'primary.dark' : 'action.hover'
                      }
                    }}
                  >
                    {category.name}
                  </Box>
                ))}
              </Box>
            </Box>

            {/* Canais - metade inferior */}
            <Box sx={{ height: '50%' }}>
              <Box sx={{ 
                p: 0.5,
                bgcolor: 'secondary.main',
                color: 'white',
                textAlign: 'center'
              }}>
                <Typography variant="caption" sx={{ fontSize: '0.7rem', fontWeight: 'bold' }}>
                  CANAIS
                </Typography>
              </Box>
              <Box sx={{ 
                height: 'calc(100% - 32px)',
                overflow: 'auto'
              }}>
                {channels.map((channel) => (
                  <Box
                    key={channel.id}
                    onClick={() => handleChannelSelect(channel)}
                    sx={{
                      p: 0.5,
                      cursor: 'pointer',
                      bgcolor: selectedChannel?.id === channel.id ? 'secondary.main' : 'transparent',
                      color: selectedChannel?.id === channel.id ? 'white' : 'text.primary',
                      fontSize: '0.7rem',
                      borderBottom: '1px solid',
                      borderColor: 'divider',
                      '&:hover': {
                        bgcolor: selectedChannel?.id === channel.id ? 'secondary.dark' : 'action.hover'
                      }
                    }}
                  >
                    {channel.name}
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>
      ) : 
      /* Layout Mobile Portrait Otimizado */
      isMobile ? (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          gap: 0.5, // Reduzido o gap entre elementos
          flex: 1,
          overflow: 'hidden',
          height: 'calc(100vh - 80px)' // Maximizado para dar o máximo de espaço às listas no mobile
        }}>
          {/* Player de vídeo no topo - mais proeminente */}
          {selectedChannel ? (
            <Paper
              elevation={3}
              sx={{
                width: '100%',
                borderRadius: 2,
                overflow: 'hidden',
                bgcolor: 'background.paper',
                mb: 1,
                flex: '0 0 auto', // Impede que o player expanda além do necessário
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                '&:hover': {
                  boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                }
              }}
            >
              <Box sx={{
                p: 1, // Reduzido de 1.5 para 1
                borderBottom: 1,
                borderColor: 'divider',
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.03)'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Typography component="span" sx={{ fontSize: '1rem' }}>📺</Typography>
                  <Typography variant="h6" component="div" sx={{
                    fontWeight: 'bold',
                    fontSize: '1rem',
                    color: 'primary.main'
                  }}>
                    {selectedChannel.name}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{
                width: '100%',
                bgcolor: 'black',
                height: { xs: '28vh', md: '70vh' }, // Aumentado no mobile para mostrar controles, maior no PC
                minHeight: { xs: '200px', md: '500px' }, // Altura mínima adequada para controles
                maxHeight: { xs: '250px', md: '900px' } // Altura máxima maior para PC
              }}>
                <VideoPlayer
                  url={selectedChannel.url || ''}
                  title={selectedChannel.name}
                  style={{
                    width: '100%',
                    height: '100%'
                  }}
                  autoPlay={true}
                />
              </Box>
            </Paper>
          ) : null}

          {/* EPG Guide - Logo após o player */}
          {selectedChannel && (
            <Paper
              elevation={2}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                height: 'auto',
                mb: 1,
                transition: 'all 0.2s ease',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.08)}`,
                '&:hover': {
                  boxShadow: `0 3px 15px ${alpha(theme.palette.primary.main, 0.12)}`,
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
                }
              }}
            >
              <Box 
                data-epg-header
                onClick={() => setEpgExpanded(!epgExpanded)}
                sx={{
                  p: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  bgcolor: alpha(theme.palette.success.main, 0.05),
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.success.main, 0.08)
                  }
                }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Typography component="span" sx={{ fontSize: '0.9rem' }}>📅</Typography>
                    <Typography variant="subtitle1" component="div" sx={{
                      fontWeight: 'bold',
                      fontSize: '0.9rem',
                      color: 'success.main'
                    }}>
                      Programação
                    </Typography>
                  </Box>
                  {epgExpanded && (
                    <Typography variant="caption" component="div" sx={{ color: 'text.secondary', fontSize: '0.7rem' }}>
                      (clique em um programa para detalhes)
                    </Typography>
                  )}
                  {epgExpanded && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleForceEpgRefresh();
                      }}
                      disabled={refreshingEpg}
                      sx={{ color: 'success.main' }}
                      title="Atualizar programação"
                    >
                      <RefreshIcon fontSize="small" />
                    </IconButton>
                  )}
                </Box>
                <IconButton
                  size="small"
                  sx={{ 
                    color: 'success.main',
                    transform: epgExpanded ? 'rotate(180deg)' : 'none',
                    transition: 'transform 0.2s ease'
                  }}
                  title={epgExpanded ? 'Recolher' : 'Expandir'}
                >
                  <ExpandMoreIcon fontSize="small" />
                </IconButton>
              </Box>
              {epgExpanded && (
                <Box 
                  onClick={(e) => e.stopPropagation()}
                  sx={{
                    overflow: 'auto',
                    maxHeight: '25vh', // Ainda mais reduzido para liberar espaço às listas
                    p: 1,
                    WebkitOverflowScrolling: 'touch',
                    overscrollBehavior: 'contain',
                    '&::-webkit-scrollbar': {
                      width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      background: 'rgba(0,0,0,0.1)',
                      borderRadius: '3px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: 'rgba(0,0,0,0.3)',
                      borderRadius: '3px',
                      '&:hover': {
                        background: 'rgba(0,0,0,0.5)',
                      }
                    }
                  }}
                >
                  {renderEpgGuide()}
                </Box>
              )}
            </Paper>
          )}

          {/* Navegação por abas */}
          <Box sx={{ 
            flex: 1,
            minHeight: 0, // Importante para permitir que o container encolha
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{ 
                borderBottom: 1, 
                borderColor: 'divider',
                '& .MuiTab-root': {
                  minWidth: 'unset',
                    py: 1.5,
                    fontSize: '0.9rem',
                    fontWeight: 600
                }
              }}
            >
              <Tab 
                label="Categorias" 
                value="categories" 
                icon={<LiveTvIcon fontSize="small" />} 
                iconPosition="start"
              />
              <Tab 
                  label={`Favoritos (${favorites.length})`}
                value="favorites" 
                icon={<FavoriteIcon fontSize="small" />} 
                iconPosition="start"
              />
            </Tabs>
            
            {/* Conteúdo das abas */}
            <Box sx={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
          {activeTab === 'categories' ? (
              <Box sx={{ 
                display: 'flex',
                  height: '100%',
                  flex: 1,
                  overflow: 'hidden'
              }}>
                  {/* Categorias - Layout vertical otimizado */}
                  <Box sx={{ 
                    width: selectedCategoryId && channels.length > 0 ? '40%' : '100%', // Reduzido de 45% para 40%
                    height: '100%',
                    borderRight: selectedCategoryId && channels.length > 0 ? 1 : 0,
                    borderColor: 'divider',
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'hidden',
                    transition: 'width 0.3s ease'
                  }}>
                    <Box sx={{
                      p: 0.5, // Reduzido para economizar espaço
                        borderBottom: 1,
                        borderColor: 'divider',
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      position: 'sticky',
                      top: 0,
                      zIndex: 1
                    }}>
                      <Typography variant="subtitle2" sx={{
                        fontWeight: 'bold',
                        fontSize: '0.8rem',
                        color: 'primary.main',
                        textAlign: 'center'
                      }}>
                        Categorias ({categories.length})
                    </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      overflow: 'auto',
                      WebkitOverflowScrolling: 'touch', // Smooth scrolling no iOS
                      overscrollBehavior: 'contain', // Previne scroll bounce
                      '&::-webkit-scrollbar': {
                        width: '4px',
                      },
                      '&::-webkit-scrollbar-track': {
                        background: 'rgba(0,0,0,0.1)',
                      },
                      '&::-webkit-scrollbar-thumb': {
                        background: 'rgba(0,0,0,0.3)',
                        borderRadius: '2px',
                        '&:hover': {
                          background: 'rgba(0,0,0,0.5)',
                        }
                      }
                    }}>
                    {loading && categories.length === 0 ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                          <CircularProgress size={20} />
                      </Box>
                    ) : (
                      <CategoryList
                        categories={categories}
                        selectedCategoryId={selectedCategoryId}
                        onCategorySelect={handleCategorySelect}
                      />
                    )}
                  </Box>
                  </Box>

                  {/* Canais - Aparece quando categoria selecionada */}
                  {selectedCategoryId && (
                  <Box sx={{ 
                      width: '60%', // Aumentado de 55% para 60%
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'hidden'
                  }}>
                    <Box sx={{ 
                        p: 0.5, // Reduzido para economizar espaço
                      borderBottom: 1,
                      borderColor: 'divider',
                        bgcolor: alpha(theme.palette.success.main, 0.05),
                        position: 'sticky',
                        top: 0,
                        zIndex: 1
                      }}>
                        <Typography variant="subtitle2" noWrap sx={{
                          fontWeight: 'bold',
                          fontSize: '0.8rem',
                          color: 'success.main',
                          textAlign: 'center'
                        }}>
                          {categories.find(c => c.id === selectedCategoryId)?.name || 'Canais'} ({channels.length})
                      </Typography>
                    </Box>
                    <Box sx={{ 
                      flex: 1,
                      overflow: 'auto',
                        WebkitOverflowScrolling: 'touch', // Smooth scrolling no iOS
                        overscrollBehavior: 'contain', // Previne scroll bounce
                        '&::-webkit-scrollbar': {
                          width: '4px',
                        },
                        '&::-webkit-scrollbar-track': {
                          background: 'rgba(0,0,0,0.1)',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          background: 'rgba(0,0,0,0.3)',
                          borderRadius: '2px',
                          '&:hover': {
                            background: 'rgba(0,0,0,0.5)',
                          }
                        }
                      }}>
                        {loading && channels.length === 0 ? (
                          <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            py: 3
                          }}>
                            <Box sx={{
                              position: 'relative',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <CircularProgress
                                size={20}
                                sx={{
                                  color: 'primary.main',
                                  animation: `${purringGlow} 2s ease-in-out infinite`
                                }}
                              />
                              <PetsIcon
                                sx={{
                                  position: 'absolute',
                                  fontSize: 12,
                                  color: 'primary.main',
                                  animation: `${catEyesBlink} 2s infinite`
                                }}
                              />
                            </Box>
                            <Typography variant="caption" sx={{
                              mt: 1,
                              color: 'text.secondary',
                              animation: `${whiskerTwitch} 1s ease-in-out infinite`
                            }}>
                              🐱 Procurando canais...
                            </Typography>
                          </Box>
                      ) : channels.length === 0 ? (
                          <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            py: 3,
                            px: 1
                          }}>
                            <LiveTvIcon sx={{ fontSize: 24, color: 'text.secondary', mb: 1 }} />
                            <Typography variant="caption" sx={{ color: 'text.secondary', textAlign: 'center' }}>
                              Nenhum canal
                        </Typography>
                          </Box>
                      ) : (
                          <>
                            <ChannelList
                              channels={searchTerm && !searchModalOpen ? searchResults : channels}
                              selectedChannelId={selectedChannel?.id}
                              onChannelSelect={handleChannelSelect}
                              epgData={epgData || undefined}
                            />

                            {/* Botão Carregar Mais - apenas para canais normais, não para busca */}
                            {!searchTerm && hasMoreChannels && allChannelsInCategory.length > 0 && (
                              <LoadMoreButton
                                onLoadMore={loadMoreChannels}
                                loading={loading}
                                hasMore={hasMoreChannels}
                                totalLoaded={channels.length}
                                totalAvailable={allChannelsInCategory.length}
                              />
                            )}
                          </>
                        )}
                      </Box>
                        </Box>
                      )}
                    </Box>
              ) : (
                /* Favoritos */
                <Box sx={{
                  height: '100%',
                  overflow: 'auto',
                  WebkitOverflowScrolling: 'touch', // Smooth scrolling no iOS
                  overscrollBehavior: 'contain' // Previne scroll bounce
                }}>
                  <Box sx={{
                    p: 1,
                    borderBottom: 1,
                    borderColor: 'divider',
                    bgcolor: alpha(theme.palette.error.main, 0.05),
                    position: 'sticky',
                    top: 0,
                    zIndex: 1
                  }}>
                    <Typography variant="subtitle2" sx={{
                      fontWeight: 'bold',
                      fontSize: '0.8rem',
                      color: 'error.main',
                      textAlign: 'center'
                    }}>
                      Canais Favoritos ({favorites.length})
                    </Typography>
                  </Box>
                  {favorites.length === 0 ? (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      py: 4,
                      px: 2
                    }}>
                      <FavoriteIcon sx={{ fontSize: 32, color: 'text.secondary', mb: 1 }} />
                      <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center' }}>
                        Nenhum favorito ainda
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.secondary', textAlign: 'center', mt: 0.5 }}>
                        Toque no ❤️ ao lado dos canais para adicionar
                      </Typography>
                    </Box>
                  ) : (
                    <>
                      <ChannelList
                        channels={favorites.slice(0, visibleFavorites)}
                        selectedChannelId={selectedChannel?.id}
                        onChannelSelect={handleChannelSelect}
                        epgData={epgData || undefined}
                      />
                      {visibleFavorites < favorites.length && (
                        <Box sx={{ p: 1, textAlign: 'center' }}>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={handleLoadMoreFavorites}
                            sx={{ fontSize: '0.75rem' }}
                          >
                            Mais {favorites.length - visibleFavorites}
                          </Button>
                  </Box>
                      )}
                    </>
                )}
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      ) : (
        /* Layout Desktop/Tablet */
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'row',
            gap: 2,
            overflow: 'hidden'
          }}
        >
          {/* Área lateral com categorias e canais */}
          <Paper
            elevation={3}
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              borderRadius: '8px',
              width: isTablet ? '350px' : '300px',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              '&:hover': {
                boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              }
            }}
          >
            {/* Tabs para Categorias e Favoritos */}
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  minWidth: 'unset',
                  py: 1.5
                }
              }}
            >
              <Tab
                label="Categorias"
                value="categories"
                icon={<LiveTvIcon fontSize="small" />}
                iconPosition="start"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: activeTab === 'categories' ? 600 : 400,
                  textTransform: 'none'
                }}
              />
              <Tab
                label="Favoritos"
                value="favorites"
                icon={<FavoriteIcon fontSize="small" />}
                iconPosition="start"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: activeTab === 'favorites' ? 600 : 400,
                  textTransform: 'none'
                }}
              />
            </Tabs>

            {/* Conteúdo da Tab - Layout Desktop/Tablet */}
            {activeTab === 'categories' ? (
            <Box sx={{ 
              display: 'flex',
              flexDirection: isTablet ? 'column' : 'row',
              height: '100%',
              width: '100%',
              overflow: 'hidden'
            }}>
              {/* Área de categorias */}
              <Box sx={{ 
                width: isTablet ? '100%' : '220px',
                height: isTablet ? 'auto' : '100%',
                borderRight: isTablet ? 0 : 1,
                borderBottom: isTablet ? 1 : 0,
                borderColor: 'divider',
                overflow: 'auto',
                minWidth: '180px',
                maxWidth: isTablet ? '100%' : '220px'
              }}>
                <Typography 
                  variant="subtitle1" 
                  sx={{ 
                    p: 2, 
                    fontWeight: 'bold',
                    borderBottom: 1,
                    borderColor: 'divider',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                >
                  <span>Categorias</span>
                  <Chip 
                    label={categories.length.toString()} 
                    size="small" 
                    variant="outlined" 
                    color="primary" 
                    sx={{ fontSize: '0.7rem', height: '20px' }} 
                  />
                </Typography>
                {loading && categories.length === 0 ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                    <CircularProgress size={24} />
                  </Box>
                ) : (
                <CategoryList
                  categories={categories}
                  selectedCategoryId={selectedCategoryId}
                  onCategorySelect={handleCategorySelect}
                />
                )}
              </Box>
              
              {/* Canais */}
              <Box sx={{ 
                flex: 1,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
              }}>
                <Typography 
                  variant="subtitle1" 
                  sx={{ 
                    p: 2, 
                    fontWeight: 'bold',
                    borderBottom: 1,
                    borderColor: 'divider'
                  }}
                >
                  Canais {selectedCategoryId && categories.find(c => c.id === selectedCategoryId)?.name ? `- ${categories.find(c => c.id === selectedCategoryId)?.name}` : ''}
                </Typography>
                <Box sx={{ 
                  flex: 1,
                  overflow: 'auto',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  justifyContent: loading && channels.length === 0 ? 'center' : 'flex-start',
                  width: '100%'
                }}>
                  {loading && channels.length === 0 ? (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 2
                    }}>
                      <Box sx={{
                        position: 'relative',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <CircularProgress
                          size={28}
                          sx={{
                            color: 'primary.main',
                            animation: `${purringGlow} 2s ease-in-out infinite`
                          }}
                        />
                        <PetsIcon
                          sx={{
                            position: 'absolute',
                            fontSize: 16,
                            color: 'primary.main',
                            animation: `${catEyesBlink} 2s infinite`
                          }}
                        />
                      </Box>
                      <Typography variant="body2" sx={{
                        color: 'text.secondary',
                        textAlign: 'center',
                        animation: `${whiskerTwitch} 1s ease-in-out infinite`
                      }}>
                        🐾 Neko está caçando canais...
                      </Typography>
                    </Box>
                  ) : channels.length === 0 ? (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      py: 4,
                      px: 2
                    }}>
                      <PetsIcon sx={{
                        fontSize: 48,
                        color: 'text.disabled',
                        mb: 2,
                        animation: `${catEyesBlink} 3s infinite`
                      }} />
                      <Typography sx={{
                        color: 'text.secondary',
                        textAlign: 'center',
                        mb: 1
                      }}>
                        😿 Neko não encontrou canais aqui
                      </Typography>
                      <Typography variant="caption" sx={{
                        color: 'text.disabled',
                        textAlign: 'center'
                      }}>
                        Tente outra categoria ou verifique sua conexão
                      </Typography>
                    </Box>
                  ) : (
                    <Box sx={{ width: '100%' }}>
                      <ChannelList
                        channels={searchTerm && !searchModalOpen ? searchResults : channels}
                        selectedChannelId={selectedChannel?.id}
                        onChannelSelect={handleChannelSelect}
                        epgData={epgData || undefined}
                      />

                      {/* Botão Carregar Mais - apenas para canais normais, não para busca */}
                      {!searchTerm && hasMoreChannels && allChannelsInCategory.length > 0 && (
                        <LoadMoreButton
                          onLoadMore={loadMoreChannels}
                          loading={loading}
                          hasMore={hasMoreChannels}
                          totalLoaded={channels.length}
                          totalAvailable={allChannelsInCategory.length}
                        />
                      )}
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          ) : (
            /* Conteúdo da aba de Favoritos */
            <Box sx={{ 
              flex: 1,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  p: 2, 
                  fontWeight: 'bold',
                  borderBottom: 1,
                  borderColor: 'divider',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <span>Canais Favoritos</span>
                <Chip 
                  label={favorites.length.toString()} 
                  size="small" 
                  variant="outlined" 
                  color="primary" 
                  sx={{ fontSize: '0.7rem', height: '20px' }} 
                />
              </Typography>
              <Box sx={{ 
                flex: 1,
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'flex-start',
                width: '100%'
              }}>
                {favorites.length === 0 ? (
                  <Typography sx={{ my: 3, px: 2, color: 'text.secondary', textAlign: 'center' }}>
                    Você ainda não adicionou nenhum canal aos favoritos
                  </Typography>
                ) : (
                  <>
                    <Box sx={{ width: '100%' }}>
                      <ChannelList
                        channels={favorites.slice(0, visibleFavorites)}
                        selectedChannelId={selectedChannel?.id}
                        onChannelSelect={handleChannelSelect}
                        epgData={epgData || undefined}
                      />
                    </Box>
                    {visibleFavorites < favorites.length && (
                      <Button 
                        variant="contained" 
                        color="primary"
                        size="small"
                        sx={{ 
                          my: 2, 
                          width: 'auto',
                          minWidth: '160px',
                          maxWidth: '200px',
                          mx: 'auto',
                          textTransform: 'none',
                          borderRadius: '8px',
                          py: 0.75
                        }}
                        onClick={handleLoadMoreFavorites}
                      >
                        Mais {favorites.length - visibleFavorites}
                      </Button>
                    )}
                  </>
                )}
              </Box>
            </Box>
          )}
        </Paper>
        
          {/* Área principal do player - Desktop/Tablet */}
        <Box sx={{ 
            flex: 2,
          display: 'flex',
          flexDirection: 'column',
                          height: '100%'
          }}>
              <Box sx={{
                display: 'flex',
                flexDirection: 'row',
                width: '100%',
                height: '100%',
                gap: 2
              }}>
                {/* Player de vídeo centralizado */}
                <Paper 
                  elevation={3}
                  sx={{ 
                    flex: '1 1 60%',
                    display: selectedChannel ? 'flex' : 'none',
                    flexDirection: 'column',
                    overflow: 'hidden',
                    bgcolor: 'background.paper',
                    borderRadius: '8px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {selectedChannel && (
                    <Box sx={{ 
                      width: '100%', 
                      height: '100%', 
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      p: 0
                    }}>
                      <Box sx={{ 
                        p: 1.5, 
                        borderBottom: 1, 
                        borderColor: 'divider',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.03)'
                      }}>
                        <Typography variant="h6" component="h2" sx={{ 
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          {selectedChannel.name}
                        </Typography>
                      </Box>
                      <Box sx={{ 
                        flexGrow: 1,
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'black',
                        position: 'relative'
                      }}>
                        <VideoPlayer 
                          url={selectedChannel.url || ''} 
                          title={selectedChannel.name} 
                          style={{ 
                            width: '100%', 
                            height: '100%',
                            maxHeight: '100%',
                            minHeight: '600px' // Aumentado para melhor visualização no desktop
                          }}
                          autoPlay={true}
                        />
                      </Box>
                    </Box>
                  )}
                </Paper>
                
                {/* EPG Guide ao lado do player */}
                {selectedChannel && (
                  <Paper 
                    elevation={3} 
                    sx={{ 
                      flex: '1 1 40%',
                      display: 'flex',
                      flexDirection: 'column',
                      overflow: 'hidden',
                      bgcolor: 'background.paper',
                      borderRadius: '8px'
                    }}
                  >
                    <Box sx={{ 
                      p: 1.5, 
                      borderBottom: 1, 
                      borderColor: 'divider',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.03)',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <Typography variant="h6" component="h2" sx={{ 
                        fontWeight: 'bold',
                        fontSize: '1.1rem'
                      }}>
                        Guia de programação: {selectedChannel?.name}
                      </Typography>
                    </Box>
                    <Box sx={{ 
                      flexGrow: 1, 
                      overflowY: 'auto',
                      p: 2
                    }}>
                      {renderEpgGuide()}
                    </Box>
                  </Paper>
                )}
              </Box>
            </Box>
        </Box>
      )}

      {/* Modal de Resultados de Busca */}
      <Modal
        open={searchModalOpen}
        onClose={closeSearchModal}
        aria-labelledby="search-results-modal"
        sx={{
          zIndex: 100000
        }}
        BackdropProps={{
          style: {
            backdropFilter: 'blur(3px)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 99999
          }
        }}
      >
        <Fade in={searchModalOpen}>
          <Box sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '90%', sm: '80%', md: '70%' },
            maxWidth: 800,
            bgcolor: 'background.paper',
            borderRadius: 3,
            boxShadow: 24,
            display: 'flex',
            flexDirection: 'column',
            maxHeight: '80vh',
            overflow: 'hidden',
            zIndex: 100001
          }}>
            <Box sx={{ 
              p: 2, 
              display: 'flex', 
              justifyContent: 'space-between',
              alignItems: 'center',
              borderBottom: 1,
              borderColor: 'divider'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SearchIcon color="primary" />
                <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 500 }}>
                  {searching ? 'Buscando...' : searchResults.length > 0 
                    ? `Resultados para "${searchTerm}"` 
                    : 'Nenhum resultado encontrado'}
                </Typography>
                {searchResults.length > 0 && (
                  <Chip 
                    label={`${searchResults.length} encontrados`} 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                )}
              </Box>
              <IconButton onClick={closeSearchModal} size="small" sx={{ color: 'text.secondary' }}>
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
            
            <Box sx={{ 
              overflow: 'auto', 
              flex: 1,
              p: 2,
              '&::-webkit-scrollbar': {
                width: '8px',
                backgroundColor: alpha(theme.palette.common.white, 0.05),
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.3),
                }
              },
            }}>
              {searching ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4, height: '200px' }}>
                  <CircularProgress size={40} />
                </Box>
              ) : searchResults.length > 0 ? (
                <Grid container spacing={2}>
                  {searchResults.map((channel) => (
                    <Grid item xs={12} sm={6} md={4} key={channel.id}>
                      <Paper
                        elevation={0}
                        sx={{ 
                          p: 2,
                          borderRadius: 2,
                          transition: 'all 0.2s ease',
                          bgcolor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.05) : alpha(theme.palette.common.black, 0.02),
                          border: '1px solid',
                          borderColor: 'transparent',
                          cursor: 'pointer',
                          display: 'flex',
                          '&:hover': { 
                            bgcolor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.08) : alpha(theme.palette.common.black, 0.04),
                            borderColor: alpha(theme.palette.primary.main, 0.3),
                            transform: 'translateY(-3px)',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                          }
                        }}
                        onClick={() => handleSelectSearchResult(channel.id)}
                      >
                        <Box sx={{ mr: 2, width: 40, height: 40, borderRadius: '50%', overflow: 'hidden', bgcolor: 'action.hover' }}>
                          <Box
                            component="img"
                            src={channel.thumbnail || '/images/channel-placeholder.png'}
                            alt={channel.name}
                            onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                              e.currentTarget.src = '/images/channel-placeholder.png';
                            }}
                            sx={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                            {channel.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                            Canal
                          </Typography>
                        </Box>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ 
                  p: 6, 
                  textAlign: 'center', 
                  display: 'flex', 
                  flexDirection: 'column', 
                  justifyContent: 'center', 
                  alignItems: 'center' 
                }}>
                  <LiveTvIcon sx={{ fontSize: 50, color: 'text.disabled', mb: 2 }} />
                  <Typography sx={{ fontWeight: 500, mb: 1 }}>Nenhum resultado encontrado</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 400, mb: 3 }}>
                    {globalSearch 
                      ? 'Não encontramos canais correspondentes ao termo de busca.' 
                      : 'Tente outros termos de busca ou ative a pesquisa global para procurar em todas as categorias.'
                    }
                  </Typography>
                  <Button 
                    variant="outlined" 
                    size="medium" 
                    startIcon={<SearchIcon />}
                    onClick={() => setSearchTerm('')}
                  >
                    Nova busca
                  </Button>
                </Box>
              )}
            </Box>
          </Box>
        </Fade>
      </Modal>

      {/* Mensagem de sucesso */}
      <Snackbar
        open={epgRefreshSuccess}
        autoHideDuration={4000}
        onClose={handleCloseSuccessMessage}
        message="Guia de programação atualizado com sucesso!"
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
      
      {/* Overlay de carregamento do EPG */}
      {loadingEpg && (
        <LoadingOverlay 
          message={epgLoadingText} 
          submessage="Isso pode levar alguns instantes dependendo do seu provedor IPTV. Por favor, aguarde." 
        />
      )}
    </Box>
  );
};

export default ChannelPage; 
