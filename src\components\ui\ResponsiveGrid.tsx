import React from 'react';
import { Grid, Box, useTheme, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ResponsiveGridProps {
  children: React.ReactNode;
  spacing?: number;
  minItemWidth?: number;
  maxColumns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  aspectRatio?: string;
  className?: string;
}

// Styled grid container with responsive behavior
const StyledGridContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  padding: theme.spacing(1),
  
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(2),
  },
  
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(3),
  }
}));

// Auto-sizing grid with CSS Grid
const AutoGrid = styled(Box)<{ 
  minItemWidth: number; 
  spacing: number; 
  aspectRatio?: string;
}>(({ theme, minItemWidth, spacing, aspectRatio }) => ({
  display: 'grid',
  gridTemplateColumns: `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`,
  gap: theme.spacing(spacing),
  width: '100%',
  
  '& > *': {
    width: '100%',
    ...(aspectRatio && {
      aspectRatio: aspectRatio,
      '& img, & video': {
        width: '100%',
        height: '100%',
        objectFit: 'cover',
      }
    })
  },
  
  // Mobile optimizations
  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: `repeat(auto-fill, minmax(${Math.max(minItemWidth * 0.8, 120)}px, 1fr))`,
    gap: theme.spacing(Math.max(spacing - 0.5, 0.5)),
  },
  
  // Tablet optimizations
  [theme.breakpoints.between('sm', 'md')]: {
    gridTemplateColumns: `repeat(auto-fill, minmax(${minItemWidth * 0.9}px, 1fr))`,
  }
}));

// Traditional MUI Grid with responsive columns
const ResponsiveMUIGrid = styled(Grid)(({ theme }) => ({
  width: '100%',
  margin: 0,
  
  '& .MuiGrid-item': {
    paddingLeft: theme.spacing(1),
    paddingTop: theme.spacing(1),
    
    [theme.breakpoints.up('sm')]: {
      paddingLeft: theme.spacing(2),
      paddingTop: theme.spacing(2),
    }
  }
}));

/**
 * Componente de grid responsivo que se adapta automaticamente ao tamanho da tela
 * Suporta tanto CSS Grid quanto MUI Grid baseado nas props fornecidas
 */
const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  spacing = 2,
  minItemWidth = 200,
  maxColumns,
  aspectRatio,
  className
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  
  // Se maxColumns for especificado, usa MUI Grid
  if (maxColumns) {
    const getColumns = () => {
      if (isMobile) return maxColumns.xs || 2;
      if (isTablet) return maxColumns.sm || 3;
      return maxColumns.md || 4;
    };
    
    const columns = getColumns();
    const itemWidth = 12 / columns;
    
    return (
      <StyledGridContainer className={className}>
        <ResponsiveMUIGrid container spacing={spacing}>
          {React.Children.map(children, (child, index) => (
            <Grid item xs={itemWidth} key={index}>
              {child}
            </Grid>
          ))}
        </ResponsiveMUIGrid>
      </StyledGridContainer>
    );
  }
  
  // Caso contrário, usa CSS Grid auto-sizing
  return (
    <StyledGridContainer className={className}>
      <AutoGrid 
        minItemWidth={minItemWidth} 
        spacing={spacing}
        aspectRatio={aspectRatio}
      >
        {children}
      </AutoGrid>
    </StyledGridContainer>
  );
};

export default ResponsiveGrid;
