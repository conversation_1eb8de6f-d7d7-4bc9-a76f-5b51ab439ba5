import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import useIPTV from '../hooks/useIPTV';
import { FiTv, FiFilm, FiMonitor, FiHeart, FiSettings, FiLoader } from 'react-icons/fi';
import { motion } from 'framer-motion';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { 
    isConnected,
    liveCategories,
    movieCategories,
    seriesCategories,
    liveStreams,
    movieStreams,
    seriesStreams,
    isLoadingCategories,
    loadStreams
  } = useIPTV();

  // Don't auto-load streams, just show categories for now

  const stats = {
    liveChannels: liveStreams?.length || 0,
    movies: movieStreams?.length || 0,
    series: seriesStreams?.length || 0,
    favorites: 0, // TODO: Implement favorites count
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
      },
    }),
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">
          {t('navigation:dashboard')}
        </h1>
        
        {isLoadingCategories && (
          <div className="flex items-center text-slate-300">
            <FiLoader className="animate-spin mr-2" />
            Loading data...
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          custom={0}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="bg-slate-800 border border-slate-700 rounded-lg p-6 hover:border-sky-500/50 transition-all duration-200"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Live Channels</h3>
            <FiTv className="h-6 w-6 text-sky-400" />
          </div>
          <p className="text-3xl font-bold text-sky-400">{stats.liveChannels}</p>
          <p className="text-sm text-slate-400 mt-2">
            {liveCategories?.length || 0} categories
          </p>
        </motion.div>
        
        <motion.div
          custom={1}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="bg-slate-800 border border-slate-700 rounded-lg p-6 hover:border-blue-500/50 transition-all duration-200"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Movies</h3>
            <FiFilm className="h-6 w-6 text-blue-400" />
          </div>
          <p className="text-3xl font-bold text-blue-400">{stats.movies}</p>
          <p className="text-sm text-slate-400 mt-2">
            {movieCategories?.length || 0} categories
          </p>
        </motion.div>
        
        <motion.div
          custom={2}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="bg-slate-800 border border-slate-700 rounded-lg p-6 hover:border-yellow-500/50 transition-all duration-200"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Series</h3>
            <FiMonitor className="h-6 w-6 text-yellow-400" />
          </div>
          <p className="text-3xl font-bold text-yellow-400">{stats.series}</p>
          <p className="text-sm text-slate-400 mt-2">
            {seriesCategories?.length || 0} categories
          </p>
        </motion.div>
        
        <motion.div
          custom={3}
          initial="hidden"
          animate="visible"
          variants={cardVariants}
          className="bg-slate-800 border border-slate-700 rounded-lg p-6 hover:border-red-500/50 transition-all duration-200"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Favorites</h3>
            <FiHeart className="h-6 w-6 text-red-400" />
          </div>
          <p className="text-3xl font-bold text-red-400">{stats.favorites}</p>
          <p className="text-sm text-slate-400 mt-2">
            Saved items
          </p>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
        className="bg-slate-800 border border-slate-700 rounded-lg p-6"
      >
        <h2 className="text-xl font-semibold text-white mb-4">
          Welcome to IPTV FUCKING PLAYER PRO
        </h2>
        
        {isConnected ? (
          <div className="space-y-4">
            <div className="flex items-center text-green-400">
              <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
              <span className="font-medium">Connected to IPTV Server</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-slate-700/50 rounded-lg p-4">
                <h4 className="font-medium text-white mb-2">Quick Actions</h4>
                <div className="space-y-2">
                  <button 
                    onClick={() => window.location.hash = '/channels'}
                    className="w-full text-left text-sm text-slate-300 hover:text-white transition-colors"
                  >
                    → Browse Live Channels
                  </button>
                  <button 
                    onClick={() => window.location.hash = '/channels'}
                    className="w-full text-left text-sm text-slate-300 hover:text-white transition-colors"
                  >
                    → Explore Movies
                  </button>
                  <button 
                    onClick={() => window.location.hash = '/channels'}
                    className="w-full text-left text-sm text-slate-300 hover:text-white transition-colors"
                  >
                    → Watch Series
                  </button>
                </div>
              </div>
              
              <div className="bg-slate-700/50 rounded-lg p-4">
                <h4 className="font-medium text-white mb-2">Content Overview</h4>
                <div className="space-y-1 text-sm text-slate-300">
                  <div>Live: {stats.liveChannels} channels</div>
                  <div>Movies: {stats.movies} titles</div>
                  <div>Series: {stats.series} shows</div>
                </div>
              </div>
              
              <div className="bg-slate-700/50 rounded-lg p-4">
                <h4 className="font-medium text-white mb-2">Features</h4>
                <div className="space-y-1 text-sm text-slate-300">
                  <div>✓ EPG Support</div>
                  <div>✓ Multi-language</div>
                  <div>✓ Favorites</div>
                  <div>✓ Watch History</div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <FiSettings className="mx-auto h-12 w-12 text-slate-400 mb-4" />
            <p className="text-slate-300 mb-4">
              Configure your IPTV connection to get started.
            </p>
            <button 
              onClick={() => window.location.hash = '/settings'}
              className="px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
            >
              Go to Settings
            </button>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default Dashboard;