import { supabase } from '../integrations/supabase';
import { LicenseService } from '../services/licenseService';

/**
 * Testa a conexão com o Supabase
 */
export async function testSupabaseConnection(): Promise<boolean> {
  try {
    console.log('🔄 Testando conexão com Supabase...');
    
    // Teste 1: Verificar se o cliente está configurado
    if (!supabase) {
      console.error('❌ Cliente Supabase não está configurado');
      return false;
    }
    
    console.log('✅ Cliente Supabase configurado');
    
    // Teste 2: Testar conexão básica com o banco
    const { data, error } = await supabase
      .from('licenses')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Erro ao conectar com o banco:', error.message);
      return false;
    }
    
    console.log('✅ Conexão com banco de dados OK');
    
    // Teste 3: Testar função RPC
    try {
      const { data: rpcData, error: rpcError } = await supabase.rpc('generate_random_key');
      
      if (rpcError) {
        console.error('❌ Erro ao testar função RPC:', rpcError.message);
        return false;
      }
      
      console.log('✅ Funções RPC funcionando. Chave gerada:', rpcData);
    } catch (rpcErr) {
      console.warn('⚠️ Função RPC pode não estar disponível:', rpcErr);
    }
    
    // Teste 4: Testar serviço de licenças
    try {
      const licenses = await LicenseService.getAllLicenses();
      console.log(`✅ Serviço de licenças OK. ${licenses.length} licenças encontradas`);
    } catch (serviceErr) {
      console.error('❌ Erro no serviço de licenças:', serviceErr);
      return false;
    }
    
    console.log('🎉 Todos os testes passaram! Supabase está conectado e funcionando.');
    return true;
    
  } catch (error) {
    console.error('❌ Erro geral ao testar Supabase:', error);
    return false;
  }
}

/**
 * Testa a geração de uma licença
 */
export async function testLicenseGeneration(): Promise<boolean> {
  try {
    console.log('🔄 Testando geração de licença...');
    
    const result = await LicenseService.generateLicense(
      'Cliente Teste',
      'http://test-iptv.example.com'
    );
    
    if (!result) {
      console.error('❌ Falha ao gerar licença');
      return false;
    }
    
    console.log('✅ Licença gerada com sucesso:', result);
    
    // Testar verificação da licença
    const checkResult = await LicenseService.checkLicense(
      result.license_key,
      'TEST-DEVICE-123'
    );
    
    if (!checkResult.valid) {
      console.error('❌ Licença gerada não é válida:', checkResult.message);
      return false;
    }
    
    console.log('✅ Verificação de licença funcionando');
    
    return true;
    
  } catch (error) {
    console.error('❌ Erro ao testar geração de licença:', error);
    return false;
  }
}

/**
 * Executa todos os testes
 */
export async function runAllTests(): Promise<void> {
  console.log('🚀 Iniciando testes do Supabase...\n');
  
  const connectionTest = await testSupabaseConnection();
  
  if (connectionTest) {
    console.log('\n');
    await testLicenseGeneration();
  }
  
  console.log('\n🏁 Testes concluídos!');
}

/**
 * Testa o MCP do Supabase
 */
export async function testMCPConnection(): Promise<boolean> {
  try {
    console.log('🔄 Testando MCP do Supabase...');

    // Importar dinamicamente para evitar problemas de dependência
    const { testMCPConnection: mcpTest } = await import('../integrations/supabase/mcp-client');
    const { supabaseAdminService } = await import('../services/supabaseAdminService');

    // Teste 1: Conexão MCP básica
    const mcpConnected = await mcpTest();
    if (!mcpConnected) {
      console.error('❌ MCP não conectado');
      return false;
    }

    console.log('✅ MCP conectado');

    // Teste 2: Inicializar serviço admin
    const adminInitialized = await supabaseAdminService.initialize();
    if (!adminInitialized) {
      console.error('❌ Serviço admin não inicializado');
      return false;
    }

    console.log('✅ Serviço admin inicializado');

    // Teste 3: Health check
    const health = await supabaseAdminService.healthCheck();
    if (health.status !== 'healthy') {
      console.warn('⚠️ Health check com problemas:', health);
    } else {
      console.log('✅ Health check OK');
    }

    // Teste 4: Estatísticas
    const stats = await supabaseAdminService.getAdminStats();
    console.log('✅ Estatísticas obtidas:', stats);

    console.log('🎉 MCP do Supabase funcionando 100%!');
    return true;

  } catch (error) {
    console.error('❌ Erro no teste MCP:', error);
    return false;
  }
}

/**
 * Executa todos os testes incluindo MCP
 */
export async function runAllTestsWithMCP(): Promise<void> {
  console.log('🚀 Iniciando testes completos do Supabase + MCP...\n');

  // Testes básicos
  const basicTest = await testSupabaseConnection();

  if (basicTest) {
    console.log('\n');
    await testLicenseGeneration();

    console.log('\n');
    await testMCPConnection();
  }

  console.log('\n🏁 Todos os testes concluídos!');
}

// Para uso no console do navegador
if (typeof window !== 'undefined') {
  (window as any).testSupabase = {
    testConnection: testSupabaseConnection,
    testLicenseGeneration,
    testMCP: testMCPConnection,
    runAllTests,
    runAllTestsWithMCP
  };

  console.log('💡 Para testar o Supabase, execute no console:');
  console.log('testSupabase.runAllTests() - Testes básicos');
  console.log('testSupabase.runAllTestsWithMCP() - Testes completos + MCP');
  console.log('testSupabase.testMCP() - Apenas teste MCP');
}
