// Script de teste para verificar melhorias do ticker
// Execute no console do navegador para testar

console.log('🧪 Iniciando testes do MessageTicker...');

// Teste 1: Verificar se o ticker está visível
function testTickerVisibility() {
  const ticker = document.querySelector('[class*="TickerContainer"]');
  if (ticker) {
    console.log('✅ Ticker encontrado e visível');
    return true;
  } else {
    console.log('❌ Ticker não encontrado');
    return false;
  }
}

// Teste 2: Verificar se os links são clicáveis
function testLinkClickability() {
  const links = document.querySelectorAll('[class*="TickerContainer"] a');
  if (links.length > 0) {
    console.log(`✅ ${links.length} link(s) encontrado(s) no ticker`);
    
    links.forEach((link, index) => {
      const styles = window.getComputedStyle(link);
      const zIndex = styles.zIndex;
      const pointerEvents = styles.pointerEvents;
      const cursor = styles.cursor;
      
      console.log(`Link ${index + 1}:`, {
        zIndex,
        pointerEvents,
        cursor,
        href: link.href
      });
      
      if (zIndex >= 1000 && pointerEvents !== 'none' && cursor === 'pointer') {
        console.log(`✅ Link ${index + 1} está configurado corretamente para cliques`);
      } else {
        console.log(`⚠️ Link ${index + 1} pode ter problemas de clicabilidade`);
      }
    });
    
    return true;
  } else {
    console.log('❌ Nenhum link encontrado no ticker');
    return false;
  }
}

// Teste 3: Verificar performance da animação
function testAnimationPerformance() {
  const scrollingContent = document.querySelector('[class*="ScrollingContent"]');
  if (scrollingContent) {
    const styles = window.getComputedStyle(scrollingContent);
    const willChange = styles.willChange;
    const backfaceVisibility = styles.backfaceVisibility;
    
    console.log('Otimizações de animação:', {
      willChange,
      backfaceVisibility
    });
    
    if (willChange.includes('transform')) {
      console.log('✅ Animação otimizada com willChange');
    } else {
      console.log('⚠️ willChange não configurado corretamente');
    }
    
    return true;
  } else {
    console.log('❌ Conteúdo scrollável não encontrado');
    return false;
  }
}

// Teste 4: Verificar cache do ticker
function testTickerCache() {
  try {
    const cacheKey = 'neko_ticker_config';
    const timestampKey = 'neko_ticker_timestamp';
    
    const cachedConfig = localStorage.getItem(cacheKey);
    const cachedTimestamp = localStorage.getItem(timestampKey);
    
    if (cachedConfig && cachedTimestamp) {
      const timestamp = parseInt(cachedTimestamp);
      const age = Date.now() - timestamp;
      const ageMinutes = Math.floor(age / (1000 * 60));
      
      console.log('✅ Cache do ticker encontrado:', {
        age: `${ageMinutes} minutos`,
        isValid: age < (5 * 60 * 1000) // 5 minutos
      });
      
      return true;
    } else {
      console.log('⚠️ Cache do ticker não encontrado (primeira execução?)');
      return false;
    }
  } catch (error) {
    console.log('❌ Erro ao verificar cache:', error);
    return false;
  }
}

// Teste 5: Simular clique no link
function testLinkClick() {
  const links = document.querySelectorAll('[class*="TickerContainer"] a');
  if (links.length > 0) {
    const firstLink = links[0];
    console.log('🖱️ Simulando clique no primeiro link...');
    
    // Simula eventos de mouse
    const events = ['mousedown', 'mouseup', 'click'];
    events.forEach(eventType => {
      const event = new MouseEvent(eventType, {
        bubbles: true,
        cancelable: true,
        view: window
      });
      firstLink.dispatchEvent(event);
    });
    
    console.log('✅ Eventos de clique simulados');
    return true;
  } else {
    console.log('❌ Nenhum link disponível para teste');
    return false;
  }
}

// Executar todos os testes
async function runAllTests() {
  console.log('\n🚀 Executando bateria de testes...\n');
  
  const tests = [
    { name: 'Visibilidade do Ticker', fn: testTickerVisibility },
    { name: 'Clicabilidade dos Links', fn: testLinkClickability },
    { name: 'Performance da Animação', fn: testAnimationPerformance },
    { name: 'Cache do Ticker', fn: testTickerCache },
    { name: 'Simulação de Clique', fn: testLinkClick }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n🧪 Testando: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`❌ Erro no teste ${test.name}:`, error);
      results.push({ name: test.name, passed: false, error });
    }
  }
  
  // Resumo dos resultados
  console.log('\n📊 Resumo dos Testes:');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`   Erro: ${result.error.message}`);
    }
  });
  
  console.log('='.repeat(50));
  console.log(`📈 Resultado: ${passed}/${total} testes passaram (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('🎉 Todos os testes passaram! O ticker está funcionando perfeitamente.');
  } else {
    console.log('⚠️ Alguns testes falharam. Verifique os logs acima para mais detalhes.');
  }
}

// Executar testes automaticamente
runAllTests();

// Exportar funções para uso manual
window.tickerTests = {
  runAllTests,
  testTickerVisibility,
  testLinkClickability,
  testAnimationPerformance,
  testTickerCache,
  testLinkClick
};
