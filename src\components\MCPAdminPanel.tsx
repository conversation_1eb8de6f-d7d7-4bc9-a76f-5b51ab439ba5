import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel
} from '@mui/material';
import { useSupabaseMCP } from '../hooks/useSupabaseMCP';
import type { License } from '../integrations/supabase/types';

export const MCPAdminPanel: React.FC = () => {
  const mcp = useSupabaseMCP();
  const [licenses, setLicenses] = useState<License[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newLicense, setNewLicense] = useState({
    client_name: '',
    iptv_url: '',
    notes: '',
    active: true
  });

  // Carregar dados iniciais
  useEffect(() => {
    if (mcp.status.isInitialized) {
      loadAllData();
    }
  }, [mcp.status.isInitialized]);

  const loadAllData = async () => {
    setLoading(true);
    try {
      const [licensesData, statsData, usersData] = await Promise.all([
        mcp.getAllLicenses(),
        mcp.getStats(),
        mcp.getAllUsers()
      ]);
      
      setLicenses(licensesData);
      setStats(statsData);
      setUsers(usersData);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateLicense = async () => {
    try {
      const result = await mcp.createLicense(newLicense);
      if (result) {
        setLicenses(prev => [result, ...prev]);
        setCreateDialogOpen(false);
        setNewLicense({ client_name: '', iptv_url: '', notes: '', active: true });
        
        // Atualizar stats
        const newStats = await mcp.getStats();
        setStats(newStats);
      }
    } catch (error) {
      console.error('Erro ao criar licença:', error);
    }
  };

  const handleDeleteLicense = async (id: string) => {
    if (window.confirm('Tem certeza que deseja deletar esta licença?')) {
      try {
        const success = await mcp.deleteLicense(id);
        if (success) {
          setLicenses(prev => prev.filter(l => l.id !== id));
          
          // Atualizar stats
          const newStats = await mcp.getStats();
          setStats(newStats);
        }
      } catch (error) {
        console.error('Erro ao deletar licença:', error);
      }
    }
  };

  const handleToggleLicense = async (license: License) => {
    try {
      const result = await mcp.updateLicense(license.id, { active: !license.active });
      if (result) {
        setLicenses(prev => prev.map(l => l.id === license.id ? result : l));
        
        // Atualizar stats
        const newStats = await mcp.getStats();
        setStats(newStats);
      }
    } catch (error) {
      console.error('Erro ao alterar licença:', error);
    }
  };

  const handleHealthCheck = async () => {
    try {
      const health = await mcp.healthCheck();
      alert(`Status: ${health.status}\nTimestamp: ${health.timestamp}`);
    } catch (error) {
      console.error('Erro no health check:', error);
    }
  };

  const handleCreateBackup = async () => {
    try {
      const backup = await mcp.createBackup();
      const dataStr = JSON.stringify(backup, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `supabase-backup-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao criar backup:', error);
    }
  };

  if (!mcp.status.isInitialized) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Inicializando MCP do Supabase...
        </Typography>
        {mcp.status.error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {mcp.status.error}
          </Alert>
        )}
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Painel Administrativo MCP
      </Typography>

      {/* Status da Conexão */}
      <Alert 
        severity={mcp.status.isConnected ? 'success' : 'error'} 
        sx={{ mb: 3 }}
      >
        MCP Status: {mcp.status.isConnected ? 'Conectado' : 'Desconectado'}
        {mcp.status.lastCheck && (
          <Typography variant="caption" display="block">
            Última verificação: {mcp.status.lastCheck.toLocaleString()}
          </Typography>
        )}
      </Alert>

      {/* Estatísticas */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total de Licenças
                </Typography>
                <Typography variant="h4">
                  {stats.totalLicenses}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Licenças Ativas
                </Typography>
                <Typography variant="h4" color="success.main">
                  {stats.activeLicenses}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Em Uso
                </Typography>
                <Typography variant="h4" color="primary.main">
                  {stats.usedLicenses}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Usuários
                </Typography>
                <Typography variant="h4">
                  {stats.totalUsers}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Ações */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button 
          variant="contained" 
          onClick={() => setCreateDialogOpen(true)}
        >
          Nova Licença
        </Button>
        <Button 
          variant="outlined" 
          onClick={loadAllData}
          disabled={loading}
        >
          Atualizar Dados
        </Button>
        <Button 
          variant="outlined" 
          onClick={handleHealthCheck}
        >
          Health Check
        </Button>
        <Button 
          variant="outlined" 
          onClick={handleCreateBackup}
        >
          Criar Backup
        </Button>
        <Button 
          variant="outlined" 
          onClick={mcp.refreshConnection}
        >
          Reconectar MCP
        </Button>
      </Box>

      {/* Tabela de Licenças */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Licenças ({licenses.length})
          </Typography>
          
          {loading ? (
            <Box sx={{ textAlign: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Chave</TableCell>
                    <TableCell>Cliente</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Dispositivo</TableCell>
                    <TableCell>Criada em</TableCell>
                    <TableCell>Ações</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {licenses.map((license) => (
                    <TableRow key={license.id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {license.license_key}
                        </Typography>
                      </TableCell>
                      <TableCell>{license.client_name || '-'}</TableCell>
                      <TableCell>
                        <Chip 
                          label={license.active ? 'Ativa' : 'Inativa'}
                          color={license.active ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{license.device_id || 'Não vinculado'}</TableCell>
                      <TableCell>
                        {new Date(license.created_at).toLocaleDateString('pt-BR')}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          onClick={() => handleToggleLicense(license)}
                          sx={{ mr: 1 }}
                        >
                          {license.active ? 'Desativar' : 'Ativar'}
                        </Button>
                        <Button
                          size="small"
                          color="error"
                          onClick={() => handleDeleteLicense(license.id)}
                        >
                          Deletar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Dialog para criar licença */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)}>
        <DialogTitle>Nova Licença</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Nome do Cliente"
            fullWidth
            variant="outlined"
            value={newLicense.client_name}
            onChange={(e) => setNewLicense(prev => ({ ...prev, client_name: e.target.value }))}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="URL IPTV"
            fullWidth
            variant="outlined"
            value={newLicense.iptv_url}
            onChange={(e) => setNewLicense(prev => ({ ...prev, iptv_url: e.target.value }))}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Observações"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={newLicense.notes}
            onChange={(e) => setNewLicense(prev => ({ ...prev, notes: e.target.value }))}
            sx={{ mb: 2 }}
          />
          <FormControlLabel
            control={
              <Switch
                checked={newLicense.active}
                onChange={(e) => setNewLicense(prev => ({ ...prev, active: e.target.checked }))}
              />
            }
            label="Licença Ativa"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={handleCreateLicense} variant="contained">
            Criar Licença
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
