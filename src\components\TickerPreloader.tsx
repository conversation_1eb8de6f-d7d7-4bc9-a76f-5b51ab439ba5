import { useEffect } from 'react';

/**
 * Componente para pré-carregar configuração do ticker de forma ultra-rápida
 * Prioriza configuração local para inicialização instantânea
 */
const TickerPreloader: React.FC = () => {
  useEffect(() => {
    // Pré-carrega configuração local imediatamente
    const preloadTickerFast = async () => {
      try {
        // Tenta carregar configuração local primeiro (mais rápido)
        const localResponse = await fetch('/ticker-config.json', {
          cache: 'no-cache'
        });

        if (localResponse.ok) {
          const localConfig = await localResponse.json();

          // Salva no localStorage para acesso rápido
          localStorage.setItem('neko_ticker_config', JSON.stringify(localConfig));
          localStorage.setItem('neko_ticker_timestamp', Date.now().toString());
        }
      } catch (error) {
        // Silencioso - não é crítico
      }
    };

    // Executa imediatamente para máxima velocidade
    preloadTickerFast();
  }, []);

  // Este componente não renderiza nada
  return null;
};

export default TickerPreloader;
