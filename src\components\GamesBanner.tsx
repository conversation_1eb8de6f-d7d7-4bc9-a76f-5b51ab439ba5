import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Paper, 
  Typography, 
  IconButton, 
  Dialog, 
  DialogContent, 
  CircularProgress,
  <PERSON><PERSON>,
  Tooltip,
  Slider,
  Chip,
  Divider
} from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import CloseIcon from '@mui/icons-material/Close';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import SportsSoccerIcon from '@mui/icons-material/SportsSoccer';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { handleExternalLinkClick } from '../utils/openExternal';

interface GameBannerData {
  id: string;
  title: string;
  imageUrl: string;
  gameDate?: string;
  competition?: string;
  link?: string;
  whatsappMessage?: string;
}

interface GameBannerProps {
  channelName?: string;
}

const JSONBIN_API_KEY = '$2a$10$PcpcnDJfAsABVk/DzT9pQei8MCVyGcpIqe9I0BE790t4Eqf0ZSQbe';
const JSONBIN_BIN_ID = '67df26348960c979a5768aa4';
const API_URL = `https://api.jsonbin.io/v3/b/${JSONBIN_BIN_ID}`;

const GamesBanner: React.FC<GameBannerProps> = ({ channelName }) => {
  const navigate = useNavigate();
  const [banners, setBanners] = useState<GameBannerData[]>([]);
  const [activeBannerIndex, setActiveBannerIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [transition, setTransition] = useState(false);
  
  // Zoom controls
  const [zoomLevel, setZoomLevel] = useState(100);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  // Adicionar referência para o timer de transição automática
  const autoChangeTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Estado para a data e hora atuais
  const [currentDateTime, setCurrentDateTime] = useState(new Date());
  
  // Atualiza a data e hora a cada minuto
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 60000); // Atualiza a cada minuto
    
    return () => clearInterval(timer);
  }, []);
  
  // Formata a data para o formato brasileiro
  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      day: 'numeric', 
      month: 'long'
    };
    
    // Coloca a primeira letra em maiúsculo
    const formattedDate = date.toLocaleDateString('pt-BR', options);
    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
  };
  
  // Formata a hora
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  useEffect(() => {
    fetchBanners();
  }, []);

  // Efeito para transição automática entre banners - modificado para parar quando diálogo estiver aberto
  useEffect(() => {
    // Limpar qualquer timer anterior
    if (autoChangeTimerRef.current) {
      clearInterval(autoChangeTimerRef.current);
      autoChangeTimerRef.current = null;
    }
    
    // Só criar o timer se o diálogo estiver fechado e tiver mais de um banner
    if (!openDialog && banners.length > 1) {
      autoChangeTimerRef.current = setInterval(() => {
        handleNextBanner();
      }, 10000); // Muda a cada 10 segundos
    }
    
    // Limpeza quando componente desmontar ou dependências mudarem
    return () => {
      if (autoChangeTimerRef.current) {
        clearInterval(autoChangeTimerRef.current);
        autoChangeTimerRef.current = null;
      }
    };
  }, [banners, activeBannerIndex, openDialog]);
  
  // Busca os dados dos banners do JSONBin
  const fetchBanners = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(API_URL, {
        method: 'GET',
        headers: {
          'X-Master-Key': JSONBIN_API_KEY,
          'X-Access-Key': JSONBIN_API_KEY,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar dados: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.record && Array.isArray(data.record.banners)) {
        setBanners(data.record.banners);
      } else {
        // Se não tiver dados, mostra alguns banners padrão
        setBanners([
          {
            id: '1',
            title: 'Brasil vs Argentina',
            imageUrl: 'https://via.placeholder.com/500x300/1a237e/ffffff?text=Brasil+vs+Argentina',
            gameDate: '2023-10-15 19:30',
            competition: 'Eliminatórias Copa do Mundo'
          },
          {
            id: '2',
            title: 'Flamengo vs Palmeiras',
            imageUrl: 'https://via.placeholder.com/500x300/c62828/ffffff?text=Flamengo+vs+Palmeiras',
            gameDate: '2023-10-18 21:00',
            competition: 'Campeonato Brasileiro'
          },
          {
            id: '3',
            title: 'Champions League',
            imageUrl: 'https://via.placeholder.com/500x300/0d47a1/ffffff?text=Champions+League+Agenda',
            gameDate: '2023-10-20',
            competition: 'UEFA Champions League'
          }
        ]);
      }
    } catch (err) {
      console.error('Erro ao buscar banners:', err);
      setError('Falha ao carregar horários dos jogos');
      
      // Adiciona banners padrão em caso de erro
      setBanners([
        {
          id: '1',
          title: 'Brasil vs Argentina',
          imageUrl: 'https://via.placeholder.com/500x300/1a237e/ffffff?text=Brasil+vs+Argentina',
          gameDate: '2023-10-15 19:30',
          competition: 'Eliminatórias Copa do Mundo'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };
  
  const handlePrevBanner = () => {
    setTransition(true);
    setTimeout(() => {
      setActiveBannerIndex((prev) => 
        prev === 0 ? banners.length - 1 : prev - 1
      );
      setTransition(false);
    }, 300);
  };
  
  const handleNextBanner = () => {
    setTransition(true);
    setTimeout(() => {
      setActiveBannerIndex((prev) => 
        prev === banners.length - 1 ? 0 : prev + 1
      );
      setTransition(false);
    }, 300);
  };
  
  const handleOpenDialog = () => {
    setOpenDialog(true);
    // O useEffect vai cuidar de parar o timer
  };
  
  const handleCloseDialog = () => {
    setOpenDialog(false);
    // Reset zoom when closing the dialog
    resetZoom();
    // O useEffect vai cuidar de reiniciar o timer
  };
  
  const handleShareWhatsApp = (e: React.MouseEvent) => {
    e.stopPropagation(); // Previne a abertura do diálogo
    
    const banner = banners[activeBannerIndex];
    if (banner.whatsappMessage) {
      // Codifica a mensagem para URL
      const encodedMessage = encodeURIComponent(banner.whatsappMessage);
      // Abre o link do WhatsApp no navegador externo
      handleExternalLinkClick(`https://wa.me/?text=${encodedMessage}`);
    } else {
      // Mensagem padrão se não houver uma personalizada
      const defaultMessage = encodeURIComponent(
        `*${banner.title}*\n` +
        (banner.gameDate ? `📅 ${banner.gameDate}\n` : '') +
        (banner.competition ? `🏆 ${banner.competition}\n` : '') +
        (banner.link ? `🔗 Assista aqui: ${banner.link}` : '')
      );
      handleExternalLinkClick(`https://wa.me/?text=${defaultMessage}`);
    }
  };
  
  const handleOpenLink = (e: React.MouseEvent) => {
    e.stopPropagation(); // Previne a abertura do diálogo
    
    const banner = banners[activeBannerIndex];
    if (banner.link) {
      handleExternalLinkClick(banner.link);
    }
  };
  
  // Funções de zoom
  const handleZoomIn = (e: React.MouseEvent) => {
    e.stopPropagation();
    setZoomLevel(prev => Math.min(prev + 25, 400));
  };
  
  const handleZoomOut = (e: React.MouseEvent) => {
    e.stopPropagation();
    setZoomLevel(prev => Math.max(prev - 25, 50));
  };
  
  const resetZoom = () => {
    setZoomLevel(100);
    setPosition({ x: 0, y: 0 });
  };
  
  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoomLevel > 100) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoomLevel > 100) {
      // Calcula a diferença entre a posição atual e a inicial
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      
      // Atualiza a posição e a posição inicial
      setPosition(prev => ({ x: prev.x + deltaX, y: prev.y + deltaY }));
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleZoomChange = (_event: Event, value: number | number[]) => {
    setZoomLevel(value as number);
  };
  
  // Adiciona eventos de zoom com roda do mouse
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    
    if (e.deltaY < 0) {
      // Roda para cima - zoom in
      setZoomLevel(prev => Math.min(prev + 10, 400));
    } else {
      // Roda para baixo - zoom out
      setZoomLevel(prev => Math.max(prev - 10, 50));
    }
  };
  
  const renderBannerContent = () => {
    if (loading) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            p: 3
          }}
        >
          <CircularProgress size={40} />
          <Typography variant="body2" sx={{ mt: 2 }}>
            Carregando jogos...
          </Typography>
        </Box>
      );
    }
    
    if (error || banners.length === 0) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            p: 3
          }}
        >
          <ErrorOutlineIcon color="error" sx={{ fontSize: 40, mb: 1 }} />
          <Typography variant="body1" gutterBottom>
            {error || 'Nenhum jogo disponível'}
          </Typography>
          <Button 
            startIcon={<SportsSoccerIcon />} 
            variant="outlined" 
            onClick={fetchBanners}
            sx={{ mt: 2 }}
          >
            Tentar novamente
          </Button>
        </Box>
      );
    }
    
    const activeBanner = banners[activeBannerIndex];
    
    return (
      <Box
        sx={{
          height: '100%',
          width: '100%',
          position: 'relative',
          overflow: 'hidden',
          cursor: 'pointer',
          opacity: transition ? 0.6 : 1,
          transition: 'opacity 0.3s ease',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
        onClick={handleOpenDialog}
      >
        <Box
          component="img"
          src={activeBanner.imageUrl}
          alt={activeBanner.title}
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            backgroundColor: 'black',
            transition: 'transform 0.3s ease',
            '&:hover': {
              transform: 'scale(1.05)'
            }
          }}
        />
        
        <Box
          sx={{
            position: 'absolute',
            bottom: 12,
            left: 0,
            right: 0,
            display: 'flex',
            justifyContent: 'center',
            gap: 1,
            zIndex: 2
          }}
        >
          {banners.map((_, index) => (
            <Box
              key={index}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: index === activeBannerIndex ? 'primary.main' : 'rgba(255,255,255,0.4)',
                transition: 'all 0.3s ease'
              }}
            />
          ))}
        </Box>
      </Box>
    );
  };
  
  return (
    <>
      <Paper 
        elevation={3} 
        sx={{ 
          height: '100%', 
          overflow: 'hidden',
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 1.5,
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: (theme) => 
              theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.03)',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SportsSoccerIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="h2" sx={{ flexGrow: 1, fontSize: '1.1rem', fontWeight: 'bold' }}>
              Agenda do Dia
            </Typography>
          </Box>
          
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              mt: 1
            }}
          >
            <Chip 
              icon={<CalendarTodayIcon />} 
              label={formatDate(currentDateTime)}
              variant="outlined"
              size="small"
              sx={{ 
                borderRadius: '16px',
                height: 28,
                bgcolor: (theme) => theme.palette.mode === 'dark' 
                  ? 'rgba(255,255,255,0.08)' 
                  : 'rgba(0,0,0,0.04)',
                fontWeight: 500,
                color: 'text.secondary'
              }}
            />
            
            <Chip 
              icon={<AccessTimeIcon />} 
              label={formatTime(currentDateTime)}
              variant="outlined"
              size="small"
              sx={{ 
                borderRadius: '16px',
                height: 28,
                bgcolor: (theme) => theme.palette.mode === 'dark' 
                  ? 'rgba(255,255,255,0.08)' 
                  : 'rgba(0,0,0,0.04)',
                fontWeight: 500,
                color: 'text.primary'
              }}
            />
          </Box>
        </Box>
        
        {/* Banner Content */}
        <Box sx={{ flexGrow: 1, position: 'relative' }}>
          {renderBannerContent()}
        </Box>
      </Paper>
      
      {/* Dialog para visualização em tela cheia com zoom */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: '#000',
            color: '#fff',
            borderRadius: 2,
            maxHeight: '90vh',
            height: 'auto'
          }
        }}
      >
        <DialogContent 
          sx={{ p: 0, position: 'relative', overflow: 'hidden' }}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <IconButton
            onClick={handleCloseDialog}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'rgba(0,0,0,0.5)',
              color: '#fff',
              zIndex: 10,
              '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
            }}
          >
            <CloseIcon />
          </IconButton>
          
          {/* Zoom controls */}
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              bgcolor: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 0.5,
              zIndex: 10
            }}
          >
            <IconButton
              size="small"
              onClick={handleZoomOut}
              disabled={zoomLevel <= 50}
              sx={{ color: 'white' }}
            >
              <ZoomOutIcon />
            </IconButton>
            
            <Typography variant="body2" sx={{ minWidth: 45, textAlign: 'center' }}>
              {zoomLevel}%
            </Typography>
            
            <IconButton
              size="small"
              onClick={handleZoomIn}
              disabled={zoomLevel >= 400}
              sx={{ color: 'white' }}
            >
              <ZoomInIcon />
            </IconButton>
            
            <IconButton
              size="small"
              onClick={resetZoom}
              disabled={zoomLevel === 100 && position.x === 0 && position.y === 0}
              sx={{ color: 'white' }}
            >
              <RestartAltIcon />
            </IconButton>
          </Box>
          
          {/* Zoom slider */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              left: '50%',
              transform: 'translateX(-50%)',
              width: '60%',
              maxWidth: 300,
              bgcolor: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 1,
              zIndex: 10,
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <ZoomOutIcon sx={{ color: 'white', mr: 1, fontSize: '1rem' }} />
            <Slider
              value={zoomLevel}
              onChange={handleZoomChange}
              min={50}
              max={400}
              sx={{ 
                color: 'white',
                '& .MuiSlider-thumb': {
                  width: 14,
                  height: 14
                }
              }}
            />
            <ZoomInIcon sx={{ color: 'white', ml: 1, fontSize: '1rem' }} />
          </Box>
          
          <Box 
            sx={{ 
              position: 'relative',
              width: '100%', 
              height: '100%', 
              minHeight: '60vh', 
              display: 'flex', 
              justifyContent: 'center',
              alignItems: 'center',
              overflow: 'hidden'
            }}
            onWheel={handleWheel}
          >
            <Box
              ref={imageRef}
              component="img"
              src={banners[activeBannerIndex]?.imageUrl}
              alt={banners[activeBannerIndex]?.title}
              sx={{
                maxWidth: `${zoomLevel}%`,
                maxHeight: `${zoomLevel}%`,
                objectFit: 'contain',
                display: 'block',
                cursor: zoomLevel > 100 ? 'move' : 'default',
                transform: `translate(${position.x}px, ${position.y}px)`,
                transition: isDragging ? 'none' : 'transform 0.05s ease'
              }}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              draggable="false"
            />
            
            {/* Botões de navegação */}
            {banners.length > 1 && (
              <>
                <IconButton
                  sx={{
                    position: 'absolute',
                    left: 8,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    bgcolor: 'rgba(0,0,0,0.5)',
                    color: '#fff',
                    zIndex: 10,
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePrevBanner();
                    resetZoom();
                  }}
                >
                  <ArrowBackIosNewIcon />
                </IconButton>
                
                <IconButton
                  sx={{
                    position: 'absolute',
                    right: 8,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    bgcolor: 'rgba(0,0,0,0.5)',
                    color: '#fff',
                    zIndex: 10,
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNextBanner();
                    resetZoom();
                  }}
                >
                  <ArrowForwardIosIcon />
                </IconButton>
              </>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GamesBanner; 