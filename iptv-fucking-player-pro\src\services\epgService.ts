import { DatabaseService } from './database'
import { useConnectionStore } from '../stores/connectionStore'

export interface EPGProgram {
  id: string
  title: string
  description: string
  startTime: Date
  endTime: Date
  category?: string
  isLive: boolean
  progress: number // 0-100
}

export interface EPGChannel {
  id: string
  displayName: string
  programs: EPGProgram[]
}

class EPGService {
  private db: DatabaseService
  private epgData: Map<string, EPGChannel> = new Map()
  private lastUpdate: number = 0
  private readonly CACHE_DURATION = 4 * 60 * 60 * 1000 // 4 hours - like professional IPTV services
  private readonly EPG_REFRESH_INTERVAL = 6 * 60 * 60 * 1000 // 6 hours - full refresh
  private isLoading: boolean = false

  constructor() {
    this.db = DatabaseService.getInstance()
  }

  async initialize(): Promise<void> {
    await this.db.initialize()
  }

  private getCurrentConnection() {
    const store = useConnectionStore.getState()
    return store.getActiveConnection()
  }

  async loadEPG(): Promise<void> {
    const connection = this.getCurrentConnection()
    if (!connection) {
      throw new Error('No connection configured')
    }

    // Prevent multiple simultaneous loads
    if (this.isLoading) {
      console.log('📺 EPG already loading, waiting...')
      return
    }

    // Try to load from persistent cache first
    await this.loadCachedEPGData()

    // Check if we have recent cached data
    const now = Date.now()
    const cacheAge = now - this.lastUpdate
    const hasValidCache = this.epgData.size > 0 && cacheAge < this.CACHE_DURATION

    if (hasValidCache) {
      console.log(`📺 Using cached EPG data (${Math.round(cacheAge / 1000 / 60)} minutes old)`)

      // Schedule background refresh if cache is getting old
      if (cacheAge > this.CACHE_DURATION / 2) {
        setTimeout(() => this.backgroundRefresh(), 5000) // 5 seconds delay
      }
      return
    }

    // Load fresh data with retry mechanism
    await this.loadFreshEPGWithRetry()
  }

  private async loadFreshEPGWithRetry(maxRetries: number = 3): Promise<void> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📺 EPG load attempt ${attempt}/${maxRetries}`)
        await this.loadFreshEPG()
        return // Success, exit retry loop
      } catch (error) {
        lastError = error as Error
        console.warn(`⚠️ EPG load attempt ${attempt} failed:`, error)

        // If we have cached data, don't retry aggressively
        if (this.epgData.size > 0) {
          console.log('📦 Using existing cached EPG data due to load failure')
          return
        }

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // Max 10 seconds
          console.log(`⏳ Waiting ${delay}ms before retry...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    // All retries failed
    if (lastError) {
      console.error(`❌ All EPG load attempts failed after ${maxRetries} tries`)
      throw lastError
    }
  }

  private async loadFreshEPG(): Promise<void> {
    const connection = this.getCurrentConnection()
    if (!connection) return

    this.isLoading = true

    try {
      console.log('📺 Loading fresh EPG data...')
      const epgUrl = `${connection.url}/xmltv.php?username=${connection.username}&password=${connection.password}`

      // Simple fetch like NekoTV original - let Electron handle CORS
      const response = await fetch(epgUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/xml, text/xml, */*',
          'User-Agent': 'IPTV Player'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const xmlText = await response.text()
      console.log(`📄 EPG XML loaded: ${(xmlText.length / 1024 / 1024).toFixed(2)}MB`)

      // Validate XML content
      if (!xmlText.includes('<tv') && !xmlText.includes('<?xml')) {
        throw new Error('Invalid XML content received - not a valid EPG file')
      }

      await this.parseEPGXML(xmlText)

      this.lastUpdate = Date.now()
      console.log(`✅ EPG data loaded successfully - ${this.epgData.size} channels`)

      // Save to persistent cache
      await this.cacheEPGData()

      // Clean old cache entries
      this.cleanOldCache()

    } catch (error) {
      console.error('❌ Error loading fresh EPG:', error)
      // If we have any cached data, use it
      if (this.epgData.size === 0) {
        await this.loadCachedEPGData()
      }
      throw error // Re-throw to let caller handle
    } finally {
      this.isLoading = false
    }
  }



  private async backgroundRefresh(): Promise<void> {
    if (this.isLoading) return

    console.log('🔄 Background EPG refresh started...')
    try {
      await this.loadFreshEPG()
      console.log('✅ Background EPG refresh completed')
    } catch (error) {
      console.error('❌ Background EPG refresh failed:', error)
    }
  }

  private async parseEPGXML(xmlText: string): Promise<void> {
    try {
      // Validate XML content
      if (!xmlText || xmlText.trim().length === 0) {
        throw new Error('Empty XML content received')
      }

      // Check for basic XML structure
      if (!xmlText.includes('<tv') && !xmlText.includes('<?xml')) {
        throw new Error('Invalid XML format - missing XML declaration or TV root element')
      }

      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml')

      // Check for parsing errors
      const parserError = xmlDoc.getElementsByTagName('parsererror')[0]
      if (parserError) {
        throw new Error(`XML parsing error: ${parserError.textContent}`)
      }

      // Validate root element
      const tvElement = xmlDoc.getElementsByTagName('tv')[0]
      if (!tvElement) {
        throw new Error('Invalid EPG XML - missing <tv> root element')
      }

      console.log('📄 Starting EPG XML parsing...')

      // Clear existing data
      this.epgData.clear()

      // Parse channels
      const channels = xmlDoc.getElementsByTagName('channel')
      console.log(`📺 Found ${channels.length} channels in EPG`)

      for (let i = 0; i < channels.length; i++) {
        const channel = channels[i]
        const channelId = channel.getAttribute('id')
        const displayNameElement = channel.getElementsByTagName('display-name')[0]
        const displayName = displayNameElement?.textContent?.trim()

        if (channelId && displayName) {
          this.epgData.set(channelId, {
            id: channelId,
            displayName: displayName,
            programs: []
          })
        }
      }

      // Parse programs
      const programs = xmlDoc.getElementsByTagName('programme')
      console.log(`📋 Found ${programs.length} programs in EPG`)

      const now = new Date()
      let validPrograms = 0
      let skippedPrograms = 0

      for (let i = 0; i < programs.length; i++) {
        try {
          const program = programs[i]
          const channelId = program.getAttribute('channel')
          const startTime = this.parseXMLTVTime(program.getAttribute('start'))
          const endTime = this.parseXMLTVTime(program.getAttribute('stop'))

          if (!channelId || !startTime || !endTime) {
            skippedPrograms++
            continue
          }

          // Skip programs that are too old (more than 24 hours ago)
          const cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          if (endTime < cutoffTime) {
            skippedPrograms++
            continue
          }

          const titleElement = program.getElementsByTagName('title')[0]
          const descElement = program.getElementsByTagName('desc')[0]
          const categoryElement = program.getElementsByTagName('category')[0]

          const title = titleElement?.textContent?.trim() || 'Unknown Program'
          const description = descElement?.textContent?.trim() || ''
          const category = categoryElement?.textContent?.trim() || ''

          // Calculate if program is live and progress
          const isLive = now >= startTime && now <= endTime
          const totalDuration = endTime.getTime() - startTime.getTime()
          const elapsed = Math.max(0, now.getTime() - startTime.getTime())
          const progress = isLive ? Math.min(100, (elapsed / totalDuration) * 100) : 0

          const epgProgram: EPGProgram = {
            id: `${channelId}_${startTime.getTime()}`,
            title,
            description,
            startTime,
            endTime,
            category,
            isLive,
            progress
          }

          const channel = this.epgData.get(channelId)
          if (channel) {
            channel.programs.push(epgProgram)
            validPrograms++
          } else {
            skippedPrograms++
          }
        } catch (programError) {
          console.warn(`⚠️ Error parsing program ${i}:`, programError)
          skippedPrograms++
        }
      }

      // Sort programs by start time for each channel
      this.epgData.forEach(channel => {
        channel.programs.sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
      })

      console.log(`✅ EPG parsing completed: ${this.epgData.size} channels, ${validPrograms} programs (${skippedPrograms} skipped)`)

      // Validate we have meaningful data
      if (this.epgData.size === 0) {
        throw new Error('No valid channels found in EPG data')
      }

      if (validPrograms === 0) {
        console.warn('⚠️ No valid programs found in EPG data')
      }

    } catch (error) {
      console.error('❌ Error parsing EPG XML:', error)
      throw error
    }
  }

  private parseXMLTVTime(timeStr: string | null): Date | null {
    if (!timeStr) return null

    try {
      // XMLTV time format: YYYYMMDDHHMMSS +TIMEZONE
      const match = timeStr.match(/^(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})\s*([+-]\d{4})?/)
      if (!match) return null

      const [, year, month, day, hour, minute, second] = match
      return new Date(
        parseInt(year),
        parseInt(month) - 1, // Month is 0-indexed
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      )
    } catch (error) {
      console.error('Error parsing XMLTV time:', timeStr, error)
      return null
    }
  }

  private async cacheEPGData(): Promise<void> {
    try {
      const connection = this.getCurrentConnection()
      if (!connection) return

      // Cache metadata
      const metadataKey = `epg_metadata_${connection.id}`
      this.db.setCache(metadataKey, {
        lastUpdate: this.lastUpdate,
        channelCount: this.epgData.size,
        totalPrograms: Array.from(this.epgData.values()).reduce((sum, channel) => sum + channel.programs.length, 0)
      }, this.EPG_REFRESH_INTERVAL)

      // Cache EPG data in chunks for better performance
      const epgArray = Array.from(this.epgData.entries())
      const chunkSize = 50 // 50 channels per chunk

      for (let i = 0; i < epgArray.length; i += chunkSize) {
        const chunk = epgArray.slice(i, i + chunkSize)
        const chunkKey = `epg_chunk_${connection.id}_${Math.floor(i / chunkSize)}`

        this.db.setCache(chunkKey, {
          data: chunk,
          chunkIndex: Math.floor(i / chunkSize),
          lastUpdate: this.lastUpdate
        }, this.EPG_REFRESH_INTERVAL)
      }

      // Store chunk count
      const chunkCount = Math.ceil(epgArray.length / chunkSize)
      this.db.setCache(`epg_chunks_count_${connection.id}`, chunkCount, this.EPG_REFRESH_INTERVAL)

      console.log(`💾 EPG cached in ${chunkCount} chunks (${this.epgData.size} channels)`)

    } catch (error) {
      console.error('Error caching EPG data:', error)
    }
  }

  private async loadCachedEPGData(): Promise<void> {
    try {
      const connection = this.getCurrentConnection()
      if (!connection) return

      // Check metadata first
      const metadataKey = `epg_metadata_${connection.id}`
      const metadata = this.db.getCache<{ lastUpdate: number, channelCount: number, totalPrograms: number }>(metadataKey)

      if (!metadata) {
        console.log('📦 No cached EPG metadata found')
        return
      }

      // Load chunks
      const chunkCount = this.db.getCache<number>(`epg_chunks_count_${connection.id}`)
      if (!chunkCount) {
        console.log('📦 No cached EPG chunks found')
        return
      }

      this.epgData.clear()
      let loadedChunks = 0

      for (let i = 0; i < chunkCount; i++) {
        const chunkKey = `epg_chunk_${connection.id}_${i}`
        const chunk = this.db.getCache<{ data: [string, EPGChannel][], chunkIndex: number, lastUpdate: number }>(chunkKey)

        if (chunk && chunk.data) {
          chunk.data.forEach(([channelId, channelData]) => {
            this.epgData.set(channelId, channelData)
          })
          loadedChunks++
        }
      }

      if (loadedChunks > 0) {
        this.lastUpdate = metadata.lastUpdate
        const cacheAge = Math.round((Date.now() - this.lastUpdate) / 1000 / 60)
        console.log(`📦 Loaded cached EPG: ${this.epgData.size} channels, ${metadata.totalPrograms} programs (${cacheAge}min old)`)
      }

    } catch (error) {
      console.error('Error loading cached EPG data:', error)
    }
  }

  private cleanOldCache(): void {
    try {
      const connection = this.getCurrentConnection()
      if (!connection) return

      // Clean old program data (keep only current and next 24 hours)
      const now = new Date()
      const cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000) // 24 hours ago
      const futureTime = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24 hours ahead

      let cleanedPrograms = 0

      this.epgData.forEach(channel => {
        const originalCount = channel.programs.length
        channel.programs = channel.programs.filter(program =>
          program.endTime >= cutoffTime && program.startTime <= futureTime
        )
        cleanedPrograms += originalCount - channel.programs.length
      })

      if (cleanedPrograms > 0) {
        console.log(`🧹 Cleaned ${cleanedPrograms} old programs from cache`)
      }

    } catch (error) {
      console.error('Error cleaning old cache:', error)
    }
  }

  getChannelEPG(channelId: string): EPGChannel | null {
    return this.epgData.get(channelId) || null
  }

  getCurrentProgram(channelId: string): EPGProgram | null {
    const channel = this.getChannelEPG(channelId)
    if (!channel) return null

    const now = new Date()
    return channel.programs.find(program =>
      now >= program.startTime && now <= program.endTime
    ) || null
  }

  getNextProgram(channelId: string): EPGProgram | null {
    const channel = this.getChannelEPG(channelId)
    if (!channel) return null

    const now = new Date()
    return channel.programs.find(program =>
      program.startTime > now
    ) || null
  }

  getAllPrograms(channelId: string): EPGProgram[] {
    const channel = this.getChannelEPG(channelId)
    return channel?.programs || []
  }

  // Get programs for a specific time range
  getProgramsInRange(channelId: string, startTime: Date, endTime: Date): EPGProgram[] {
    const channel = this.getChannelEPG(channelId)
    if (!channel) return []

    return channel.programs.filter(program =>
      program.startTime < endTime && program.endTime > startTime
    )
  }

  // Update progress for live programs
  updateProgress(): void {
    const now = new Date()
    let updatedPrograms = 0

    this.epgData.forEach(channel => {
      channel.programs.forEach(program => {
        const wasLive = program.isLive
        const isCurrentlyLive = now >= program.startTime && now <= program.endTime

        if (isCurrentlyLive) {
          const totalDuration = program.endTime.getTime() - program.startTime.getTime()
          const elapsed = Math.max(0, now.getTime() - program.startTime.getTime())
          program.progress = Math.min(100, (elapsed / totalDuration) * 100)
          program.isLive = true
          updatedPrograms++
        } else if (wasLive) {
          program.isLive = false
          program.progress = now > program.endTime ? 100 : 0
        }
      })
    })

    // Only log if there were updates to avoid spam
    if (updatedPrograms > 0) {
      console.log(`🔄 Updated ${updatedPrograms} live programs`)
    }
  }

  // Check if EPG needs refresh
  needsRefresh(): boolean {
    const now = Date.now()
    const cacheAge = now - this.lastUpdate
    return this.epgData.size === 0 || cacheAge > this.CACHE_DURATION
  }

  // Get cache status
  getCacheStatus(): { age: number, channelCount: number, isValid: boolean } {
    const age = Date.now() - this.lastUpdate
    return {
      age: Math.round(age / 1000 / 60), // age in minutes
      channelCount: this.epgData.size,
      isValid: age < this.CACHE_DURATION && this.epgData.size > 0
    }
  }

  // Format time for display
  formatTime(date: Date): string {
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  formatDuration(startTime: Date, endTime: Date): string {
    const duration = endTime.getTime() - startTime.getTime()
    const minutes = Math.floor(duration / (1000 * 60))
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }
}

export default EPGService