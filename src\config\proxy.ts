// Configuração automática do proxy IPTV
export const getProxyConfig = () => {
  // Se está em desenvolvimento local
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return {
      url: 'http://localhost:3001/proxy',
      type: 'local',
      name: 'Proxy Local Node.js'
    };
  }
  
  // Se está em produção, usar Railway
  if (process.env.NODE_ENV === 'production') {
    return {
      url: 'https://web-production-766ea.up.railway.app/proxy',
      type: 'railway',
      name: 'Proxy Railway Cloud'
    };
  }
  
  // Fallback para desenvolvimento
  return {
    url: 'http://localhost:3001/proxy',
    type: 'local',
    name: 'Proxy Local Node.js'
  };
};

// Função para gerar URL do proxy
export const getProxyUrl = (originalUrl: string) => {
  const config = getProxyConfig();
  const encodedUrl = encodeURIComponent(originalUrl);
  return `${config.url}?url=${encodedUrl}`;
};

// Função para verificar se o proxy está funcionando
export const checkProxyHealth = async () => {
  const config = getProxyConfig();
  const healthUrl = config.url.replace('/proxy', '/health');
  
  try {
    const response = await fetch(healthUrl);
    return {
      status: response.ok,
      config: config,
      healthUrl: healthUrl
    };
  } catch (error) {
    return {
      status: false,
      config: config,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}; 