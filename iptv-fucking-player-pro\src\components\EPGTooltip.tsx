import React, { memo } from 'react'
import { EPGProgram } from '../services/epgService'
import { FiClock, FiPlay } from 'react-icons/fi'

interface EPGTooltipProps {
  programs: EPGProgram[]
  channelName: string
  isVisible: boolean
  position: { x: number; y: number }
}

const EPGTooltip: React.FC<EPGTooltipProps> = memo(({ 
  programs, 
  channelName, 
  isVisible, 
  position 
}) => {
  if (!isVisible) return null

  // Get current and next few programs
  const now = new Date()
  const relevantPrograms = programs
    .filter(program => program.endTime > now)
    .slice(0, 4) // Show max 4 programs

  return (
    <div
      className="fixed z-50 bg-slate-800 border border-slate-600 rounded-lg shadow-2xl p-4 max-w-sm transition-all duration-200 ease-out"
      style={{
        left: position.x + 10,
        top: position.y - 10,
        transform: 'translateY(-50%)',
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? 'auto' : 'none'
      }}
    >
      {/* Channel name */}
      <h3 className="font-semibold text-white mb-3 border-b border-slate-600 pb-2">
        {channelName}
      </h3>

      {/* Programs list or fallback */}
      {relevantPrograms.length > 0 ? (
        <div className="space-y-2">
          {relevantPrograms.map((program) => (
            <div
              key={program.id}
              className={`p-2 rounded ${
                program.isLive 
                  ? 'bg-sky-500/20 border border-sky-500/30' 
                  : 'bg-slate-700/50'
              }`}
            >
              {/* Time and live indicator */}
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center text-xs text-slate-300">
                  <FiClock className="w-3 h-3 mr-1" />
                  {program.startTime.toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                  {' - '}
                  {program.endTime.toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </div>
                
                {program.isLive && (
                  <div className="flex items-center text-xs text-green-400">
                    <FiPlay className="w-3 h-3 mr-1" />
                    LIVE
                  </div>
                )}
              </div>

              {/* Program title */}
              <h4 className="font-medium text-white text-sm mb-1 line-clamp-1">
                {program.title}
              </h4>

              {/* Progress bar for live program */}
              {program.isLive && (
                <div className="w-full bg-slate-600 rounded-full h-1 mb-1">
                  <div
                    className="bg-sky-500 h-1 rounded-full transition-all duration-1000"
                    style={{ width: `${program.progress}%` }}
                  />
                </div>
              )}

              {/* Description */}
              {program.description && (
                <p className="text-xs text-slate-400 line-clamp-2">
                  {program.description}
                </p>
              )}

              {/* Category */}
              {program.category && (
                <span className="inline-block mt-1 px-2 py-0.5 bg-slate-600 text-xs text-slate-300 rounded">
                  {program.category}
                </span>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-2">
          <div className="flex items-center text-xs text-green-400 mb-2">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
            LIVE
          </div>
          <p className="text-xs text-slate-400">No EPG data available</p>
        </div>
      )}

      {/* Arrow pointing to the card */}
      <div
        className="absolute w-3 h-3 bg-slate-800 border-l border-b border-slate-600 transform rotate-45"
        style={{
          left: -6,
          top: '50%',
          marginTop: -6
        }}
      />
    </div>
  )
})

export default EPGTooltip