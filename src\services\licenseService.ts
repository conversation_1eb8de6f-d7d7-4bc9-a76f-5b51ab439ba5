import { supabase } from '../integrations/supabase';
import type { License, LicenseInsert, LicenseUpdate } from '../integrations/supabase/types';

export interface LicenseCheckResult {
  valid: boolean;
  message: string;
  iptv_url?: string;
}

export interface LicenseGenerationResult {
  id: string;
  license_key: string;
}

export class LicenseService {
  /**
   * Verifica se uma licença é válida para um dispositivo específico
   */
  static async checkLicense(licenseKey: string, deviceId: string): Promise<LicenseCheckResult> {
    try {
      const { data, error } = await supabase.rpc('check_license', {
        p_license_key: licenseKey,
        p_device_id: deviceId
      });

      if (error) {
        console.error('Erro ao verificar licença:', error);
        return {
          valid: false,
          message: 'Erro interno do servidor'
        };
      }

      return data as LicenseCheckResult;
    } catch (error) {
      console.error('Erro ao verificar licença:', error);
      return {
        valid: false,
        message: '<PERSON><PERSON> de conex<PERSON>'
      };
    }
  }

  /**
   * Gera uma nova licença
   */
  static async generateLicense(clientName?: string, iptvUrl?: string): Promise<LicenseGenerationResult | null> {
    try {
      const { data, error } = await supabase.rpc('generate_license', {
        client_name: clientName,
        iptv_url: iptvUrl
      });

      if (error) {
        console.error('Erro ao gerar licença:', error);
        return null;
      }

      return data?.[0] || null;
    } catch (error) {
      console.error('Erro ao gerar licença:', error);
      return null;
    }
  }

  /**
   * Lista todas as licenças (requer autenticação)
   */
  static async getAllLicenses(): Promise<License[]> {
    try {
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erro ao buscar licenças:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erro ao buscar licenças:', error);
      return [];
    }
  }

  /**
   * Busca uma licença específica por chave
   */
  static async getLicenseByKey(licenseKey: string): Promise<License | null> {
    try {
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey)
        .single();

      if (error) {
        console.error('Erro ao buscar licença:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao buscar licença:', error);
      return null;
    }
  }

  /**
   * Atualiza uma licença
   */
  static async updateLicense(id: string, updates: LicenseUpdate): Promise<License | null> {
    try {
      const { data, error } = await supabase
        .from('licenses')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar licença:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao atualizar licença:', error);
      return null;
    }
  }

  /**
   * Ativa/desativa uma licença
   */
  static async toggleLicense(licenseKey: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('toggle_license', {
        key: licenseKey
      });

      if (error) {
        console.error('Erro ao alternar status da licença:', error);
        return false;
      }

      return data;
    } catch (error) {
      console.error('Erro ao alternar status da licença:', error);
      return false;
    }
  }

  /**
   * Deleta uma licença
   */
  static async deleteLicense(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('licenses')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Erro ao deletar licença:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao deletar licença:', error);
      return false;
    }
  }

  /**
   * Registra visualização de conteúdo
   */
  static async registerContentView(
    licenseKey: string,
    deviceId: string,
    contentId: string,
    contentName: string,
    contentType: string,
    contentCategory?: string,
    contentImage?: string,
    epgInfo?: any
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('register_content_view', {
        p_license_key: licenseKey,
        p_device_id: deviceId,
        p_content_id: contentId,
        p_content_name: contentName,
        p_content_type: contentType,
        p_content_category: contentCategory,
        p_content_image: contentImage,
        p_epg_info: epgInfo
      });

      if (error) {
        console.error('Erro ao registrar visualização:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao registrar visualização:', error);
      return false;
    }
  }

  /**
   * Busca licenças por dispositivo
   */
  static async getLicensesByDevice(deviceId: string): Promise<License[]> {
    try {
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('device_id', deviceId);

      if (error) {
        console.error('Erro ao buscar licenças por dispositivo:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erro ao buscar licenças por dispositivo:', error);
      return [];
    }
  }

  /**
   * Busca licenças ativas
   */
  static async getActiveLicenses(): Promise<License[]> {
    try {
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erro ao buscar licenças ativas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erro ao buscar licenças ativas:', error);
      return [];
    }
  }
}
