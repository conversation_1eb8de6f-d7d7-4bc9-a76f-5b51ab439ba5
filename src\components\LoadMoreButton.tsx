import React from 'react';
import { Box, Button, Typography, CircularProgress } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface LoadMoreButtonProps {
  onLoadMore: () => void;
  loading?: boolean;
  hasMore: boolean;
  totalLoaded: number;
  totalAvailable: number;
}

const LoadMoreButton: React.FC<LoadMoreButtonProps> = ({
  onLoadMore,
  loading = false,
  hasMore,
  totalLoaded,
  totalAvailable
}) => {
  if (!hasMore) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        pt: 0.5,
        pb: 1,
        px: 2,
        mt: -0.5, // Remove espaço extra do último item da lista
        color: 'text.secondary'
      }}>
        <Typography variant="body2">
          Todos os {totalAvailable} canais foram carregados
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      pt: 0.5,
      pb: 1,
      px: 2,
      gap: 1,
      mt: -0.5 // Remove espaço extra do último item da lista
    }}>
      <Typography variant="body2" color="text.secondary">
        Mostrando {totalLoaded} de {totalAvailable} canais
      </Typography>

      <Button
        variant="outlined"
        onClick={onLoadMore}
        disabled={loading}
        startIcon={loading ? <CircularProgress size={16} /> : <ExpandMoreIcon />}
        sx={{
          textTransform: 'none',
          borderRadius: 2,
          px: 3,
          py: 1
        }}
      >
        {loading ? 'Carregando...' : 'Carregar mais canais'}
      </Button>
    </Box>
  );
};

export default LoadMoreButton;
