@use './_theme.scss' as theme;

.mobile-channels {
  padding: theme.$spacing-md;
  
  &__list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: theme.$spacing-md;
  }
  
  &__item {
    background: theme.$surface-bg;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  &__thumbnail {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
    background: theme.$elevated-bg;
  }
  
  &__info {
    padding: theme.$spacing-sm;
  }
  
  &__title {
    color: theme.$text-color;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  &__group {
    color: theme.$text-secondary;
    font-size: 0.8rem;
    margin-top: theme.$spacing-xs;
  }
}

@media (max-width: theme.$breakpoint-sm) {
  .mobile-channels {
    padding: theme.$spacing-sm;
    
    &__list {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: theme.$spacing-sm;
    }
    
    &__title {
      font-size: 0.8rem;
    }
    
    &__group {
      font-size: 0.7rem;
    }
  }
} 