// Script para testar velocidade de inicialização do ticker
// Execute no console do navegador

console.log('⚡ Testando velocidade de inicialização do FastMessageTicker...');

// Função para medir tempo de inicialização
function measureTickerInitialization() {
  const startTime = performance.now();
  
  // Observa quando o ticker aparece na tela
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const ticker = node.querySelector ? 
            node.querySelector('[class*="FastTickerContainer"], [class*="TickerContainer"]') :
            null;
          
          if (ticker || (node.className && node.className.includes && 
              (node.className.includes('FastTickerContainer') || node.className.includes('TickerContainer')))) {
            const endTime = performance.now();
            const initTime = endTime - startTime;
            
            console.log(`🚀 Ticker inicializado em: ${initTime.toFixed(2)}ms`);
            
            if (initTime < 100) {
              console.log('✅ EXCELENTE: Inicialização ultra-rápida (<100ms)');
            } else if (initTime < 500) {
              console.log('✅ BOM: Inicialização rápida (<500ms)');
            } else if (initTime < 1000) {
              console.log('⚠️ ACEITÁVEL: Inicialização moderada (<1s)');
            } else {
              console.log('❌ LENTO: Inicialização demorada (>1s)');
            }
            
            observer.disconnect();
          }
        }
      });
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Timeout de segurança
  setTimeout(() => {
    observer.disconnect();
    console.log('⏰ Timeout: Ticker não foi detectado em 5 segundos');
  }, 5000);
}

// Função para testar clicabilidade dos links
function testLinkClickability() {
  console.log('\n🖱️ Testando clicabilidade dos links...');
  
  const links = document.querySelectorAll('[class*="FastTickerContainer"] a, [class*="TickerContainer"] a');
  
  if (links.length === 0) {
    console.log('❌ Nenhum link encontrado no ticker');
    return false;
  }
  
  console.log(`📍 Encontrados ${links.length} link(s) no ticker`);
  
  links.forEach((link, index) => {
    const rect = link.getBoundingClientRect();
    const styles = window.getComputedStyle(link);
    
    const isVisible = rect.width > 0 && rect.height > 0;
    const isClickable = styles.pointerEvents !== 'none';
    const hasCorrectCursor = styles.cursor === 'pointer';
    const hasHighZIndex = parseInt(styles.zIndex) >= 1000;
    
    console.log(`Link ${index + 1}:`, {
      visible: isVisible,
      clickable: isClickable,
      cursor: hasCorrectCursor,
      zIndex: styles.zIndex,
      href: link.href
    });
    
    if (isVisible && isClickable && hasCorrectCursor && hasHighZIndex) {
      console.log(`✅ Link ${index + 1} está perfeitamente configurado`);
    } else {
      console.log(`⚠️ Link ${index + 1} pode ter problemas`);
    }
  });
  
  return true;
}

// Função para testar performance da animação
function testAnimationPerformance() {
  console.log('\n🎨 Testando performance da animação...');
  
  const scrollingContent = document.querySelector('[class*="FastScrollingContent"], [class*="ScrollingContent"]');
  
  if (!scrollingContent) {
    console.log('❌ Conteúdo scrollável não encontrado');
    return false;
  }
  
  const styles = window.getComputedStyle(scrollingContent);
  
  console.log('Propriedades de otimização:', {
    willChange: styles.willChange,
    backfaceVisibility: styles.backfaceVisibility,
    transform: styles.transform,
    animation: styles.animation
  });
  
  // Testa se a animação está usando transform3d
  const usesTransform3d = styles.animation.includes('translate3d') || 
                         styles.transform.includes('translate3d');
  
  if (usesTransform3d) {
    console.log('✅ Animação otimizada com transform3d');
  } else {
    console.log('⚠️ Animação pode não estar otimizada');
  }
  
  return true;
}

// Função para simular clique e medir resposta
function testClickResponse() {
  console.log('\n⚡ Testando tempo de resposta ao clique...');
  
  const links = document.querySelectorAll('[class*="FastTickerContainer"] a, [class*="TickerContainer"] a');
  
  if (links.length === 0) {
    console.log('❌ Nenhum link disponível para teste');
    return false;
  }
  
  const firstLink = links[0];
  const startTime = performance.now();
  
  // Simula clique
  const clickEvent = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
    view: window
  });
  
  firstLink.dispatchEvent(clickEvent);
  
  const endTime = performance.now();
  const responseTime = endTime - startTime;
  
  console.log(`🖱️ Tempo de resposta ao clique: ${responseTime.toFixed(2)}ms`);
  
  if (responseTime < 10) {
    console.log('✅ EXCELENTE: Resposta instantânea');
  } else if (responseTime < 50) {
    console.log('✅ BOM: Resposta rápida');
  } else {
    console.log('⚠️ Resposta pode estar lenta');
  }
  
  return true;
}

// Função principal de teste
async function runSpeedTests() {
  console.log('\n🏁 Iniciando testes de velocidade do ticker...\n');
  
  // Aguarda um pouco para garantir que a página carregou
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const tests = [
    { name: 'Clicabilidade dos Links', fn: testLinkClickability },
    { name: 'Performance da Animação', fn: testAnimationPerformance },
    { name: 'Tempo de Resposta ao Clique', fn: testClickResponse }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n🧪 Executando: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.error(`❌ Erro no teste ${test.name}:`, error);
      results.push({ name: test.name, passed: false, error });
    }
  }
  
  // Resumo
  console.log('\n📊 Resumo dos Testes de Velocidade:');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
  });
  
  console.log('='.repeat(50));
  console.log(`📈 Resultado: ${passed}/${total} testes passaram (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('🎉 Todos os testes de velocidade passaram! O ticker está ultra-otimizado.');
  } else {
    console.log('⚠️ Alguns testes falharam. Verifique os logs para otimizações.');
  }
}

// Função para comparar velocidades
function compareTickerVersions() {
  console.log('\n🔄 Comparando versões do ticker...');
  
  const fastTicker = document.querySelector('[class*="FastTickerContainer"]');
  const normalTicker = document.querySelector('[class*="TickerContainer"]:not([class*="FastTickerContainer"])');
  
  if (fastTicker) {
    console.log('✅ FastMessageTicker detectado - versão otimizada em uso');
  } else if (normalTicker) {
    console.log('⚠️ MessageTicker normal detectado - considere usar FastMessageTicker');
  } else {
    console.log('❌ Nenhum ticker detectado');
  }
}

// Executar testes automaticamente
console.log('🚀 Iniciando medição de inicialização...');
measureTickerInitialization();

// Executar outros testes após delay
setTimeout(() => {
  compareTickerVersions();
  runSpeedTests();
}, 2000);

// Exportar funções para uso manual
window.tickerSpeedTests = {
  measureTickerInitialization,
  testLinkClickability,
  testAnimationPerformance,
  testClickResponse,
  runSpeedTests,
  compareTickerVersions
};
