import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Grid,
  Divider,
  IconButton,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  <PERSON>ne as TuneIcon,
  <PERSON><PERSON>hart as StatsIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Hd as HdIcon,
  FourK as FourKIcon,
  HighQuality as QualityIcon
} from '@mui/icons-material';
import type { UseChannelOrganizerReturn } from '../hooks/useChannelOrganizer';

interface ChannelOrganizerPanelProps {
  organizer: UseChannelOrganizerReturn;
  onRefresh?: () => void;
}

const ChannelOrganizerPanel: React.FC<ChannelOrganizerPanelProps> = ({
  organizer,
  onRefresh
}) => {
  const theme = useTheme();

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case '4K': return <FourKIcon fontSize="small" />;
      case 'FHD': return <HdIcon fontSize="small" />;
      case 'HD': return <HdIcon fontSize="small" />;
      default: return <QualityIcon fontSize="small" />;
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case '4K': return '#ff6b35';
      case 'FHD': return '#4caf50';
      case 'HD': return '#2196f3';
      case 'SD': return '#757575';
      default: return theme.palette.text.secondary;
    }
  };

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(18, 18, 18, 0.95), rgba(28, 28, 28, 0.95))'
          : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95))'
      }}
    >
      {/* Cabeçalho */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <TuneIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          Organizador de Canais
        </Typography>
        {onRefresh && (
          <Tooltip title="Atualizar">
            <IconButton onClick={onRefresh} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Grid container spacing={2}>
        {/* Configurações Principais */}
        <Grid item xs={12} md={6}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Toggle Organização */}
            <FormControlLabel
              control={
                <Switch
                  checked={organizer.isOrganized}
                  onChange={organizer.toggleOrganization}
                  color="primary"
                />
              }
              label="Organizar canais por qualidade"
            />

            {/* Qualidade Padrão */}
            <FormControl size="small" disabled={!organizer.isOrganized}>
              <InputLabel>Qualidade Padrão</InputLabel>
              <Select
                value={organizer.defaultQuality}
                onChange={(e) => organizer.setDefaultQuality(e.target.value as any)}
                label="Qualidade Padrão"
              >
                <MenuItem value="4K">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getQualityIcon('4K')}
                    4K Ultra HD
                  </Box>
                </MenuItem>
                <MenuItem value="FHD">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getQualityIcon('FHD')}
                    Full HD (FHD)
                  </Box>
                </MenuItem>
                <MenuItem value="HD">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getQualityIcon('HD')}
                    HD
                  </Box>
                </MenuItem>
                <MenuItem value="SD">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getQualityIcon('SD')}
                    SD
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            {/* Filtros */}
            <Box>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FilterIcon fontSize="small" />
                Filtros Ativos
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {organizer.selectedCategory !== 'all' && (
                  <Chip
                    label={`Categoria: ${organizer.selectedCategory}`}
                    onDelete={() => organizer.setSelectedCategory('all')}
                    size="small"
                    color="primary"
                  />
                )}
                {organizer.searchTerm && (
                  <Chip
                    label={`Busca: ${organizer.searchTerm}`}
                    onDelete={() => organizer.setSearchTerm('')}
                    size="small"
                    color="secondary"
                  />
                )}
                {(organizer.selectedCategory !== 'all' || organizer.searchTerm) && (
                  <Chip
                    label="Limpar Filtros"
                    onClick={organizer.resetFilters}
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            </Box>
          </Box>
        </Grid>

        {/* Estatísticas */}
        <Grid item xs={12} md={6}>
          <Box>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <StatsIcon fontSize="small" />
              Estatísticas
            </Typography>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {/* Totais */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip
                  label={`${organizer.stats.totalGroups} canais únicos`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`${organizer.stats.totalVariants} variantes`}
                  size="small"
                  color="secondary"
                />
                {organizer.stats.alternativesCount > 0 && (
                  <Chip
                    label={`${organizer.stats.alternativesCount} com alternativas`}
                    size="small"
                    color="warning"
                  />
                )}
              </Box>

              {/* Distribuição de Qualidade */}
              <Box>
                <Typography variant="caption" color="text.secondary" gutterBottom>
                  Distribuição por Qualidade:
                </Typography>
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  {Object.entries(organizer.stats.qualityDistribution)
                    .sort(([a], [b]) => {
                      const order = { '4K': 4, 'FHD': 3, 'HD': 2, 'SD': 1 };
                      return (order[b as keyof typeof order] || 0) - (order[a as keyof typeof order] || 0);
                    })
                    .map(([quality, count]) => (
                      <Chip
                        key={quality}
                        label={`${quality}: ${count}`}
                        size="small"
                        sx={{
                          bgcolor: alpha(getQualityColor(quality), 0.1),
                          color: getQualityColor(quality),
                          fontSize: '0.7rem'
                        }}
                      />
                    ))}
                </Box>
              </Box>

              {/* Top Categorias */}
              <Box>
                <Typography variant="caption" color="text.secondary" gutterBottom>
                  Top Categorias:
                </Typography>
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  {Object.entries(organizer.stats.categoryDistribution)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 3)
                    .map(([category, count]) => (
                      <Chip
                        key={category}
                        label={`${category}: ${count}`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    ))}
                </Box>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>

      {/* Status */}
      <Divider sx={{ my: 2 }} />
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="caption" color="text.secondary">
          {organizer.isOrganized 
            ? `Exibindo ${organizer.filteredGroups.length} de ${organizer.channelGroups.length} canais organizados`
            : `Exibindo ${organizer.compatibilityChannels.length} canais (modo compatibilidade)`
          }
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={organizer.isOrganized ? 'Organizado' : 'Lista Original'}
            size="small"
            color={organizer.isOrganized ? 'success' : 'default'}
          />
        </Box>
      </Box>
    </Paper>
  );
};

export default ChannelOrganizerPanel;
