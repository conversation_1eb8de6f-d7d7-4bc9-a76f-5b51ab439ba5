import { useState, useEffect, useCallback } from 'react';
import { messageTickerService } from '../services/messageTickerService';
import type { TickerConfig } from '../components/MessageTicker';

interface UseMessageTickerOptions {
  autoLoad?: boolean;
  refreshInterval?: number; // em minutos
  customUrl?: string;
}

interface UseMessageTickerReturn {
  config: TickerConfig | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  preload: () => Promise<void>;
  resetToDefault: () => void;
  cacheInfo: {
    hasCache: boolean;
    lastFetch: Date | null;
    isValid: boolean;
  };
}

/**
 * Hook personalizado otimizado para gerenciar o MessageTicker
 */
export const useMessageTicker = (options: UseMessageTickerOptions = {}): UseMessageTickerReturn => {
  const {
    autoLoad = true,
    refreshInterval,
    customUrl
  } = options;

  const [config, setConfig] = useState<TickerConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Carrega configuração do ticker com otimizações
   */
  const loadConfig = useCallback(async (forceRefresh: boolean = false) => {
    // Se já tem configuração e não está forçando refresh, não mostra loading
    if (!forceRefresh && config) {
      setError(null);
    } else {
      setLoading(true);
      setError(null);
    }

    try {
      const tickerConfig = await messageTickerService.getConfig(forceRefresh, customUrl);
      setConfig(tickerConfig);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);

      // Em caso de erro, tenta usar configuração padrão de forma assíncrona
      try {
        const defaultConfig = await messageTickerService.resetToDefault();
        setConfig(defaultConfig);
        setError(null); // Limpa erro se conseguiu carregar padrão
      } catch (defaultError) {
        // Silencioso
      }
    } finally {
      setLoading(false);
    }
  }, [customUrl, config]);

  /**
   * Força refresh da configuração
   */
  const refresh = useCallback(async () => {
    await loadConfig(true);
  }, [loadConfig]);

  /**
   * Pré-carrega configuração em background
   */
  const preload = useCallback(async () => {
    try {
      await messageTickerService.preloadConfig(customUrl);
    } catch (err) {
      // Silencioso
    }
  }, [customUrl]);

  /**
   * Reseta para configuração padrão
   */
  const resetToDefault = useCallback(() => {
    try {
      const defaultConfig = messageTickerService.resetToDefault();
      setConfig(defaultConfig);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao resetar configuração';
      setError(errorMessage);
    }
  }, []);

  /**
   * Obtém informações do cache
   */
  const cacheInfo = messageTickerService.getCacheInfo();

  // Carregamento inicial super otimizado - prioriza velocidade
  useEffect(() => {
    if (autoLoad) {
      const initializeTicker = async () => {
        try {
          // Primeiro tenta carregar configuração local imediatamente
          const localConfig = await fetch('/ticker-config.json', {
            cache: 'no-cache'
          }).then(res => res.json()).catch(() => null);

          if (localConfig) {
            setConfig(localConfig);
            setLoading(false);

            // Depois tenta atualizar com configuração remota em background
            setTimeout(() => {
              messageTickerService.getConfig(true, customUrl)
                .then(remoteConfig => {
                  if (JSON.stringify(remoteConfig) !== JSON.stringify(localConfig)) {
                    setConfig(remoteConfig);
                  }
                })
                .catch(() => {});
            }, 1000); // Delay de 1 segundo para não interferir na UI
          } else {
            // Se não conseguir carregar local, usa o método normal
            loadConfig();
          }
        } catch (error) {
          loadConfig();
        }
      };

      initializeTicker();
    }
  }, [autoLoad, loadConfig, customUrl]);

  // Refresh automático
  useEffect(() => {
    if (!refreshInterval || refreshInterval <= 0) {
      return;
    }

    const intervalMs = refreshInterval * 60 * 1000; // converter minutos para ms
    const interval = setInterval(() => {
      loadConfig(true);
    }, intervalMs);

    return () => clearInterval(interval);
  }, [refreshInterval, loadConfig]);

  return {
    config,
    loading,
    error,
    refresh,
    preload,
    resetToDefault,
    cacheInfo
  };
};

/**
 * Hook simplificado para uso básico com inicialização ultra-rápida
 */
export const useSimpleMessageTicker = (customUrl?: string) => {
  const [config, setConfig] = useState<TickerConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeQuickly = async () => {
      try {
        // SEMPRE tenta buscar do Pastebin primeiro (prioridade máxima)
        const remoteConfig = await messageTickerService.getConfig(true, customUrl); // Force refresh
        setConfig(remoteConfig);
        setLoading(false);
        setError(null);
        return;
      } catch (remoteError) {
        // Se Pastebin falhou, tenta configuração local
        try {
          const localResponse = await fetch('/ticker-config.json', {
            cache: 'no-cache'
          });

          if (localResponse.ok) {
            const localConfig = await localResponse.json();
            setConfig(localConfig);
            setLoading(false);
            setError(null);
            return;
          }
        } catch (localError) {
          // Silencioso
        }

        // Último recurso: configuração padrão
        setError(remoteError instanceof Error ? remoteError.message : 'Erro desconhecido');
        try {
          const defaultConfig = await messageTickerService.resetToDefault();
          setConfig(defaultConfig);
          setError(null);
        } catch (defaultError) {
          // Silencioso
        }
      } finally {
        setLoading(false);
      }
    };

    initializeQuickly();
  }, [customUrl]);

  return {
    config: config && config.enabled ? config : null,
    loading,
    error
  };
};

export default useMessageTicker;
