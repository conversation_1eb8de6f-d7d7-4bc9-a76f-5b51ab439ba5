import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, Card, CardMedia, CardContent, Grid, CircularProgress, Skeleton, useTheme, LinearProgress } from '@mui/material';
import { dbService } from '../../services/dbService';
import { useNavigate } from 'react-router-dom';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { Stream } from '../../services/iptvService';

interface ContinueWatchingSeriesProps {
  onSeriesSelect?: (series: Stream) => void;
  onSeriesPlay?: (series: Stream, episodeId: string, seasonId: string, startTime: number) => void;
  maxItems?: number;
}

// Extend the Stream interface to include the episode information
interface WatchedSeries extends Stream {
  progress: number;
  duration: number;
  seasonId: string;
  episodeId: string;
  currentEpisode?: {
    number: number;
    title: string;
    season: number;
  };
}

const ContinueWatchingSeries: React.FC<ContinueWatchingSeriesProps> = ({ 
  onSeriesSelect, 
  onSeriesPlay, 
  maxItems = 6 
}) => {
  const [watchedSeries, setWatchedSeries] = useState<WatchedSeries[]>([]);
  const [loading, setLoading] = useState(true);
  const theme = useTheme();
  const navigate = useNavigate();
  // Add a ref to track the latest data
  const watchedSeriesRef = useRef<WatchedSeries[]>([]);
  // Add a ref for the interval
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Function to load watched series
  const loadWatchedSeries = async () => {
    try {
      const series = await dbService.getRecentlyWatchedSeries(maxItems);
      
      // Transform the data
      const transformedSeries = series.map(item => ({
        id: item.id,
        name: item.name,
        thumbnail: item.thumbnail,
        cover: item.cover,
        progress: item.progress,
        duration: item.duration,
        seasonId: item.seasonId || '',
        episodeId: item.episodeId || '',
        currentEpisode: item.currentEpisode
      }));
      
      // Check if data has changed before updating state
      if (JSON.stringify(transformedSeries) !== JSON.stringify(watchedSeriesRef.current)) {
        console.log('📺 Continue Watching Series: Updated with new data');
        setWatchedSeries(transformedSeries);
        watchedSeriesRef.current = transformedSeries;
      }
    } catch (error) {
      console.error('Error loading watched series:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    const initialLoad = async () => {
      setLoading(true);
      await loadWatchedSeries();
    };
    
    initialLoad();
  }, [maxItems]);

  // Set up periodic refresh with visibility handling
  useEffect(() => {
    // Function to start the refresh interval
    const startRefreshInterval = () => {
      // Clear any existing interval first
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      
      // Set new interval
      refreshIntervalRef.current = setInterval(() => {
        loadWatchedSeries();
      }, 30000); // 30 seconds
    };
    
    // Function to stop the refresh interval
    const stopRefreshInterval = () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
    
    // Handle visibility change
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Tab is hidden, pause refreshes
        stopRefreshInterval();
      } else {
        // Tab is visible again, refresh immediately and restart interval
        loadWatchedSeries();
        startRefreshInterval();
      }
    };
    
    // Initial load and start interval
    loadWatchedSeries();
    startRefreshInterval();
    
    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Clean up
    return () => {
      stopRefreshInterval();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [maxItems]);

  const handleSeriesClick = (series: WatchedSeries) => {
    // Convert WatchedSeries to Stream
    const streamSeries: Stream = {
      id: series.id,
      name: series.name,
      thumbnail: series.thumbnail,
      cover: series.cover
    };
    
    if (onSeriesPlay && series.currentEpisode && series.episodeId) {
      // Use the season from currentEpisode instead of seasonId
      const seasonId = series.currentEpisode.season ? String(series.currentEpisode.season) : series.seasonId;
      
      console.log('🎬 Playing series from continue watching:', {
        series: series.name,
        seriesId: series.id,
        seasonId,
        episodeId: series.episodeId,
        originalSeasonId: series.seasonId,
        currentEpisode: series.currentEpisode
      });
      
      // FIX: Swap the order of parameters to match the function signature:
      // playSeriesDirectly(series: Stream, episodeId: string, seasonId: string, startTime: number)
      onSeriesPlay(streamSeries, series.episodeId, seasonId, series.progress);
    } else if (onSeriesSelect) {
      // Otherwise, use the selection handler
      onSeriesSelect(streamSeries);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimeLeft = (current: number, total: number) => {
    const remaining = total - current;
    if (remaining <= 0) return "Concluído";
    
    const minutes = Math.floor(remaining / 60);
    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m restantes`;
    }
    return `${minutes}m restantes`;
  };

  if (loading) {
    return (
      <Box sx={{ mt: 3 }}>
        <Grid container spacing={2}>
          {[...Array(4)].map((_, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={index}>
              <Card sx={{ position: 'relative', borderRadius: 2, overflow: 'hidden', bgcolor: 'rgba(255,255,255,0.04)' }}>
                <Skeleton variant="rectangular" height={160} animation="wave" sx={{ bgcolor: 'rgba(255,255,255,0.08)' }} />
                <CardContent sx={{ height: 80 }}>
                  <Skeleton animation="wave" height={24} width="80%" sx={{ mb: 1, bgcolor: 'rgba(255,255,255,0.08)' }} />
                  <Skeleton animation="wave" height={16} width="60%" sx={{ bgcolor: 'rgba(255,255,255,0.08)' }} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (watchedSeries.length === 0) {
    return (
      <Box sx={{ mt: 3 }}>
        <Box sx={{ bgcolor: 'rgba(18, 18, 18, 0.7)', p: 3, borderRadius: 2, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Nenhuma série assistida recentemente.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Assista alguma série para que ela apareça aqui.
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Grid container spacing={2}>
        {watchedSeries.map((series) => {
          const progressPercent = (series.progress / series.duration) * 100;
          
          return (
            <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={series.id}>
              <Card 
                sx={{ 
                  position: 'relative', 
                  borderRadius: 2, 
                  overflow: 'hidden',
                  transition: 'transform 0.2s',
                  bgcolor: 'rgba(18, 18, 18, 0.7)',
                  '&:hover': {
                    transform: 'scale(1.05)',
                    '& .MuiBox-root.play-button': {
                      opacity: 1
                    }
                  },
                  cursor: 'pointer'
                }}
                onClick={() => handleSeriesClick(series)}
              >
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height={160}
                    image={series.cover || series.thumbnail}
                    alt={series.name}
                    sx={{ objectFit: 'cover' }}
                  />
                  <LinearProgress 
                    variant="determinate" 
                    value={progressPercent} 
                    sx={{ 
                      position: 'absolute', 
                      bottom: 0, 
                      left: 0, 
                      right: 0, 
                      height: 4,
                      borderRadius: 0,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: theme.palette.primary.main
                      }
                    }} 
                  />
                  <Box 
                    className="play-button"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(0,0,0,0.4)',
                      opacity: 0,
                      transition: 'opacity 0.2s'
                    }}
                  >
                    <Box 
                      sx={{ 
                        width: 50, 
                        height: 50, 
                        borderRadius: '50%', 
                        bgcolor: 'rgba(14, 165, 233, 0.85)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <PlayArrowIcon sx={{ color: 'white', fontSize: 30 }} />
                    </Box>
                  </Box>
                </Box>
                <CardContent sx={{ py: 1.5 }}>
                  <Typography variant="body1" noWrap sx={{ fontWeight: 500 }}>
                    {series.name}
                  </Typography>
                  {series.currentEpisode && (
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      T{series.currentEpisode.season} E{series.currentEpisode.number} - {series.currentEpisode.title}
                    </Typography>
                  )}
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 0.5 }}>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {formatTime(series.progress)} / {formatTime(series.duration)}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {formatTimeLeft(series.progress, series.duration)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default ContinueWatchingSeries; 