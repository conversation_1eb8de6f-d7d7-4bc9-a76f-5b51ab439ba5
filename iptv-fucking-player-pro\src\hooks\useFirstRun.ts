import { useState, useEffect } from 'react';

const FIRST_RUN_KEY = 'iptv-player-first-run';

export const useFirstRun = () => {
  const [isFirstRun, setIsFirstRun] = useState<boolean | null>(null);

  useEffect(() => {
    const hasRunBefore = localStorage.getItem(FIRST_RUN_KEY);
    setIsFirstRun(!hasRunBefore);
  }, []);

  const markAsCompleted = () => {
    localStorage.setItem(FIRST_RUN_KEY, 'completed');
    setIsFirstRun(false);
  };

  return {
    isFirstRun,
    markAsCompleted
  };
};