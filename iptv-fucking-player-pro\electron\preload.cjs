const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose isElectronApp to renderer process
contextBridge.exposeInMainWorld('isElectronApp', true);

// Expose safe APIs
contextBridge.exposeInMainWorld('electronAPI', {
  getAppVersion: () => process.env.npm_package_version || 'dev',
  getPlatform: () => process.platform,
  
  openExternal: (url) => {
    const allowedDomains = [
      'github.com',
      'discord.gg'
    ];

    try {
      const urlObj = new URL(url);
      const isAllowed = allowedDomains.some(domain => urlObj.hostname.includes(domain));

      if (isAllowed) {
        ipcRenderer.send('open-external', url);
        return true;
      } else {
        console.warn('Domain not allowed for external opening:', urlObj.hostname);
        return false;
      }
    } catch (error) {
      console.error('Invalid URL:', url);
      return false;
    }
  }
});

// Initialize when DOM is loaded
window.addEventListener('DOMContentLoaded', () => {
  console.log('Electron preload script loaded');
});