import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Rating,
  Chip,
  Stack,
  IconButton,
  useTheme,
  CircularProgress,
  LinearProgress,
  alpha,
  Fade,
  Tooltip,
  Skeleton,
  styled
} from '@mui/material';
import { Stream, createIPTVService } from '../../services/iptvService';
import { tmdbService } from '../../services/tmdbService';
import { dbService } from '../../services/dbService';
import VideoPlayer from '../VideoPlayer/VideoPlayer';
import CloseIcon from '@mui/icons-material/Close';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StarIcon from '@mui/icons-material/Star';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { motion } from 'framer-motion';
import { PlayCircle } from '@mui/icons-material';
import { getSafeUrl } from '../../utils/proxyUrl';

interface MovieCardProps {
  movie: Stream;
  onClick?: (movie: Stream) => void;
}

const StyledCard = styled(Card)(({ theme }) => ({
  position: 'relative',
  height: '100%',
  width: '100%',
  transition: 'transform 0.3s ease',
  overflow: 'hidden',
  backgroundColor: 'transparent',
  '&:hover': {
    transform: 'scale(1.05)',
    zIndex: 1,
    '& .MuiCardActionArea-focusHighlight': {
      opacity: 0,
    },
    '& .play-button': {
      opacity: 1,
      transform: 'translate(-50%, -50%) scale(1)',
    }
  },
}));

const MovieOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  padding: theme.spacing(1),
  background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 70%, rgba(0,0,0,0) 100%)',
  transition: 'opacity 0.3s ease',
  opacity: 1,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  zIndex: 2,
}));

const PlayButton = styled(PlayCircle)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%) scale(0.8)',
  fontSize: '3rem',
  color: theme.palette.common.white,
  opacity: 0,
  transition: 'all 0.3s ease',
  zIndex: 2,
}));

const StyledLinearProgress = styled(LinearProgress)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  height: '3px',
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  '& .MuiLinearProgress-bar': {
    backgroundColor: theme.palette.primary.main,
  },
  zIndex: 3,
}));

const MovieCard: React.FC<MovieCardProps> = ({ movie, onClick }) => {
  const [open, setOpen] = useState(false);
  const [showPlayer, setShowPlayer] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tmdbData, setTmdbData] = useState<any>(null);
  const [tmdbLoading, setTmdbLoading] = useState(true);
  const [watchProgress, setWatchProgress] = useState<number>(0);
  const [movieDuration, setMovieDuration] = useState<number>(0);
  const playerRef = useRef<any>(null);
  const theme = useTheme();

  useEffect(() => {
    const loadTmdbData = async () => {
      try {
        setTmdbLoading(true);
        const data = await tmdbService.searchMovie(movie.name);
        setTmdbData(data);
      } catch (error) {
        console.error('Erro ao carregar dados do TMDB:', error);
      } finally {
        setTmdbLoading(false);
      }
    };
    
    loadTmdbData();
  }, [movie.name]);

  useEffect(() => {
    const loadWatchProgress = async () => {
      const watched = await dbService.getWatchedMovie(movie.id);
      if (watched) {
        setWatchProgress(watched.progress);
        setMovieDuration(watched.duration);
      }
    };
    
    loadWatchProgress();
  }, [movie.id]);

  const handleOpen = async () => {
    if (onClick) {
      onClick(movie);
      return;
    }
    
    setOpen(true);
    if (!tmdbData) {
      setLoading(true);
      const data = await tmdbService.searchMovie(movie.name);
      setTmdbData(data);
      setLoading(false);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setShowPlayer(false);
  };

  const handlePlay = async () => {
    if (!movie.url && !movie.direct_source) {
      const streamId = movie.id;
      if (!streamId) {
        console.error('ID do filme não disponível');
        return;
      }

      try {
        const iptvService = await createIPTVService();
        const movieUrl = iptvService.getStreamUrl(streamId, 'movie', movie.direct_source);
        movie.url = movieUrl;
        console.log('URL do filme construída:', movieUrl);
      } catch (error) {
        console.error('Erro ao obter URL do filme:', error);
        return;
      }
    }

    let finalUrl = movie.direct_source || movie.url;
    if (!finalUrl) {
      console.error('URL do filme não disponível');
      return;
    }

    // Usar URL direta sem proxy
    console.log('Reproduzindo filme com URL direta:', finalUrl);
    setShowPlayer(true);
  };

  const saveWatchProgress = async (progress: number, duration: number) => {
    try {
      await dbService.saveWatchedMovie({
        id: movie.id,
        name: movie.name,
        thumbnail: movie.thumbnail || '',
        cover: movie.cover,
        progress: progress,
        duration: duration,
        lastWatched: Date.now()
      });
      setWatchProgress(progress);
      setMovieDuration(duration);
    } catch (error) {
      console.error('Erro ao salvar progresso do filme:', error);
    }
  };

  const handleTimeUpdate = (currentTime: number, duration: number) => {
    // Save progress every 5 seconds
    if (currentTime % 5 < 1) {
      saveWatchProgress(currentTime, duration);
    }
  };

  const handleResume = () => {
    handlePlay();
    // Player will set the current time to watchProgress in its onReady callback
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}min`;
  };

  const getDirector = () => {
    if (!tmdbData?.credits?.crew) return '';
    const director = tmdbData.credits.crew.find((person: any) => person.job === 'Director');
    return director ? director.name : '';
  };

  const getCast = () => {
    if (!tmdbData?.credits?.cast) return '';
    return tmdbData.credits.cast
      .slice(0, 5)
      .map((actor: any) => actor.name)
      .join(', ');
  };

  const progressPercentage = movieDuration > 0 ? (watchProgress / movieDuration) * 100 : 0;

  const [isHovered, setIsHovered] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  
  // Função de teste para adicionar aos favoritos
  const toggleFavorite = (event: React.MouseEvent) => {
    event.stopPropagation();
    setIsFavorite(!isFavorite);
    // Implementação real seria com dbService
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        style={{ height: '100%', width: '100%' }}
      >
        <StyledCard>
          <CardActionArea onClick={handleOpen} sx={{ height: '100%' }}>
            <CardMedia
              component="img"
              image={movie.cover || movie.thumbnail || '/placeholder-cover.jpg'}
              alt={movie.name}
              sx={{ 
                height: '100%',
                objectFit: 'cover',
              }}
            />
            
            {/* Overlay com informações (sempre visível) */}
            <MovieOverlay className="movie-overlay">
              {(tmdbData?.vote_average || movie.rating) && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, marginBottom: 0.5 }}>
                  <StarIcon sx={{ color: '#FFD700', fontSize: '0.9rem' }} />
                  <Typography sx={{ fontWeight: 700, fontSize: '0.75rem', color: 'white' }}>
                    {tmdbData?.vote_average 
                      ? tmdbData.vote_average.toFixed(1) 
                      : movie.rating ? movie.rating.toFixed(1) : ''}
              </Typography>
            </Box>
          )}
              <Typography variant="subtitle2" component="div" sx={{ 
                    color: 'white',
                    fontWeight: 'bold',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}>
                  {movie.name}
              </Typography>
              {movie.year && (
                <Typography variant="caption" sx={{ color: 'white', opacity: 0.8 }}>
                  {movie.year}
                </Typography>
              )}
            </MovieOverlay>
            
            {/* Botão de play quando hover */}
            <PlayButton className="play-button" />
            
            {/* Barra de progresso para conteúdo assistido */}
            {progressPercentage > 0 && progressPercentage < 100 && (
              <StyledLinearProgress variant="determinate" value={progressPercentage} />
            )}
          </CardActionArea>
        </StyledCard>
      </motion.div>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundImage: tmdbData?.backdrop_path 
              ? `linear-gradient(to bottom, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.95) 100%), url(${tmdbService.getImageUrl(tmdbData.backdrop_path, 'backdrop')})`
              : 'none',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundColor: '#121212',
            minHeight: '80vh',
            borderRadius: '12px',
            overflow: 'hidden'
          }
        }}
      >
        {tmdbLoading ? (
          <DialogContent sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
            <CircularProgress />
          </DialogContent>
        ) : !showPlayer ? (
          <>
            <DialogTitle 
              sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'flex-start',
                px: 3,
                pt: 3
              }}
            >
              <Box>
                <Typography variant="h4" component="h2" sx={{ fontWeight: 700, mb: 1 }}>
                  {movie.name}
                </Typography>
                {tmdbData?.tagline && (
                  <Typography variant="subtitle1" sx={{ color: 'rgba(255,255,255,0.7)', mb: 2, fontStyle: 'italic' }}>
                    {tmdbData.tagline}
                  </Typography>
                )}
              </Box>
              <IconButton onClick={handleClose} sx={{ color: 'white' }}>
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ px: 3, pb: 3 }}>
              <Stack spacing={4}>
                <Box sx={{ display: 'flex', gap: 3, flexWrap: { xs: 'wrap', md: 'nowrap' } }}>
                  <Box 
                    component="img" 
                    src={movie.cover || movie.thumbnail} 
                    alt={movie.name}
                    sx={{ 
                      width: { xs: '100%', sm: '300px' },
                      maxHeight: '450px',
                      objectFit: 'cover',
                      borderRadius: '8px',
                      boxShadow: '0 10px 30px rgba(0,0,0,0.5)'
                    }}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Stack spacing={3}>
                      <Box>
                        <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                          {watchProgress > 0 && (
                            <Button 
                              variant="contained" 
                              color="primary" 
                              startIcon={<PlayArrowIcon />}
                              onClick={handleResume}
                              sx={{ 
                                textTransform: 'none',
                                fontWeight: 600,
                                borderRadius: '28px',
                                px: 3
                              }}
                            >
                              Continue ({Math.floor(watchProgress / 60)}:{String(Math.floor(watchProgress % 60)).padStart(2, '0')})
                            </Button>
                          )}
                          <Button 
                            variant="contained" 
                            color="primary" 
                            startIcon={<PlayArrowIcon />}
                            onClick={handlePlay}
                            sx={{ 
                              textTransform: 'none',
                              fontWeight: 600,
                              borderRadius: '28px',
                              px: 3
                            }}
                          >
                            {watchProgress > 0 ? 'Reiniciar' : 'Assistir'}
                          </Button>
                        </Stack>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2, flexWrap: 'wrap' }}>
                          {tmdbData?.vote_average > 0 && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Rating
                                value={tmdbData.vote_average / 2}
                                precision={0.5}
                                readOnly
                                sx={{
                                  mr: 1,
                                  '& .MuiRating-iconFilled': {
                                    color: '#FFD700'
                                  },
                                  '& .MuiRating-iconEmpty': {
                                    color: 'rgba(255,215,0,0.3)'
                                  }
                                }}
                              />
                              <Typography sx={{ color: '#FFD700' }}>
                                {tmdbData.vote_average.toFixed(1)}
                              </Typography>
                            </Box>
                          )}
                          
                          {tmdbData?.release_date && (
                            <Chip 
                              label={new Date(tmdbData.release_date).getFullYear()} 
                              sx={{ 
                                backgroundColor: 'rgba(255,255,255,0.1)',
                                color: 'white'
                              }} 
                            />
                          )}
                          
                          {tmdbData?.runtime > 0 && (
                            <Chip 
                              label={formatDuration(tmdbData.runtime)} 
                              sx={{ 
                                backgroundColor: 'rgba(255,255,255,0.1)',
                                color: 'white'
                              }} 
                            />
                          )}
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          {tmdbData?.genres && (
                            <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                              {tmdbData.genres.map((genre: any) => (
                                <Chip
                                  key={genre.id}
                                  label={genre.name}
                                  sx={{
                                    backgroundColor: 'rgba(255,255,255,0.08)',
                                    color: 'white',
                                    '&:hover': {
                                      backgroundColor: 'rgba(255,255,255,0.12)'
                                    }
                                  }}
                                />
                              ))}
                            </Stack>
                          )}
                        </Box>
                      </Box>
                      
                      {tmdbData?.overview && (
                        <Box>
                          <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>Sinopse</Typography>
                          <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                            {tmdbData.overview}
                          </Typography>
                        </Box>
                      )}
                      
                      <Box>
                        {getDirector() && (
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" sx={{ color: 'rgba(255,255,255,0.6)' }}>
                              Diretor
                            </Typography>
                            <Typography variant="body1">
                              {getDirector()}
                            </Typography>
                          </Box>
                        )}
                        
                        {getCast() && (
                          <Box>
                            <Typography variant="subtitle2" sx={{ color: 'rgba(255,255,255,0.6)' }}>
                              Elenco
                            </Typography>
                            <Typography variant="body1">
                              {getCast()}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Stack>
                  </Box>
                </Box>
              </Stack>
            </DialogContent>
          </>
        ) : (
          <Box sx={{ position: 'relative', pt: '56.25%', /* 16:9 aspect ratio */ height: 0 }}>
            <VideoPlayer
              ref={playerRef}
              url={(() => {
                const rawUrl = movie.direct_source || movie.url || '';
                if (!rawUrl) return '';
                return getSafeUrl(rawUrl);
              })()}
              title={movie.name}
              autoPlay
              initialTime={watchProgress}
              onTimeUpdate={handleTimeUpdate}
              onClose={handleClose}
              style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
            />
          </Box>
        )}
      </Dialog>
    </>
  );
};

export default MovieCard;