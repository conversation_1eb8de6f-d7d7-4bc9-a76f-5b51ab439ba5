.playlist-viewer {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.playlist-header {
  margin-bottom: 20px;
}

.playlist-header h1 {
  font-size: 24px;
  margin: 0;
}

.playlist-header p {
  color: #666;
}

.playlist-stats {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #999;
}

.playlist-controls {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.url-form {
  flex: 1;
  min-width: 300px;
  display: flex;
  gap: 0.75rem;
}

.url-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 150ms ease;
  background: #f8fafc;
}

.url-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.load-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 150ms ease;
}

.load-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

.file-upload {
  position: relative;
}

.file-upload input[type="file"] {
  display: none;
}

.file-upload-label {
  padding: 0.75rem 1.5rem;
  background: #22c55e;
  color: white;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 150ms ease;
  display: inline-block;
}

.file-upload-label:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.25);
}

.filters {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-input {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  min-width: 200px;
  background: #f8fafc;
  transition: all 150ms ease;
}

.search-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filter-select {
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: #f8fafc url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") no-repeat right 0.5rem center/1.5em 1.5em;
  appearance: none;
  transition: all 150ms ease;
}

.filter-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Saved Playlists */
.saved-playlists {
  margin-bottom: 30px;
}

.saved-playlists h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.saved-playlists-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.saved-playlist-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.saved-playlist-item h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
}

.saved-playlist-item p {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #666;
}

.saved-playlist-actions {
  display: flex;
  gap: 10px;
}

.load-saved-button {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.load-saved-button:hover {
  background-color: #0056b3;
}

.delete-button {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-button:hover {
  background-color: #c82333;
}

/* Playlist Items */
.playlist-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.playlist-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 250ms ease;
  cursor: pointer;
}

.playlist-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.item-logo {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.item-info {
  padding: 1.25rem;
}

.item-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
}

.item-group {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.5rem;
  display: block;
}

.item-type {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.item-type.channel {
  background: #dbeafe;
  color: #1d4ed8;
}

.item-type.movie {
  background: #fce7f3;
  color: #be185d;
}

.item-type.series {
  background: #f3e8ff;
  color: #7e22ce;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-size: 1rem;
}

.error {
  background: #fef2f2;
  color: #ef4444;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
}

.playlist-viewer-error {
  color: red;
  text-align: center;
}

@media (max-width: 640px) {
  .playlist-viewer {
    padding: 1rem;
  }

  .playlist-controls {
    padding: 1rem;
  }

  .url-form {
    min-width: 100%;
  }

  .filters {
    width: 100%;
  }

  .search-input,
  .filter-select {
    width: 100%;
  }
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem;
}

.pagination-button {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  color: #1e293b;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 150ms ease;
  cursor: pointer;
}

.pagination-button:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-number {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  background: white;
  color: #1e293b;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 150ms ease;
  cursor: pointer;
}

.pagination-number:hover:not(.active) {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.pagination-number.active {
  background: #2563eb;
  border-color: #2563eb;
  color: white;
}

.pagination-ellipsis {
  color: #64748b;
  font-weight: 500;
  padding: 0 0.25rem;
}

@media (max-width: 640px) {
  .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }

  .pagination-numbers {
    order: -1;
  }

  .pagination-button {
    width: 100%;
  }
}
