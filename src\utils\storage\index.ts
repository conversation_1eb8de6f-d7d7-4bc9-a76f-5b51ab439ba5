import { indexedDBStorage } from './indexedDB';
import {
  Channel,
  Playlist,
  WatchHistoryItem,
  FavoriteChannel,
  UserPreferences,
  ChannelInput,
  PlaylistInput,
  WatchHistoryInput,
  FavoriteInput,
  PreferencesInput
} from '../../types/Channel';

interface SearchOptions {
  searchInName?: boolean;
  searchInGroup?: boolean;
  searchInCategory?: boolean;
  caseSensitive?: boolean;
}

class Storage {
  private isOnline: boolean = navigator.onLine;
  
  constructor() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  private handleOnline = async () => {
    this.isOnline = true;
  };

  private handleOffline = () => {
    this.isOnline = false;
  };

  // Channel operations
  async saveChannel(channel: ChannelInput): Promise<Channel> {
    const newChannel: Channel = {
      ...channel,
      id: crypto.randomUUID(),
      isFavorite: false,
      lastWatched: undefined,
      watchProgress: 0
    };

    await indexedDBStorage.saveChannel(newChannel);
    return newChannel;
  }

  async getChannel(id: string): Promise<Channel | undefined> {
    const localChannel = await indexedDBStorage.getChannel(id);
    return localChannel;
  }

  async getChannels(playlistId?: string): Promise<Channel[]> {
    const channels = playlistId 
      ? await indexedDBStorage.getChannelsByPlaylist(playlistId)
      : await indexedDBStorage.getChannels();

    // Enrich with favorite status
    const favorites = await this.getFavorites();
    const favoriteIds = new Set(favorites.map(f => f.id));

    return channels.map(channel => ({
      ...channel,
      isFavorite: favoriteIds.has(channel.id)
    }));
  }

  async getChannelGroups(playlistId: string): Promise<string[]> {
    const channels = await this.getChannels(playlistId);
    const groups = new Set<string>();
    
    channels.forEach(channel => {
      if (channel.group) {
        groups.add(channel.group);
      }
    });
    
    return Array.from(groups).sort();
  }

  async searchChannels(
    query: string,
    playlistId?: string,
    options: SearchOptions = {}
  ): Promise<Channel[]> {
    const {
      searchInName = true,
      searchInGroup = true,
      searchInCategory = true,
      caseSensitive = false
    } = options;

    const channels = await this.getChannels(playlistId);
    const searchQuery = caseSensitive ? query : query.toLowerCase();

    return channels.filter(channel => {
      let matchFound = false;

      if (searchInName) {
        const name = caseSensitive ? channel.name : channel.name.toLowerCase();
        matchFound = matchFound || name.includes(searchQuery);
      }

      if (searchInGroup && channel.group) {
        const group = caseSensitive ? channel.group : channel.group.toLowerCase();
        matchFound = matchFound || group.includes(searchQuery);
      }

      if (searchInCategory && channel.category) {
        const category = caseSensitive ? channel.category : channel.category.toLowerCase();
        matchFound = matchFound || category.includes(searchQuery);
      }

      return matchFound;
    });
  }

  // Playlist operations
  async savePlaylist(playlist: PlaylistInput): Promise<Playlist> {
    const newPlaylist: Playlist = {
      ...playlist,
      id: crypto.randomUUID(),
      lastUpdated: new Date().toISOString(),
      channelCount: 0
    };

    await indexedDBStorage.savePlaylist(newPlaylist);
    return newPlaylist;
  }

  async getPlaylist(id: string): Promise<Playlist | undefined> {
    const localPlaylist = await indexedDBStorage.getPlaylist(id);
    return localPlaylist;
  }

  async getPlaylists(): Promise<Playlist[]> {
    return indexedDBStorage.getPlaylists();
  }

  async deletePlaylist(id: string): Promise<void> {
    await indexedDBStorage.deletePlaylist(id);
  }

  // Watch history operations (local only)
  async addToHistory(item: WatchHistoryInput): Promise<void> {
    return indexedDBStorage.addToHistory(item);
  }

  async updateWatchProgress(
    channelId: string,
    position: number,
    duration: number
  ): Promise<void> {
    return indexedDBStorage.updateWatchProgress(channelId, position, duration);
  }

  async getHistory(limit?: number): Promise<WatchHistoryItem[]> {
    return indexedDBStorage.getHistory(limit);
  }

  async clearHistory(): Promise<void> {
    return indexedDBStorage.clearHistory();
  }

  // Favorites operations (local only)
  async addToFavorites(channel: Channel): Promise<void> {
    return indexedDBStorage.addToFavorites({
      id: channel.id,
      name: channel.name,
      url: channel.url,
      playlistId: channel.playlistId,
      addedAt: new Date().toISOString(),
      notes: ''
    });
  }

  async removeFromFavorites(channelId: string): Promise<void> {
    return indexedDBStorage.removeFromFavorites(channelId);
  }

  async getFavorites(playlistId?: string): Promise<FavoriteChannel[]> {
    return indexedDBStorage.getFavorites(playlistId);
  }

  async isFavorite(channelId: string): Promise<boolean> {
    const favorites = await indexedDBStorage.getFavorites();
    return favorites.some(f => f.id === channelId);
  }

  async getPreferences(): Promise<UserPreferences> {
    return indexedDBStorage.getPreferences();
  }

  async savePreferences(preferences: PreferencesInput): Promise<void> {
    return indexedDBStorage.savePreferences(preferences);
  }

  // Authentication stubs - not connected to any backend
  async login(_email: string, _password: string): Promise<void> {
    console.log('Authentication feature not implemented');
    return Promise.resolve();
  }

  async logout(): Promise<void> {
    console.log('Authentication feature not implemented');
    return Promise.resolve();
  }

  isAuthenticated(): boolean {
    return false; // Authentication functionality is not implemented
  }

  async clearAllData(): Promise<void> {
    await indexedDBStorage.clearAllData();
  }
}

export const dbService = new Storage();
