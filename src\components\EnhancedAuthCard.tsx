import React from 'react';
import { 
  Card,
  CardContent,
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Slide
} from '@mui/material';
import { motion } from 'framer-motion';
import { useSpring, animated } from '@react-spring/web';

interface EnhancedAuthCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
}

const AnimatedCard = animated(Card);
const MotionBox = motion.create(Box);

const EnhancedAuthCard: React.FC<EnhancedAuthCardProps> = ({
  children,
  title = "Neko TV",
  subtitle = "Acesso ao Sistema",
  showLogo = true
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isDarkMode = theme.palette.mode === 'dark';

  // Animação do card principal
  const cardSpring = useSpring({
    from: { 
      opacity: 0, 
      transform: 'translateY(50px) scale(0.9)',
      filter: 'blur(10px)'
    },
    to: { 
      opacity: 1, 
      transform: 'translateY(0px) scale(1)',
      filter: 'blur(0px)'
    },
    config: { 
      tension: 200, 
      friction: 20,
      duration: 800
    },
    delay: 300
  });

  // Animação do logo
  const logoSpring = useSpring({
    from: { 
      opacity: 0, 
      transform: 'translateY(-30px) rotate(-10deg) scale(0.8)',
    },
    to: { 
      opacity: 1, 
      transform: 'translateY(0px) rotate(0deg) scale(1)',
    },
    config: { 
      tension: 300, 
      friction: 25 
    },
    delay: 100
  });

  // Efeito de brilho no card
  const glowAnimation = useSpring({
    from: { opacity: 0.3 },
    to: { opacity: 0.8 },
    config: { duration: 2000 },
    loop: { reverse: true }
  });

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        zIndex: 1,
        padding: isMobile ? 1 : 2,
        width: '100%'
      }}
    >
      {/* Logo animado - mais compacto */}
      {showLogo && (
        <animated.div style={logoSpring}>
          <MotionBox
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              type: "spring",
              stiffness: 200,
              damping: 15,
              delay: 0.2
            }}
            whileHover={{ 
              scale: 1.05,
              rotate: [0, -3, 3, 0],
              transition: { duration: 0.4 }
            }}
            sx={{
              mb: 1.5,
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Box
              sx={{
                width: isMobile ? 50 : 60,
                height: isMobile ? 50 : 60,
                borderRadius: '50%',
                background: `linear-gradient(135deg, 
                  ${theme.palette.primary.main}, 
                  ${theme.palette.secondary.main})`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                boxShadow: `0 0 30px ${alpha(theme.palette.primary.main, 0.4)}`,
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  inset: -3,
                  background: `linear-gradient(135deg, 
                    ${theme.palette.primary.main}, 
                    ${theme.palette.secondary.main})`,
                  borderRadius: '50%',
                  opacity: 0.2,
                  filter: 'blur(10px)',
                  animation: 'pulse 4s ease-in-out infinite'
                },
                '@keyframes pulse': {
                  '0%, 100%': {
                    transform: 'scale(0.95)',
                    opacity: 0.2
                  },
                  '50%': {
                    transform: 'scale(1.05)',
                    opacity: 0.4
                  }
                }
              }}
            >
              <img 
                src="/logo-neko-tv.png" 
                alt="Neko TV Logo" 
                style={{ 
                  width: '65%', 
                  height: '65%', 
                  objectFit: 'contain',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                  position: 'relative',
                  zIndex: 1
                }} 
              />
            </Box>
          </MotionBox>
        </animated.div>
      )}

      {/* Card principal com animações - mais compacto */}
      <AnimatedCard
        style={cardSpring}
        sx={{
          width: '100%',
          maxWidth: isMobile ? '340px' : '380px',
          borderRadius: 2,
          background: isDarkMode
            ? `linear-gradient(135deg,
                ${alpha('#1a1f35', 0.95)} 0%,
                ${alpha('#0f172a', 0.95)} 100%)`
            : `linear-gradient(135deg,
                ${alpha('#ffffff', 0.95)} 0%,
                ${alpha('#f8fafc', 0.95)} 100%)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
          boxShadow: isDarkMode
            ? `0 15px 30px -8px rgba(0,0,0,0.4),
               0 0 0 1px ${alpha(theme.palette.primary.main, 0.08)},
               inset 0 1px 0 ${alpha('#ffffff', 0.08)}`
            : `0 15px 30px -8px rgba(0,0,0,0.15),
               0 0 0 1px ${alpha(theme.palette.primary.main, 0.08)},
               inset 0 1px 0 ${alpha('#ffffff', 0.6)}`,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: `linear-gradient(90deg,
              ${theme.palette.primary.main},
              ${theme.palette.secondary.main},
              ${theme.palette.primary.main})`,
            backgroundSize: '200% 100%',
            animation: 'shimmer 4s ease-in-out infinite'
          },
          '@keyframes shimmer': {
            '0%': { backgroundPosition: '-200% 0' },
            '100%': { backgroundPosition: '200% 0' }
          }
        }}
      >
        {/* Efeito de brilho interno */}
        <animated.div
          style={{
            ...glowAnimation,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `radial-gradient(circle at 50% 0%, 
              ${alpha(theme.palette.primary.main, 0.1)} 0%, 
              transparent 50%)`,
            pointerEvents: 'none',
            zIndex: 0
          }}
        />

        <CardContent sx={{ p: isMobile ? 1.5 : 2, position: 'relative', zIndex: 1 }}>
          {/* Cabeçalho - mais compacto */}
          <Fade in timeout={1000}>
            <Box sx={{ textAlign: 'center', mb: 1.5 }}>
              <Typography
                variant={isMobile ? "h6" : "h5"}
                sx={{
                  fontWeight: 700,
                  background: `linear-gradient(135deg,
                    ${theme.palette.primary.main},
                    ${theme.palette.secondary.main})`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 0.5,
                  fontSize: isMobile ? '1.1rem' : '1.3rem',
                  textShadow: isDarkMode
                    ? `0 0 15px ${alpha(theme.palette.primary.main, 0.3)}`
                    : 'none'
                }}
              >
                {title}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.secondary,
                  fontWeight: 500,
                  opacity: 0.7,
                  fontSize: '0.8rem'
                }}
              >
                {subtitle}
              </Typography>
            </Box>
          </Fade>

          {/* Conteúdo principal */}
          <Slide direction="up" in timeout={800}>
            <Box>
              {children}
            </Box>
          </Slide>
        </CardContent>

        {/* Decoração inferior */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: `linear-gradient(90deg, 
              transparent, 
              ${alpha(theme.palette.primary.main, 0.5)}, 
              transparent)`,
          }}
        />
      </AnimatedCard>

      {/* Elementos decorativos flutuantes - reduzidos e mais sutis */}
      {[...Array(2)].map((_, i) => (
        <MotionBox
          key={i}
          initial={{
            opacity: 0,
            x: Math.random() * 100 - 50,
            y: Math.random() * 100 - 50
          }}
          animate={{
            opacity: [0.02, 0.08, 0.02],
            x: Math.random() * 200 - 100,
            y: Math.random() * 200 - 100,
            scale: [1, 1.05, 1]
          }}
          transition={{
            duration: Math.random() * 20 + 20,
            repeat: Infinity,
            repeatType: "reverse",
            delay: Math.random() * 10
          }}
          sx={{
            position: 'absolute',
            width: Math.random() * 3 + 2,
            height: Math.random() * 3 + 2,
            borderRadius: '50%',
            background: `linear-gradient(135deg,
              ${theme.palette.primary.main},
              ${theme.palette.secondary.main})`,
            filter: 'blur(2px)',
            zIndex: -1,
            pointerEvents: 'none'
          }}
        />
      ))}
    </Box>
  );
};

export default EnhancedAuthCard;