/* Custom slider styles for video player */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: rgba(255, 255, 255, 0.2);
  height: 4px;
  border-radius: 2px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #0ea5e9;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #0284c7;
  transform: scale(1.2);
}

.slider::-moz-range-track {
  background: rgba(255, 255, 255, 0.2);
  height: 4px;
  border-radius: 2px;
  border: none;
}

.slider::-moz-range-thumb {
  background: #0ea5e9;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  background: #0284c7;
  transform: scale(1.2);
}

/* Progress bar specific styles */
.slider[type="range"]::-webkit-slider-thumb {
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.3);
}

.slider[type="range"]::-moz-range-thumb {
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.3);
}

/* Volume slider styles */
.slider.volume::-webkit-slider-track {
  height: 2px;
}

.slider.volume::-moz-range-track {
  height: 2px;
}

.slider.volume::-webkit-slider-thumb {
  height: 12px;
  width: 12px;
}

.slider.volume::-moz-range-thumb {
  height: 12px;
  width: 12px;
}