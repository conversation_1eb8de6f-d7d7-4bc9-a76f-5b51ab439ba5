import React, { useState, useEffect } from 'react';
import { Box, Button, Alert, Snackbar, Typography, IconButton } from '@mui/material';
import { Download, Close, PhoneAndroid, GetApp } from '@mui/icons-material';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const InstallPWA: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [userAgent, setUserAgent] = useState('');

  useEffect(() => {
    setUserAgent(navigator.userAgent);
    
    // Verificar se já está instalado
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isIOSStandalone = (window.navigator as any).standalone === true;
    setIsInstalled(isStandalone || isIOSStandalone);

    // Listener para o evento beforeinstallprompt
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('beforeinstallprompt event fired');
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
    };

    // Listener para quando o app é instalado
    const handleAppInstalled = () => {
      console.log('PWA was installed');
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      setShowInstructions(true);
      return;
    }

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      console.error('Error during installation:', error);
      setShowInstructions(true);
    }
  };

  const getDeviceInstructions = () => {
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);
    const isSamsung = /SamsungBrowser/.test(userAgent);
    
    if (isIOS) {
      return {
        title: "Instalar no iOS",
        steps: [
          "1. Toque no botão de compartilhar (quadrado com seta)",
          "2. Role para baixo e toque em 'Adicionar à Tela de Início'",
          "3. Toque em 'Adicionar' no canto superior direito"
        ]
      };
    }
    
    if (isSamsung) {
      return {
        title: "Instalar no Samsung Internet",
        steps: [
          "1. Toque no menu (três linhas) no canto inferior direito",
          "2. Toque em 'Adicionar página a'",
          "3. Selecione 'Tela inicial'"
        ]
      };
    }
    
    if (isAndroid) {
      return {
        title: "Instalar no Android",
        steps: [
          "1. Toque no menu (três pontos) no Chrome",
          "2. Toque em 'Adicionar à tela inicial'",
          "3. Toque em 'Adicionar'"
        ]
      };
    }
    
    return {
      title: "Instalar no Navegador",
      steps: [
        "1. Procure pelo ícone de instalação na barra de endereços",
        "2. Ou acesse o menu do navegador",
        "3. Procure por 'Instalar app' ou 'Adicionar à tela inicial'"
      ]
    };
  };

  // Não mostrar se já estiver instalado
  if (isInstalled) {
    return null;
  }

  const instructions = getDeviceInstructions();

  return (
    <Box sx={{ mb: 2 }}>
      {isInstallable ? (
        <Button
          variant="contained"
          startIcon={<Download />}
          onClick={handleInstallClick}
          fullWidth
          sx={{
            bgcolor: 'primary.main',
            color: 'white',
            '&:hover': {
              bgcolor: 'primary.dark',
            }
          }}
        >
          Instalar App
        </Button>
      ) : (
        <Button
          variant="outlined"
          startIcon={<PhoneAndroid />}
          onClick={() => setShowInstructions(true)}
          fullWidth
          sx={{
            borderColor: 'primary.main',
            color: 'primary.main',
            '&:hover': {
              bgcolor: 'primary.main',
              color: 'white',
            }
          }}
        >
          Como Instalar
        </Button>
      )}

      <Snackbar
        open={showInstructions}
        onClose={() => setShowInstructions(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="info"
          action={
            <IconButton
              size="small"
              aria-label="close"
              color="inherit"
              onClick={() => setShowInstructions(false)}
            >
              <Close fontSize="small" />
            </IconButton>
          }
          sx={{ width: '100%', maxWidth: 400 }}
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
            {instructions.title}
          </Typography>
          {instructions.steps.map((step, index) => (
            <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
              {step}
            </Typography>
          ))}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default InstallPWA; 