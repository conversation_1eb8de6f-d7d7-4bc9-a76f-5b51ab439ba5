import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  useTheme,
  useMediaQuery,
  alpha,
  Backdrop,
  Fade,
  IconButton,
  Tooltip,
  Divider,
  Alert,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Paper,
  CircularProgress,
  Checkbox
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import DeviceService, { deviceService } from '../services/deviceService';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import RefreshIcon from '@mui/icons-material/Refresh';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import LockIcon from '@mui/icons-material/Lock';
import DoneIcon from '@mui/icons-material/Done';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import PersonIcon from '@mui/icons-material/Person';
import LinkIcon from '@mui/icons-material/Link';
import HistoryIcon from '@mui/icons-material/History';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import DeleteIcon from '@mui/icons-material/Delete';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import InfoIcon from '@mui/icons-material/Info';
import { useNavigate } from 'react-router-dom';
import { dbService } from '../services/dbService';
import { createIPTVService } from '../services/iptvService';

// Importar nossos componentes personalizados
import ParticlesBackground from '../components/ParticlesBackground';
import EnhancedAuthCard from '../components/EnhancedAuthCard';
import EnhancedInput from '../components/EnhancedInput';
import EnhancedButton from '../components/EnhancedButton';
import WelcomeTvDisplay from '../components/WelcomeTvDisplay';

const MotionBox = motion.create(Box);

const EnhancedAccess: React.FC<{ setUserInfo?: any, setServerInfo?: any }> = ({ setUserInfo, setServerInfo }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [deviceId, setDeviceId] = useState<string>('');
  const [copied, setCopied] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');
  const [isError, setIsError] = useState<boolean>(false);
  const [showManualLogin, setShowManualLogin] = useState<boolean>(false);
  const [showSavedConnections, setShowSavedConnections] = useState<boolean>(false);
  const [manualUrl, setManualUrl] = useState<string>('');
  const [manualUsername, setManualUsername] = useState<string>('');
  const [manualPassword, setManualPassword] = useState<string>('');
  const [enableAutoLoginOnSubmit, setEnableAutoLoginOnSubmit] = useState<boolean>(true);
  const [loginSuccess, setLoginSuccess] = useState<boolean>(false);
  const [savedConnections, setSavedConnections] = useState<any[]>([]);

  useEffect(() => {
    // Função assíncrona para inicialização
    const initializeDevice = async () => {
      // Obter o ID do dispositivo (aguardar inicialização completa)
      try {
        const id = await DeviceService.getDeviceIdAsync();
        setDeviceId(id);
      } catch (error) {
        console.error('Erro ao obter ID do dispositivo:', error);
        setDeviceId('ID não disponível');
      }
      
      // Verificar se estamos em modo de troca de conta ou logout
      const currentUrl = window.location.href;
      const urlObj = new URL(currentUrl);
      const isSwitchingAccount = urlObj.searchParams.has('switch');
      const isLoggingOut = urlObj.searchParams.has('logout');

    // Debug: Log para verificar o estado
    console.log('EnhancedAccess - Estado atual:', {
      isSwitchingAccount,
      isLoggingOut,
      url: currentUrl
    });
      
      // Se estiver fazendo logout, limpar qualquer flag de login automático
      if (isLoggingOut) {
        // Remover a flag de logged_out se existir
        localStorage.removeItem('logged_out');
      }
      
      // Carregar conexões salvas
      try {
        setMessage('Verificando conexões salvas...');
        setIsError(false);
        const connections = await dbService.getAllConnections();
        if (connections && connections.length > 0) {
          setSavedConnections(connections);
          
          // Verificar se há uma conexão com autoLogin ativado
          const autoLoginConnection = connections.find(conn => conn.autoLogin === true);
          
          // Só fazer login automático se não estiver trocando de conta ou deslogando
          if (autoLoginConnection && !isSwitchingAccount && !isLoggingOut) {
            // Mostrar mensagem de tentativa de login automático
            try {
              const urlObj = new URL(autoLoginConnection.url);
              setMessage(`Tentando login automático com ${urlObj.hostname}...`);
            } catch {
              setMessage('Tentando login automático...');
            }
            setIsError(false);
            setIsChecking(true);
            
            // Tentar conectar automaticamente, preservando o estado de autoLogin
            await connectWithSavedConnection(autoLoginConnection, true);
            return; // Sair da função se o login automático foi tentado
          } else if (isSwitchingAccount || isLoggingOut) {
            // Se estiver trocando de conta ou deslogando, mostrar as conexões salvas
            setShowSavedConnections(true);
            
            if (isLoggingOut) {
              setMessage('Você foi desconectado. Selecione uma conta para conectar.');
            } else {
              setMessage('Selecione uma conta para conectar');
            }
          }
        }
      } catch (error) {
        console.error('Erro ao carregar conexões salvas:', error);
        setMessage('Erro ao carregar conexões salvas');
        setIsError(true);
      }

      // Se não houver conexão com autoLogin, verificar licença
      setMessage('Verificando licença...');
      setIsError(false);
      const savedLicense = await deviceService.loadLicense();
      if (savedLicense && savedLicense.active) {
        checkLicense();
      } else {
        setMessage('');
      }
    };
    
    // Executar a função de inicialização
    initializeDevice();
  }, []);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(deviceId).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }).catch(err => {
      console.error('Erro ao copiar para área de transferência:', err);
    });
  };

  const checkLicense = async () => {
    setIsChecking(true);
    setMessage('Verificando licença...');
    setIsError(false);

    try {
      const result = await deviceService.checkActivation();

      if (result && result.valid) {
        setMessage('Licença encontrada! Redirecionando...');
        if (result.iptvUrl) {
          const iptvUrl = result.iptvUrl;

          let username = '';
          let password = '';
          let serverBaseUrl = '';

          try {
            const urlObj = new URL(iptvUrl);
            const params = new URLSearchParams(urlObj.search);
            if (params.has('username')) username = params.get('username') || '';
            if (params.has('password')) password = params.get('password') || '';
            serverBaseUrl = urlObj.origin;
          } catch (error) {
            console.error('Erro ao extrair credenciais da URL:', error);
            serverBaseUrl = iptvUrl;
          }

          await dbService.setConnection({
            url: iptvUrl,
            username: username,
            password: password,
            type: 'url',
            format: 'm3u',
            autoLogin: true,
            lastUsed: Date.now()
          });

          if (setServerInfo) {
            setServerInfo({
              url: serverBaseUrl,
              username: username,
              password: password,
              name: result.name || 'Neko TV'
            });
          }

          try {
            const iptvService = await createIPTVService();
            const userInfoResponse = await iptvService.getUserInfo();

            if (userInfoResponse && userInfoResponse.user_info && setUserInfo) {
              setUserInfo(userInfoResponse.user_info);
            }
          } catch (iptvError) {
            console.error('Erro ao obter informações do servidor IPTV:', iptvError);
          }

          setTimeout(() => {
            navigate('/content');
          }, 1500);
        } else {
          setMessage('Licença válida, mas sem URL configurada.');
          setIsError(true);
        }
      } else {
        setMessage(result.message || 'Nenhuma licença encontrada para este dispositivo.');
        setIsError(true);
      }
    } catch (error) {
      console.error('Erro ao verificar licença:', error);
      setMessage('Erro ao verificar licença. Tente novamente.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };



  const handleManualLogin = async () => {
    setIsChecking(true);
    setMessage('Conectando...');
    setIsError(false);

    try {
      if (!manualUrl || manualUrl.trim().length === 0) {
        setMessage('Por favor, insira uma URL válida.');
        setIsError(true);
        return;
      }

      // Se o login automático estiver habilitado, desativar para todas as outras conexões
      if (enableAutoLoginOnSubmit) {
        const connections = await dbService.getAllConnections();
        for (const conn of connections) {
          if (conn.autoLogin) {
            await dbService.setConnection({
              ...conn,
              autoLogin: false
            });
          }
        }
      }

      await dbService.setConnection({
        url: manualUrl.trim(),
        username: manualUsername || '',
        password: manualPassword || '',
        type: 'url',
        format: 'm3u',
        autoLogin: enableAutoLoginOnSubmit,
        lastUsed: Date.now()
      });

      const iptvService = await createIPTVService();
      const userInfo = await iptvService.getUserInfo();

      if (userInfo && userInfo.user_info) {
        let successMessage = 'Conectado com sucesso!';
        if (enableAutoLoginOnSubmit) {
          successMessage += ' Login automático ativado.';
        }
        setMessage(successMessage + ' Redirecionando...');
        setLoginSuccess(true);

        if (setUserInfo) {
          setUserInfo(userInfo.user_info);
        }

        if (setServerInfo) {
          setServerInfo({
            url: manualUrl.trim(),
            username: manualUsername || '',
            password: manualPassword || '',
            name: userInfo.user_info.username || 'Neko TV'
          });
        }

        setTimeout(() => {
          navigate('/content');
        }, 2000);
      } else {
        setMessage('Falha na conexão. Verifique os dados e tente novamente.');
        setIsError(true);
      }
    } catch (error) {
      console.error('Erro no login manual:', error);
      setMessage('Erro ao tentar conectar. Verifique a URL e as credenciais.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };
  
  const connectWithSavedConnection = async (connection: any, preserveAutoLogin: boolean = false) => {
    setIsChecking(true);
    setIsError(false);
    
    // Mostrar mensagem com o nome do servidor
    try {
      const urlObj = new URL(connection.url);
      setMessage(`Conectando a ${urlObj.hostname}...`);
    } catch {
      setMessage('Conectando ao servidor...');
    }

    try {
      // Verificar se estamos em modo de troca de conta
      const currentUrl = window.location.href;
      const urlObj = new URL(currentUrl);
      const isSwitchingAccount = urlObj.searchParams.has('switch');
      
      // Atualizar a conexão atual com a selecionada
      // Se estiver trocando de conta, preservar o autoLogin original da conexão
      // Se não estiver trocando, manter o comportamento original
      await dbService.setConnection({
        ...connection,
        lastUsed: Date.now(), // Atualizar o timestamp de último uso
        autoLogin: preserveAutoLogin ? connection.autoLogin : (isSwitchingAccount ? connection.autoLogin : true) // Se trocando conta, manter autoLogin original; se não, definir como true
      });

      setMessage('Verificando credenciais...');
      const iptvService = await createIPTVService();
      const userInfo = await iptvService.getUserInfo();

      if (userInfo && userInfo.user_info) {
        setMessage('Credenciais verificadas com sucesso! Redirecionando...');
        setLoginSuccess(true);

        if (setUserInfo) {
          setUserInfo(userInfo.user_info);
        }

        if (setServerInfo) {
          setServerInfo({
            url: connection.url,
            username: connection.username || '',
            password: connection.password || '',
            name: userInfo.user_info.username || 'Neko TV'
          });
        }

        setTimeout(() => {
          // Verificar se estamos em modo de troca de conta
          const currentUrl = window.location.href;
          const urlObj = new URL(currentUrl);
          const isSwitchingAccount = urlObj.searchParams.has('switch');

          // Se estiver trocando de conta, mostrar as conexões salvas novamente
          // em vez de redirecionar para a tela principal
          if (isSwitchingAccount) {
            // Limpar mensagem e mostrar as conexões salvas
            setMessage('Login bem-sucedido! Você pode selecionar outra conta ou continuar com esta.');
            setIsChecking(false);
            setLoginSuccess(true); // Manter true para mostrar o botão "Ir para Conteúdo"
            setShowSavedConnections(true);

            // Remover o parâmetro switch da URL para evitar loops
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('switch');
            window.history.replaceState({}, '', newUrl.toString());
          } else {
            // Comportamento normal - redirecionar para a tela principal
            navigate('/content');
          }
        }, 1500);
      } else {
        setMessage('Falha na conexão. A conta salva pode estar inválida.');
        setIsError(true);
      }
    } catch (error) {
      console.error('Erro ao conectar com conta salva:', error);
      setMessage('Erro ao tentar conectar com a conta salva. Verifique sua conexão com a internet ou se as credenciais ainda são válidas.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };
  
  // Função para desativar o login automático de uma conexão
  const disableAutoLogin = async (connection: any, event: React.MouseEvent) => {
    // Impedir que o clique propague para o item da lista
    event.stopPropagation();
    
    try {
      setIsChecking(true);
      setMessage('Desativando login automático...');
      setIsError(false);
      
      // Atualizar a conexão para desativar o autoLogin
      await dbService.setConnection({
        ...connection,
        autoLogin: false
      });
      
      // Atualizar a lista de conexões salvas
      const updatedConnections = await dbService.getAllConnections();
      setSavedConnections(updatedConnections);
      
      // Mostrar mensagem de sucesso com o nome do servidor
      try {
        const urlObj = new URL(connection.url);
        setMessage(`Login automático desativado para ${urlObj.hostname}`);
      } catch {
        setMessage('Login automático desativado com sucesso');
      }
      setIsError(false);
    } catch (error) {
      console.error('Erro ao desativar login automático:', error);
      setMessage('Erro ao desativar login automático. Tente novamente.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };
  
  // Função para ativar o login automático de uma conexão
  const enableAutoLogin = async (connection: any, event: React.MouseEvent) => {
    // Impedir que o clique propague para o item da lista
    event.stopPropagation();
    
    try {
      setIsChecking(true);
      setMessage('Configurando login automático...');
      setIsError(false);
      
      // Primeiro, desativar o autoLogin em todas as conexões
      const allConnections = await dbService.getAllConnections();
      for (const conn of allConnections) {
        if (conn.autoLogin) {
          await dbService.setConnection({
            ...conn,
            autoLogin: false
          });
        }
      }
      
      // Depois, ativar o autoLogin apenas para a conexão selecionada
      await dbService.setConnection({
        ...connection,
        autoLogin: true
      });
      
      // Atualizar a lista de conexões salvas
      const updatedConnections = await dbService.getAllConnections();
      setSavedConnections(updatedConnections);
      
      // Mostrar mensagem de sucesso com o nome do servidor
      try {
        const urlObj = new URL(connection.url);
        setMessage(`Login automático ativado para ${urlObj.hostname}!`);
      } catch {
        setMessage('Login automático ativado com sucesso!');
      }
      setIsError(false);
    } catch (error) {
      console.error('Erro ao ativar login automático:', error);
      setMessage('Erro ao ativar login automático. Tente novamente.');
      setIsError(true);
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        minHeight: '100vh',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        background: `linear-gradient(135deg,
          ${alpha(theme.palette.background.default, 0.95)} 0%,
          ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
        backdropFilter: 'blur(20px)',
        padding: isMobile ? 1 : 2
      }}
    >
      {/* Background com partículas - posicionamento fixo */}
      <ParticlesBackground type="auth" intensity="low" />

      {/* Banner promocional no topo - mais largo */}
      <Box sx={{
        position: 'absolute',
        top: isMobile ? 15 : 25,
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1,
        width: '100%',
        maxWidth: isMobile ? '95%' : '800px', // Aumentado para 800px
        px: 1
      }}>
        <WelcomeTvDisplay />
      </Box>

      {/* Backdrop de sucesso ou carregamento */}
      <Backdrop
        open={loginSuccess || isChecking}
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(8px)',
        }}
      >
        <Fade in={loginSuccess || isChecking}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
            }}
          >
            {loginSuccess ? (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: [0, 1.2, 1] }}
                transition={{
                  duration: 0.5,
                  times: [0, 0.6, 1],
                  ease: "easeInOut"
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.success.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 0 30px ${alpha(theme.palette.success.main, 0.5)}`
                  }}
                >
                  <DoneIcon sx={{ fontSize: 40, color: '#fff' }} />
                </Box>
              </motion.div>
            ) : (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <CircularProgress size={60} color="primary" />
                </Box>
              </motion.div>
            )}
            <Typography
              variant="h5"
              sx={{
                color: loginSuccess ? theme.palette.success.main : theme.palette.primary.main,
                fontWeight: 600,
                textAlign: 'center'
              }}
            >
              {loginSuccess ? 'Login bem-sucedido!' : 'Conectando...'}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.text.secondary,
                textAlign: 'center'
              }}
            >
              {loginSuccess ? 'Redirecionando para o conteúdo...' : 'Verificando suas credenciais...'}
            </Typography>
          </Box>
        </Fade>
      </Backdrop>

      {/* Conteúdo principal centralizado */}
      <Box sx={{
        marginTop: isMobile ? '120px' : '140px',
        width: '100%',
        maxWidth: '420px',
        zIndex: 2
      }}>
        <EnhancedAuthCard
          title="Neko TV"
          subtitle="Acesso Rápido ao Sistema"
          showLogo={false}
        >
        <AnimatePresence mode="wait">
          {!showManualLogin && !showSavedConnections ? (
            <motion.div
              key="main-form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4 }}
            >
              {/* Verificação Automática */}
              <Box sx={{ mb: 3 }}>
                <EnhancedButton
                  onClick={checkLicense}
                  loading={isChecking}
                  disabled={isChecking}
                  variant="primary"
                  size="large"
                  fullWidth
                  startIcon={<RefreshIcon />}
                >
                  {isChecking ? 'Verificando...' : 'Verificar Licença'}
                </EnhancedButton>
              </Box>

              {/* Divisor */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                my: 3,
                '&::before, &::after': {
                  content: '""',
                  flex: 1,
                  height: '1px',
                  background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.divider, 0.5)}, transparent)`
                }
              }}>
                <Typography
                  variant="body2"
                  sx={{
                    px: 2,
                    color: 'text.secondary',
                    fontSize: '0.75rem',
                    fontWeight: 500
                  }}
                >
                  OU
                </Typography>
              </Box>

              {/* Mensagem de Acesso Automático */}
              {savedConnections.some(conn => conn.autoLogin) && (
                <Box sx={{ 
                  mb: 3, 
                  p: 2, 
                  borderRadius: 2, 
                  backgroundColor: alpha(theme.palette.success.main, 0.1),
                  border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`
                }}>
                  <Typography variant="body2" sx={{ color: theme.palette.success.main, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DoneIcon fontSize="small" />
                    <span>Login automático configurado</span>
                  </Typography>
                  <Typography variant="caption" sx={{ mt: 1, display: 'block', color: 'text.secondary' }}>
                    Na próxima vez que abrir o aplicativo, você será conectado automaticamente.
                  </Typography>
                </Box>
              )}
              
              {/* Botões de Opções */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* Contas Salvas - Mostrar apenas se houver conexões */}
                {savedConnections.length > 0 && (
                  <EnhancedButton
                    onClick={() => setShowSavedConnections(true)}
                    variant="outline"
                    size="large"
                    fullWidth
                    startIcon={<HistoryIcon />}
                    endIcon={<KeyboardArrowRightIcon />}
                  >
                    Contas Conectadas ({savedConnections.length})
                  </EnhancedButton>
                )}

                {/* Conexão Manual */}
                <EnhancedButton
                  onClick={() => setShowManualLogin(true)}
                  variant="outline"
                  size="large"
                  fullWidth
                  startIcon={<LiveTvIcon />}
                  endIcon={<KeyboardArrowRightIcon />}
                >
                  Conexão Manual
                </EnhancedButton>
              </Box>

              {/* ID do Dispositivo - Compacto */}
              <Box sx={{
                mt: 4,
                p: 2,
                borderRadius: 2,
                background: alpha(theme.palette.background.paper, 0.5),
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    mb: 1,
                    fontWeight: 600,
                    color: 'text.secondary',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}
                >
                  ID do Dispositivo
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontFamily: 'monospace',
                      fontSize: '0.8rem',
                      flex: 1,
                      wordBreak: 'break-all',
                      color: 'text.primary',
                      opacity: 0.8
                    }}
                  >
                    {deviceId}
                  </Typography>
                  <Tooltip title={copied ? "Copiado!" : "Copiar"}>
                    <IconButton
                      onClick={copyToClipboard}
                      size="small"
                      sx={{
                        color: copied ? theme.palette.success.main : theme.palette.text.secondary,
                        '&:hover': {
                          color: theme.palette.primary.main,
                          backgroundColor: alpha(theme.palette.primary.main, 0.1)
                        }
                      }}
                    >
                      {copied ? <DoneIcon fontSize="small" /> : <ContentCopyIcon fontSize="small" />}
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
            </motion.div>
          ) : showManualLogin ? (
            <motion.div
              key="manual-form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4 }}
            >
              {/* Formulário de Login Manual */}
              <Box sx={{ mb: 3 }}>
                <EnhancedInput
                  label="URL do Servidor"
                  value={manualUrl}
                  onChange={setManualUrl}
                  type="url"
                  placeholder="http://exemplo.com:8080"
                  icon={<LinkIcon />}
                />

                <EnhancedInput
                  label="Usuário"
                  value={manualUsername}
                  onChange={setManualUsername}
                  placeholder="Seu usuário"
                  icon={<PersonIcon />}
                />

                <EnhancedInput
                  label="Senha"
                  value={manualPassword}
                  onChange={setManualPassword}
                  type="password"
                  placeholder="Sua senha"
                  icon={<LockIcon />}
                />
                
                {/* Opção de Login Automático */}
                 <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                   <Checkbox 
                     checked={enableAutoLoginOnSubmit}
                     onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEnableAutoLoginOnSubmit(e.target.checked)}
                     color="primary"
                     size="small"
                     id="auto-login-checkbox"
                   />
                   <Typography 
                     variant="body2" 
                     component="label" 
                     htmlFor="auto-login-checkbox"
                     sx={{ ml: 1, cursor: 'pointer', color: 'text.secondary', display: 'flex', alignItems: 'center' }}
                     onClick={() => setEnableAutoLoginOnSubmit(!enableAutoLoginOnSubmit)}
                   >
                     Ativar login automático
                     <Tooltip title="O aplicativo irá conectar automaticamente na próxima vez que for aberto">
                       <IconButton size="small" sx={{ ml: 0.5, p: 0.2 }}>
                         <InfoIcon fontSize="small" sx={{ fontSize: '0.8rem', opacity: 0.7 }} />
                       </IconButton>
                     </Tooltip>
                   </Typography>
                 </Box>
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <EnhancedButton
                  onClick={() => setShowManualLogin(false)}
                  variant="ghost"
                  size="medium"
                  fullWidth
                >
                  Voltar
                </EnhancedButton>

                <EnhancedButton
                  onClick={handleManualLogin}
                  loading={isChecking}
                  disabled={isChecking || !manualUrl.trim()}
                  variant="primary"
                  size="medium"
                  fullWidth
                  startIcon={<LiveTvIcon />}
                >
                  Conectar
                </EnhancedButton>
              </Box>
            </motion.div>
          ) : (
            <motion.div
              key="saved-connections"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4 }}
            >
              {/* Lista de Conexões Salvas */}
              <Typography
                variant="h6"
                sx={{
                  mb: 2,
                  fontWeight: 600,
                  color: theme.palette.text.primary
                }}
              >
                Contas Conectadas
              </Typography>
              
              <Paper
                elevation={0}
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  overflow: 'hidden',
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  backgroundColor: alpha(theme.palette.background.paper, 0.5)
                }}
              >
                <List sx={{ p: 0 }}>
                  {savedConnections.map((connection, index) => {
                    // Extrair informações da URL para exibição
                    let displayUrl = connection.url;
                    let displayUsername = connection.username || '';
                    
                    try {
                      const urlObj = new URL(connection.url);
                      displayUrl = urlObj.hostname;
                      
                      // Se não tiver username no objeto, tentar extrair da URL
                      if (!displayUsername) {
                        const params = new URLSearchParams(urlObj.search);
                        if (params.has('username')) {
                          displayUsername = params.get('username') || '';
                        }
                      }
                    } catch (e) {
                      // Manter a URL original se não for possível analisar
                    }
                    
                    // Formatar a data de último uso
                    const lastUsedDate = new Date(connection.lastUsed || 0);
                    const formattedDate = lastUsedDate.toLocaleDateString('pt-BR', {
                      day: '2-digit',
                      month: '2-digit',
                      year: '2-digit'
                    });
                    
                    return (
                      <React.Fragment key={index}>
                        {index > 0 && <Divider />}
                        <ListItemButton
                          onClick={() => connectWithSavedConnection(connection, false)}
                          disabled={isChecking}
                          sx={{
                            py: 2,
                            position: 'relative',
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.08)
                            },
                            ...(connection.autoLogin && {
                              backgroundColor: alpha(theme.palette.primary.main, 0.05),
                              borderLeft: `4px solid ${theme.palette.primary.main}`
                            })
                          }}
                        >
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            <AccountCircleIcon color={connection.autoLogin ? "primary" : "inherit"} />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Box component="span" sx={{ fontWeight: 600 }}>
                                  {displayUsername || displayUrl}
                                </Box>
                                {connection.autoLogin && (
                                  <Tooltip title="Login automático ativado">
                                    <Box
                                      component="span"
                                      sx={{
                                        ml: 1,
                                        px: 1,
                                        py: 0.25,
                                        borderRadius: 1,
                                        fontSize: '0.7rem',
                                        fontWeight: 'bold',
                                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                        color: theme.palette.primary.main
                                      }}
                                    >
                                      AUTO
                                    </Box>
                                  </Tooltip>
                                )}
                              </Box>
                            }
                            secondary={
                              <Box component="span" sx={{ display: 'flex', flexDirection: 'column', mt: 0.5 }}>
                                <Box component="span" sx={{ opacity: 0.7, fontSize: '0.875rem' }}>
                                  {displayUrl}
                                </Box>
                                <Box component="span" sx={{ opacity: 0.5, mt: 0.5, fontSize: '0.75rem' }}>
                                  Último acesso: {formattedDate}
                                </Box>
                              </Box>
                            }
                          />
                          {connection.autoLogin ? (
                            <Tooltip title="Desativar login automático">
                              <IconButton
                                size="small"
                                onClick={(e) => disableAutoLogin(connection, e)}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  '&:hover': {
                                    color: theme.palette.error.main,
                                    backgroundColor: alpha(theme.palette.error.main, 0.1)
                                  }
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          ) : (
                            <Tooltip title="Ativar login automático">
                              <IconButton
                                size="small"
                                onClick={(e) => enableAutoLogin(connection, e)}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  '&:hover': {
                                    color: theme.palette.success.main,
                                    backgroundColor: alpha(theme.palette.success.main, 0.1)
                                  }
                                }}
                              >
                                <AutorenewIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </ListItemButton>
                      </React.Fragment>
                    );
                  })}
                </List>
              </Paper>
              
              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                <EnhancedButton
                  onClick={() => setShowSavedConnections(false)}
                  variant="ghost"
                  size="medium"
                  fullWidth
                >
                  Voltar
                </EnhancedButton>
                
                {/* Botão para ir para a tela principal após login bem-sucedido durante troca de conta */}
                {loginSuccess && (
                  <EnhancedButton
                    onClick={() => navigate('/content')}
                    variant="primary"
                    size="medium"
                    fullWidth
                    startIcon={<LiveTvIcon />}
                  >
                    {(() => {
                      // Verificar se estamos em modo de troca de conta
                      const currentUrl = window.location.href;
                      const urlObj = new URL(currentUrl);
                      const isSwitchingAccount = urlObj.searchParams.has('switch');
                      return isSwitchingAccount ? 'Continuar com esta Conta' : 'Ir para Conteúdo';
                    })()}
                  </EnhancedButton>
                )}
              </Box>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mensagens de Status */}
        <Collapse in={!!message} timeout={300}>
          <Alert
            severity={isError ? "error" : "success"}
            icon={isChecking ? <CircularProgress size={20} thickness={4} /> : undefined}
            sx={{
              mt: 3,
              borderRadius: 2,
              '& .MuiAlert-icon': {
                fontSize: '1.25rem'
              },
              animation: isChecking ? 'pulse 1.5s infinite' : 'none',
              '@keyframes pulse': {
                '0%': {
                  boxShadow: '0 0 0 0 rgba(0, 0, 0, 0.1)'
                },
                '70%': {
                  boxShadow: '0 0 0 6px rgba(0, 0, 0, 0)'
                },
                '100%': {
                  boxShadow: '0 0 0 0 rgba(0, 0, 0, 0)'
                }
              }
            }}
          >
            <Typography variant="body2" sx={{ fontWeight: isChecking ? 500 : 400 }}>
              {message}
            </Typography>
          </Alert>
        </Collapse>
        </EnhancedAuthCard>
      </Box>
    </Box>
  );
};

export default EnhancedAccess;