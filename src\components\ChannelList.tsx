import React, { useMemo, useCallback, memo } from 'react';
import { Box, List, ListItem, ListItemButton, ListItemText, Typography, ListItemAvatar, Avatar, IconButton, useMediaQuery } from '@mui/material';
import { Stream, EPGChannel } from '../services/iptvService';
import moment from 'moment';
import { useTheme } from '@mui/material/styles';
import ProgressBar from './ProgressBar';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import { useFavorites } from '../contexts/FavoritesContext';

interface ChannelListProps {
  channels: Stream[];
  selectedChannelId?: string;
  onChannelSelect: (channel: Stream) => void;
  epgData?: EPGChannel[];
}

// Improved channel name normalization function
const normalizeChannelName = (name: string): string => {
  // Convert to lowercase
  let normalized = name.toLowerCase();
  
  // Remove quality indicators and special variations
  normalized = normalized
    .replace(/\[.*?\]/g, '') // Remove content in brackets
    .replace(/\(.*?\)/g, '') // Remove content in parentheses
    .replace(/\bfhd\b|\bhd\b|\bsd\b|\bh265\b|\bhevc\b|\b4k\b/gi, '') // Remove quality indicators as whole words
    .replace(/\+/g, 'plus') // Replace + with 'plus' to avoid confusion
    .replace(/\&/g, 'and') // Replace & with 'and'
    .replace(/\s+/g, '') // Remove all whitespace
    .replace(/[^a-z0-9]/g, ''); // Remove special characters
  
  // Keep at least 4 characters to avoid overly short strings
  return normalized.length < 4 ? name.toLowerCase().replace(/\s+/g, '') : normalized;
};

// Create a memoized channel item component
const ChannelItem = React.memo(({
  channel,
  isSelected,
  onSelect,
  programData,
  isFavorite,
  onToggleFavorite
}: {
  channel: Stream;
  isSelected: boolean;
  onSelect: () => void;
  programData: { current?: any; next?: any } | undefined;
  isFavorite: boolean;
  onToggleFavorite: (event: React.MouseEvent) => void;
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <ListItem
      disablePadding
      dense
      sx={{
        flexDirection: 'column',
        alignItems: 'stretch',
        borderBottom: '1px solid rgba(255,255,255,0.05)'
      }}
    >
      <ListItemButton
        selected={isSelected}
        onClick={onSelect}
        sx={{
          py: 1,
          px: 1,
          minHeight: programData && (programData.current || programData.next) ? '88px' : '48px',
          alignItems: 'flex-start',
          '&.Mui-selected': {
            bgcolor: 'primary.main',
            '&:hover': {
              bgcolor: 'primary.dark',
            },
          },
          position: 'relative',
          '&:hover': {
            bgcolor: theme.palette.action.hover,
          },
        }}
      >
        {channel.thumbnail && (
          <ListItemAvatar sx={{ minWidth: 44, mt: 0.25 }}>
            <Avatar
              src={channel.thumbnail}
              alt={channel.name}
              variant="rounded"
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1
              }}
            />
          </ListItemAvatar>
        )}
        <ListItemText
          primary={channel.name}
          secondary={programData && (programData.current || programData.next) && (
            <Box sx={{ mt: 0.5 }}>
              {/* Programa atual */}
              {programData.current && (
                <>
                  <Typography
                    variant="caption"
                    component="div"
                    sx={{
                      color: isSelected ? 'rgba(255,255,255,0.9)' : 'text.primary',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      lineHeight: 1.2,
                      mb: 0.25,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    🔴 {programData.current.title}
                  </Typography>

                  {/* Horário do programa atual */}
                  <Typography
                    variant="caption"
                    component="div"
                    sx={{
                      color: isSelected ? 'rgba(255,255,255,0.7)' : 'text.secondary',
                      fontSize: '0.65rem',
                      lineHeight: 1.1,
                      mb: 0.25,
                      fontFamily: 'monospace'
                    }}
                  >
                    {moment(programData.current.startTime, 'YYYY-MM-DD HH:mm:ss').format('HH:mm')} - {moment(programData.current.endTime, 'YYYY-MM-DD HH:mm:ss').format('HH:mm')}
                  </Typography>

                  {/* Barra de progresso */}
                  <ProgressBar
                    startTime={programData.current.startTime}
                    endTime={programData.current.endTime}
                    height={3}
                    showTime={false}
                    variant="compact"
                    foregroundColor={isSelected ? 'rgba(255,255,255,0.8)' : theme.palette.primary.main}
                  />
                </>
              )}

              {/* Próximo programa */}
              {programData.next && (
                <Typography
                  variant="caption"
                  component="div"
                  sx={{
                    color: isSelected ? 'rgba(255,255,255,0.6)' : 'text.secondary',
                    fontSize: '0.7rem',
                    lineHeight: 1.1,
                    mt: programData.current ? 0.5 : 0,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  ⏭️ {moment(programData.next.startTime, 'YYYY-MM-DD HH:mm:ss').format('HH:mm')} - {programData.next.title}
                </Typography>
              )}
            </Box>
          )}
          secondaryTypographyProps={{
            component: 'div',
            sx: { mt: 0 }
          }}
          sx={{
            m: 0,
            pr: 4,
            '& .MuiListItemText-primary': {
              color: isSelected ? 'common.white' : 'text.primary',
              fontSize: '0.9rem',
              fontWeight: isSelected ? 600 : 500,
              lineHeight: 1.2,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            },
          }}
        />
        <IconButton
          onClick={onToggleFavorite}
          size="small"
          sx={{
            color: isFavorite ? 'error.main' : 'action.disabled',
            position: 'absolute',
            right: 4,
            top: programData && (programData.current || programData.next) ? '15%' : '50%',
            transform: 'translateY(-50%)',
            padding: 0.5,
            '&:hover': {
              color: 'error.main',
              bgcolor: 'rgba(255, 0, 0, 0.1)'
            }
          }}
        >
          {isFavorite ? <FavoriteIcon fontSize="small" /> : <FavoriteBorderIcon fontSize="small" />}
        </IconButton>
      </ListItemButton>
    </ListItem>
  );
});

// Funções auxiliares para a barra de progresso
const getProgramProgress = (program: any): number => {
  const now = moment();
  const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
  const end = moment(program.endTime, 'YYYY-MM-DD HH:mm:ss');
  
  // Cálculo da porcentagem de progresso
  const totalDuration = end.diff(start);
  const elapsedDuration = now.diff(start);
  const progress = Math.min(Math.max((elapsedDuration / totalDuration) * 100, 0), 100);
  
  return progress;
};

const getProgramProgressText = (program: any): string => {
  const now = moment();
  const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
  const elapsed = now.diff(start, 'minutes');
  
  return `${elapsed} min`;
};

const getProgramDuration = (program: any): string => {
  const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
  const end = moment(program.endTime, 'YYYY-MM-DD HH:mm:ss');
  const duration = end.diff(start, 'minutes');
  
  return `${duration} min`;
};

const ChannelList: React.FC<ChannelListProps> = memo(({
  channels,
  selectedChannelId,
  onChannelSelect,
  epgData
}) => {
  const { isFavorite, addFavorite, removeFavorite, forceSave } = useFavorites();
  
  // Memoize normalized channel names
  const normalizedChannelMap = useMemo(() => {
    const map = new Map<string, string>();
    channels.forEach(channel => {
      map.set(channel.name, normalizeChannelName(channel.name));
    });
    if (epgData) {
      epgData.forEach(epg => {
        map.set(epg.name, normalizeChannelName(epg.name));
      });
    }
    return map;
  }, [channels, epgData]);

  // Create memoized findMatchingChannel function
  const findMatchingChannel = useCallback((channelName: string): EPGChannel | undefined => {
    if (!epgData) return undefined;
    
    const normalizedName = normalizedChannelMap.get(channelName) || normalizeChannelName(channelName);
    
    // Try exact match first
    let match = epgData.find(ch => {
      const epgNormalizedName = normalizedChannelMap.get(ch.name) || normalizeChannelName(ch.name);
      return epgNormalizedName === normalizedName;
    });

    if (!match) {
      // Try more specific partial match with stricter rules
      match = epgData.find(ch => {
        const epgNormalizedName = normalizedChannelMap.get(ch.name) || normalizeChannelName(ch.name);
        
        // Only consider a match if one name fully contains the other AND they share at least 70% similarity
        const includesOther = normalizedName.includes(epgNormalizedName) || epgNormalizedName.includes(normalizedName);
        
        if (!includesOther) return false;
        
        // Calculate similarity ratio (Jaccard similarity on character sets)
        const set1 = new Set(normalizedName);
        const set2 = new Set(epgNormalizedName);
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        const similarity = intersection.size / union.size;
        return similarity >= 0.7; // At least 70% similar
      });
    }

    return match;
  }, [epgData, normalizedChannelMap]);

  // Memoize current and next programs for all channels
  const channelPrograms = useMemo(() => {
    const now = moment();
    const programMap = new Map<string, any>();

    if (!epgData) return programMap;

    channels.forEach(channel => {
      const matchingChannel = findMatchingChannel(channel.name);
      if (matchingChannel?.programs?.length) {
        // Sort all programs by start time
        const sortedPrograms = matchingChannel.programs.sort((a, b) => {
          const startA = moment(a.startTime, 'YYYY-MM-DD HH:mm:ss');
          const startB = moment(b.startTime, 'YYYY-MM-DD HH:mm:ss');
          return startA.valueOf() - startB.valueOf();
        });

        // Find current program
        const currentProgram = sortedPrograms.find(program => {
          const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
          const end = moment(program.endTime, 'YYYY-MM-DD HH:mm:ss');
          return now.isBetween(start, end);
        });

        // Find next program
        const nextProgram = sortedPrograms.find(program => {
          const start = moment(program.startTime, 'YYYY-MM-DD HH:mm:ss');
          return start.isAfter(now);
        });

        if (currentProgram || nextProgram) {
          programMap.set(channel.id, {
            current: currentProgram,
            next: nextProgram
          });
        }
      }
    });

    return programMap;
  }, [channels, epgData, findMatchingChannel]);

  const handleToggleFavorite = useCallback((e: React.MouseEvent, channel: Stream) => {
    e.stopPropagation(); // Prevenir que o clique propague para o item da lista
    
    if (isFavorite(channel.id)) {
      removeFavorite(channel.id);
    } else {
      addFavorite(channel);
    }
    
    // Força o salvamento imediato após adicionar/remover favorito
    setTimeout(() => forceSave(), 100);
  }, [isFavorite, addFavorite, removeFavorite, forceSave]);

  return (
    <Box sx={{
      overflow: 'auto',
      height: '100%',
      width: '100%',
      WebkitOverflowScrolling: 'touch',
      overscrollBehavior: 'contain',
      bgcolor: 'rgba(0,0,0,0.3)',
      display: 'flex',
      flexDirection: 'column',
      minHeight: 0,
      '&::-webkit-scrollbar': {
        width: '8px',
      },
      '&::-webkit-scrollbar-track': {
        background: 'rgba(0,0,0,0.1)',
      },
      '&::-webkit-scrollbar-thumb': {
        background: 'rgba(255,255,255,0.2)',
        borderRadius: '4px',
        '&:hover': {
          background: 'rgba(255,255,255,0.3)',
        },
      },
    }}>
      <List disablePadding dense sx={{ 
        width: '100%',
        flex: 1,
        minHeight: 0
      }}>
        {channels.map((channel) => {
          const isSelected = selectedChannelId === channel.id;
          const programData = channelPrograms.get(channel.id);

          return (
            <ChannelItem
              key={channel.id}
              channel={channel}
              isSelected={isSelected}
              onSelect={() => onChannelSelect(channel)}
              programData={programData}
              isFavorite={isFavorite(channel.id)}
              onToggleFavorite={(e) => handleToggleFavorite(e, channel)}
            />
          );
        })}
      </List>
    </Box>
  );
});

// Adiciona displayName para debugging
ChannelList.displayName = 'ChannelList';

export default ChannelList;