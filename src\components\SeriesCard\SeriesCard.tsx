import React from 'react';
import { Card, Typography, Box } from '@mui/material';

interface SeriesCardProps {
  series: any;
  onClick?: (series: any) => void;
}

const SeriesCard: React.FC<SeriesCardProps> = ({ series, onClick }) => {
  return (
    <Card 
      onClick={() => onClick?.(series)}
        sx={{
        cursor: 'pointer',
        height: '300px',
        backgroundImage: `url(${series.cover || series.thumbnail || ''})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
                              position: 'relative',
                              '&:hover': {
          transform: 'scale(1.05)',
          transition: 'transform 0.3s ease'
        }
      }}
    >
                              <Box
                                sx={{
                                  position: 'absolute',
                                  bottom: 0,
                                  left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
          p: 2
        }}
      >
        <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
          {series.name || 'Série'}
                              </Typography>
                            </Box>
    </Card>
  );
};

export default SeriesCard; 