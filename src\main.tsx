import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserRouter } from 'react-router-dom';
import App from './App';
import './styles/index.scss';
import { FavoritesProvider } from './contexts/FavoritesContext';
import { setupGlobalProxyInterceptor } from './utils/proxyUrl';
// PWA removido temporariamente

// Verifica se está rodando no Electron e configura o proxy adequadamente
const isElectronApp = window.location.protocol === 'file:' || (window as any).isElectronApp === true || window.process?.versions?.electron;

// Configurar interceptor de proxy IMEDIATAMENTE (exceto no Electron)
if (!isElectronApp) {
  setupGlobalProxyInterceptor();
} else {
  console.log('🖥️ Electron detectado - proxy global não será configurado');
  // A propriedade isElectronApp já é definida no preload.cjs
}

// Remove loading screen
const loadingElement = document.querySelector('.loading');
if (loadingElement?.parentNode) {
  loadingElement.parentNode.removeChild(loadingElement);
}

// Configuração do React Router
const routerProps = {};

// Log da detecção do Electron (apenas em desenvolvimento)
if (process.env.NODE_ENV === 'development') {
  console.log('isElectronApp:', isElectronApp, window.location.protocol);
}

// Sobrescreve a função nativa de alert com nossa versão moderna
// Isso impede qualquer alerta nativo de aparecer, independente de onde ele seja chamado
const originalAlert = window.alert;
window.alert = (message: string) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Alert interceptado:', message);
  }
  
  // Criar overlay com efeito de blur para notificações elegantes
  const overlayId = 'neko-modern-alert';
  
  // Remover se já existir
  const existingOverlay = document.getElementById(overlayId);
  if (existingOverlay) {
    existingOverlay.remove();
  }
  
  // Criar overlay moderno
  const overlayEl = document.createElement('div');
  overlayEl.id = overlayId;
  overlayEl.className = 'neko-modern-alert';
  
  // HTML com animação
  overlayEl.innerHTML = `
    <div class="modern-alert-modal">
      <div class="modal-icon">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
          <path d="M12 16v-4"></path>
          <path d="M12 8h.01"></path>
        </svg>
      </div>
      <div class="modal-content">
        <p>${message}</p>
        <button id="neko-dismiss-alert">OK</button>
      </div>
    </div>
  `;
  
  // Estilos modernos
  const style = document.createElement('style');
  style.textContent = `
    .neko-modern-alert {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(8px);
      z-index: 99999;
      animation: fadeIn 0.2s ease-out;
    }
    
    .modern-alert-modal {
      background: linear-gradient(135deg, #1e293b, #0f172a);
      border-radius: 16px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
      padding: 24px;
      max-width: 400px;
      width: 85%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      overflow: hidden;
      position: relative;
      animation: slideUp 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    }
    
    .modal-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, #0ea5e9, #0284c7);
      border-radius: 50%;
      margin-bottom: 16px;
      color: white;
    }
    
    .modal-content p {
      color: #e2e8f0;
      font-size: 16px;
      line-height: 1.6;
      margin: 0 0 20px;
    }
    
    #neko-dismiss-alert {
      background: linear-gradient(90deg, #0ea5e9, #0284c7);
      border: none;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      padding: 10px 32px;
      cursor: pointer;
      font-size: 15px;
      transition: all 0.2s ease;
    }
    
    #neko-dismiss-alert:hover {
      background: linear-gradient(90deg, #0284c7, #0369a1);
      transform: translateY(-2px);
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes slideUp {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
  `;
  
  // Adicionar à página
  document.head.appendChild(style);
  document.body.appendChild(overlayEl);
  
  // Função para fechar o modal
  const closeModal = () => {
    overlayEl.style.opacity = '0';
    overlayEl.style.transition = 'opacity 0.2s';

    setTimeout(() => {
      if (document.body.contains(overlayEl)) {
        overlayEl.remove();
        // Remover style tag também para evitar acúmulo
        if (document.head.contains(style)) {
          style.remove();
        }
      }
    }, 200);
  };

  // Adicionar handler ao botão
  const dismissButton = document.getElementById('neko-dismiss-alert');
  if (dismissButton) {
    dismissButton.addEventListener('click', closeModal, { once: true });
  }

  // Também fechar ao clicar no overlay (fora do modal)
  overlayEl.addEventListener('click', (e) => {
    if (e.target === overlayEl) {
      closeModal();
    }
  }, { once: true });
};

// Usa HashRouter para Electron e BrowserRouter para web
const Router = isElectronApp ? HashRouter : BrowserRouter;

// Service Worker removido temporariamente
if (process.env.NODE_ENV === 'development') {
  console.log('PWA desabilitado no modo desenvolvimento');
}

// Importar utilitários de teste apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  import('./utils/testCorsProxy').catch(console.warn);
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Router {...routerProps}>
      <FavoritesProvider>
        <App />
      </FavoritesProvider>
    </Router>
  </React.StrictMode>
);

// Handle offline/online status
window.addEventListener('online', () => {
  document.documentElement.classList.remove('offline');
});

window.addEventListener('offline', () => {
  document.documentElement.classList.add('offline');
});

// Handle visibility change for video playback
document.addEventListener('visibilitychange', () => {
  const event = new CustomEvent('visibilityChanged', {
    detail: { hidden: document.hidden }
  });
  window.dispatchEvent(event);
});

// Prevent accidental navigation
window.addEventListener('beforeunload', (event) => {
  if (document.querySelector('video[data-playing="true"]')) {
    event.preventDefault();
    event.returnValue = '';
  }
});

// Error boundary for uncaught errors
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

// Handle promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Performance monitoring
if ('performance' in window) {
  window.addEventListener('load', () => {
    const timing = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (timing) {
      const metrics = {
        dnsLookup: Math.max(0, timing.domainLookupEnd - timing.domainLookupStart),
        tcpConnection: Math.max(0, timing.connectEnd - timing.connectStart),
        serverResponse: Math.max(0, timing.responseEnd - timing.requestStart),
        domLoad: Math.max(0, timing.domContentLoadedEventEnd - timing.domContentLoadedEventStart),
        fullPageLoad: Math.max(0, timing.loadEventEnd - timing.startTime)
      };
      if (process.env.NODE_ENV === 'development') {
        console.log('Performance metrics:', metrics);
      }
    }
  });
}
