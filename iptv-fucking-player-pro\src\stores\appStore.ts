import { create } from 'zustand';
import { Connection, Category, Stream, SeriesInfo, EPGChannel } from '../types/iptv';

interface AppState {
  // Connection state
  currentConnection: Connection | null;
  isAuthenticated: boolean;
  
  // Content state
  categories: {
    live: Category[];
    movie: Category[];
    series: Category[];
  };
  
  streams: {
    live: Stream[];
    movie: Stream[];
    series: Stream[];
  };
  
  currentSeries: SeriesInfo | null;
  epgData: EPGChannel[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  sidebarCollapsed: boolean;
  currentLanguage: string;
  theme: 'dark' | 'light';
  
  // Player state
  currentStream: Stream | null;
  isPlaying: boolean;
  
  // Actions
  setConnection: (connection: Connection | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setCategories: (type: 'live' | 'movie' | 'series', categories: Category[]) => void;
  setStreams: (type: 'live' | 'movie' | 'series', streams: Stream[]) => void;
  setCurrentSeries: (series: SeriesInfo | null) => void;
  setEPGData: (epg: EPGChannel[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setLanguage: (language: string) => void;
  setTheme: (theme: 'dark' | 'light') => void;
  setCurrentStream: (stream: Stream | null) => void;
  setPlaying: (playing: boolean) => void;
  clearAll: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  // Initial state
  currentConnection: null,
  isAuthenticated: false,
  
  categories: {
    live: [],
    movie: [],
    series: [],
  },
  
  streams: {
    live: [],
    movie: [],
    series: [],
  },
  
  currentSeries: null,
  epgData: [],
  
  isLoading: false,
  error: null,
  sidebarCollapsed: false,
  currentLanguage: 'en-US',
  theme: 'dark',
  
  currentStream: null,
  isPlaying: false,
  
  // Actions
  setConnection: (connection) => set({ currentConnection: connection }),
  setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
  
  setCategories: (type, categories) => set((state) => ({
    categories: {
      ...state.categories,
      [type]: categories,
    },
  })),
  
  setStreams: (type, streams) => set((state) => ({
    streams: {
      ...state.streams,
      [type]: streams,
    },
  })),
  
  setCurrentSeries: (series) => set({ currentSeries: series }),
  setEPGData: (epg) => set({ epgData: epg }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
  setLanguage: (language) => set({ currentLanguage: language }),
  setTheme: (theme) => set({ theme }),
  setCurrentStream: (stream) => set({ currentStream: stream }),
  setPlaying: (playing) => set({ isPlaying: playing }),
  
  clearAll: () => set({
    currentConnection: null,
    isAuthenticated: false,
    categories: { live: [], movie: [], series: [] },
    streams: { live: [], movie: [], series: [] },
    currentSeries: null,
    epgData: [],
    currentStream: null,
    isPlaying: false,
    error: null,
  }),
}));