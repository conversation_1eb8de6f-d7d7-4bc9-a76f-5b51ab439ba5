const CACHE_NAME = 'iptv-player-v1';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';
const ASSETS_CACHE = 'assets-v1';

const STATIC_RESOURCES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/logo-neko-tv.png',
  '/icons/icon-48.png',
  '/icons/icon-72.png',
  '/icons/icon-96.png',
  '/icons/icon-144.png',
  '/icons/icon-192.png',
  '/icons/icon-512.png'
];

// Cache estático na instalação
self.addEventListener('install', event => {
  event.waitUntil(
    Promise.all([
      // Cache estático
      caches.open(STATIC_CACHE).then(cache => {
        console.log('Caching static resources');
        return cache.addAll(STATIC_RESOURCES);
      }),
      // Cache de assets
      caches.open(ASSETS_CACHE).then(cache => {
        console.log('Caching assets');
        // Aqui você pode adicionar outros assets como fontes, imagens, etc.
      })
    ])
  );
  self.skipWaiting();
});

// Limpeza de caches antigos na ativação
self.addEventListener('activate', event => {
  event.waitUntil(
    Promise.all([
      // Limpa caches antigos
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (![STATIC_CACHE, DYNAMIC_CACHE, ASSETS_CACHE].includes(cacheName)) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Toma controle imediatamente
      clients.claim()
    ])
  );
});

// Estratégia de cache e rede
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Ignora requisições de análise e outras não importantes
  if (request.method !== 'GET' || request.url.includes('analytics') || request.url.includes('chrome-extension') || request.url.includes('cursor') || request.url.includes('pointer')) {
    return;
  }

  // Estratégia para arquivos estáticos
  if (STATIC_RESOURCES.includes(url.pathname)) {
    event.respondWith(
      caches.match(request)
        .then(response => response || fetch(request))
    );
    return;
  }

  // Estratégia para streams de vídeo
  if (request.headers.get('range')) {
    event.respondWith(
      fetch(request)
        .catch(() => new Response('Offline - Stream indisponível', { status: 503 }))
    );
    return;
  }

  // Estratégia padrão: Network First com fallback para cache
  event.respondWith(
    fetch(request)
      .then(response => {
        // Cache a resposta bem-sucedida
        const responseClone = response.clone();
        caches.open(DYNAMIC_CACHE).then(cache => {
          cache.put(request, responseClone);
        });
        return response;
      })
      .catch(() => {
        return caches.match(request)
          .then(response => {
            if (response) {
              return response;
            }
            // Fallback para fontes
            if (request.url.includes('.woff2')) {
              return new Response('', {
                status: 200,
                headers: new Headers({
                  'Content-Type': 'application/font-woff2'
                })
              });
            }
            // Fallback genérico
            return new Response('Offline - Conteúdo indisponível', {
              status: 503,
              headers: new Headers({
                'Content-Type': 'text/plain'
              })
            });
          });
      })
  );
});

// Sincronização em segundo plano
self.addEventListener('sync', event => {
  if (event.tag === 'sync-favorites') {
    event.waitUntil(
      // Implementar sincronização de favoritos quando online
      Promise.resolve()
    );
  }
});

// Notificações push
self.addEventListener('push', event => {
  const options = {
    body: event.data.text(),
    icon: '/icons/icon-192.png',
    badge: '/icons/icon-96.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Ver canal',
        icon: '/icons/icon-48.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('IPTV Player', options)
  );
}); 