import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useIPTV from '../hooks/useIPTV';
import useEPG from '../hooks/useEPG';
import EPGTooltip from '../components/EPGTooltip';
import PlayerModal from '../components/PlayerModal';
import { FiLoader, FiTv, FiPlay, FiClock } from 'react-icons/fi';
import { motion } from 'framer-motion';

const LiveChannels: React.FC = () => {
  const { t } = useTranslation();
  const { 
    isConnected,
    liveCategories,
    liveStreams,
    isLoadingCategories,
    isLoadingStreams,
    loadStreams,
    getStreamUrl
  } = useIPTV();
  
  const {
    getCurrentProgram,
    getAllPrograms,
    isLoadingEPG
  } = useEPG();
  
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [filteredStreams, setFilteredStreams] = useState<any[]>([]);
  
  // Pagination state
  const [displayedStreams, setDisplayedStreams] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const ITEMS_PER_PAGE = 24; // 6x4 grid
  
  // Tooltip state with debounce
  const [tooltip, setTooltip] = useState<{
    isVisible: boolean;
    channelId: string;
    position: { x: number; y: number };
  }>({
    isVisible: false,
    channelId: '',
    position: { x: 0, y: 0 }
  });
  
  const [tooltipTimeout, setTooltipTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Optimized tooltip handlers
  const handleMouseEnter = (stream: any, e: React.MouseEvent<HTMLDivElement>) => {
    // Clear any existing timeout
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
    }
    
    const channelId = stream.epg_channel_id || stream.stream_id.toString();
    console.log('🖱️ Mouse enter on channel:', stream.name);
    console.log('📋 Stream data:', { 
      name: stream.name, 
      stream_id: stream.stream_id, 
      epg_channel_id: stream.epg_channel_id,
      finalChannelId: channelId 
    });
    
    // Set new timeout for showing tooltip
    const timeout = setTimeout(() => {
      // Check if element still exists
      if (e.currentTarget) {
        const rect = e.currentTarget.getBoundingClientRect();
        const programs = getAllPrograms(channelId);
        console.log('📺 EPG programs for channel:', programs.length, 'programs found');
        console.log('🔧 Setting tooltip visible for channel:', channelId);
        
        setTooltip({
          isVisible: true,
          channelId: channelId,
          position: { x: rect.right, y: rect.top + rect.height / 2 }
        });
      }
    }, 300); // 300ms delay
    
    setTooltipTimeout(timeout);
  };
  
  const handleMouseLeave = () => {
    console.log('🖱️ Mouse leave - hiding tooltip');
    
    // Clear timeout if mouse leaves before tooltip shows
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
      setTooltipTimeout(null);
    }
    
    // Hide tooltip immediately
    setTooltip(prev => ({ ...prev, isVisible: false }));
  };
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (tooltipTimeout) {
        clearTimeout(tooltipTimeout);
      }
    };
  }, [tooltipTimeout]);
  
  // Player modal state
  const [playerModal, setPlayerModal] = useState<{
    isOpen: boolean;
    streamUrl: string;
    channelName: string;
    channelId: string;
  }>({
    isOpen: false,
    streamUrl: '',
    channelName: '',
    channelId: ''
  });

  // Load streams automatically when connected
  useEffect(() => {
    if (isConnected) {
      loadStreams();
    }
  }, [isConnected]);

  // Filter streams by category and reset pagination
  useEffect(() => {
    let filtered;
    if (selectedCategory === 'all') {
      filtered = liveStreams;
    } else {
      filtered = liveStreams.filter(stream => stream.category_id === selectedCategory);
    }
    
    setFilteredStreams(filtered);
    setCurrentPage(1);
    setDisplayedStreams(filtered.slice(0, ITEMS_PER_PAGE));
  }, [liveStreams, selectedCategory]);

  // Load more function
  const loadMoreStreams = () => {
    setIsLoadingMore(true);
    
    // Store current scroll position to maintain smooth UX
    const currentScrollY = window.scrollY;
    
    // Simulate loading delay for better UX
    setTimeout(() => {
      const nextPage = currentPage + 1;
      const startIndex = currentPage * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const newStreams = filteredStreams.slice(startIndex, endIndex);
      
      setDisplayedStreams(prev => [...prev, ...newStreams]);
      setCurrentPage(nextPage);
      setIsLoadingMore(false);
      
      // Smooth scroll to show new content
      setTimeout(() => {
        window.scrollTo({
          top: currentScrollY + 200,
          behavior: 'smooth'
        });
      }, 100);
    }, 500);
  };

  // Check if there are more items to load
  const hasMoreItems = displayedStreams.length < filteredStreams.length;

  if (!isConnected) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-white">
          Live Channels
        </h1>
        
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-6 text-center">
          <FiTv className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <p className="text-slate-300 mb-4">
            Please configure your IPTV connection to view live channels.
          </p>
          <button 
            onClick={() => window.location.hash = '/settings'}
            className="px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
          >
            Go to Settings
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">
          Live Channels
        </h1>
        
        {(isLoadingCategories || isLoadingStreams || isLoadingEPG) && (
          <div className="flex items-center text-slate-300">
            <FiLoader className="animate-spin mr-2" />
            {isLoadingCategories ? 'Loading categories...' : 
             isLoadingStreams ? 'Loading channels...' : 
             'Loading EPG...'}
          </div>
        )}
      </div>

      {/* Categories Filter */}
      {(liveCategories.length > 0 || isLoadingCategories) && (
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-3">Categories</h3>
          <div className="flex flex-wrap gap-2">
            {isLoadingCategories ? (
              // Skeleton loading for categories
              <>
                <div className="h-9 w-32 bg-slate-700 rounded-lg animate-pulse"></div>
                <div className="h-9 w-24 bg-slate-700 rounded-lg animate-pulse"></div>
                <div className="h-9 w-28 bg-slate-700 rounded-lg animate-pulse"></div>
                <div className="h-9 w-36 bg-slate-700 rounded-lg animate-pulse"></div>
                <div className="h-9 w-20 bg-slate-700 rounded-lg animate-pulse"></div>
              </>
            ) : (
              <>
                <button
                  key="all"
                  onClick={() => setSelectedCategory('all')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedCategory === 'all'
                      ? 'bg-sky-600 text-white'
                      : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                  }`}
                >
                  All Categories ({liveStreams?.length || 0})
                </button>
                
                {liveCategories.map((category) => {
                  const categoryStreams = liveStreams.filter(stream => stream.category_id === category.category_id)
                  return (
                    <button
                      key={category.category_id}
                      onClick={() => setSelectedCategory(category.category_id)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        selectedCategory === category.category_id
                          ? 'bg-sky-600 text-white'
                          : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                      }`}
                    >
                      {category.category_name} ({categoryStreams.length})
                    </button>
                  )
                })}
              </>
            )}
          </div>
        </div>
      )}

      {/* Channels Grid */}
      {displayedStreams.length > 0 ? (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-4">
            {displayedStreams.map((stream, index) => (
            <ChannelCard 
              key={stream.stream_id} 
              stream={stream} 
              index={index}
              onMouseEnter={(e) => handleMouseEnter(stream, e)}
              onMouseLeave={handleMouseLeave}
              onClick={() => {
                const streamUrl = getStreamUrl(stream.stream_id, 'live');
                setPlayerModal({
                  isOpen: true,
                  streamUrl,
                  channelName: stream.name,
                  channelId: stream.epg_channel_id || stream.stream_id.toString()
                });
              }}
              currentProgram={getCurrentProgram(stream.epg_channel_id || stream.stream_id.toString())}
            />
            ))}
          </div>

          {/* Load More Button */}
          {hasMoreItems && (
            <div className="flex justify-center">
              <button
                onClick={loadMoreStreams}
                disabled={isLoadingMore}
                className="px-8 py-3 bg-sky-500 hover:bg-sky-600 disabled:bg-sky-400 text-white font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 disabled:cursor-not-allowed"
              >
                {isLoadingMore ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Loading...</span>
                  </>
                ) : (
                  <>
                    <span>Load More Channels</span>
                    <span className="bg-sky-600 px-2 py-1 rounded text-sm">
                      +{Math.min(ITEMS_PER_PAGE, filteredStreams.length - displayedStreams.length)}
                    </span>
                  </>
                )}
              </button>
            </div>
          )}

          {/* Results info */}
          <div className="text-center text-sm text-slate-400">
            Showing {displayedStreams.length} of {filteredStreams.length} channels
            {selectedCategory !== 'all' && (
              <span className="ml-2">
                in {liveCategories.find(cat => cat.category_id === selectedCategory)?.category_name}
              </span>
            )}
          </div>
        </div>
      ) : (
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-6 text-center">
          {(isLoadingCategories || isLoadingStreams || isLoadingEPG) ? (
            <div className="flex items-center justify-center">
              <FiLoader className="animate-spin h-8 w-8 text-sky-500 mr-3" />
              <span className="text-slate-300">
                {isLoadingCategories ? 'Loading categories...' : 
                 isLoadingStreams ? 'Loading channels...' : 
                 'Loading EPG...'}
              </span>
            </div>
          ) : (
            <>
              <FiTv className="mx-auto h-12 w-12 text-slate-400 mb-4" />
              <p className="text-slate-300 mb-2">
                {selectedCategory === 'all' 
                  ? 'No channels available.'
                  : 'No channels in this category.'
                }
              </p>
              {selectedCategory === 'all' && (
                <p className="text-slate-500 text-sm">
                  Make sure your IPTV connection is working and try refreshing the page.
                </p>
              )}
            </>
          )}
        </div>
      )}

      {/* EPG Tooltip */}
      {tooltip.isVisible && (
        <div
          className="fixed z-50 bg-red-500 text-white p-2 rounded"
          style={{
            left: tooltip.position.x + 10,
            top: tooltip.position.y - 10,
          }}
        >
          Debug Tooltip: {tooltip.channelId}
        </div>
      )}
      
      <EPGTooltip
        programs={getAllPrograms(tooltip.channelId)}
        channelName={displayedStreams.find(s => 
          (s.epg_channel_id || s.stream_id.toString()) === tooltip.channelId
        )?.name || ''}
        isVisible={tooltip.isVisible}
        position={tooltip.position}
      />

      {/* Player Modal */}
      <PlayerModal
        isOpen={playerModal.isOpen}
        onClose={() => setPlayerModal(prev => ({ ...prev, isOpen: false }))}
        streamUrl={playerModal.streamUrl}
        channelName={playerModal.channelName}
        channelId={playerModal.channelId}
      />
    </div>
  );
};

interface ChannelCardProps {
  stream: any;
  index: number;
  onMouseEnter: (e: React.MouseEvent<HTMLDivElement>) => void;
  onMouseLeave: () => void;
  onClick: () => void;
  currentProgram: any;
}

const ChannelCard: React.FC<ChannelCardProps> = ({ 
  stream, 
  index, 
  onMouseEnter, 
  onMouseLeave, 
  onClick,
  currentProgram 
}) => {

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05 }}
      className="bg-slate-800 border border-slate-700 rounded-lg overflow-hidden hover:border-sky-500/50 hover:shadow-lg hover:shadow-sky-500/10 transition-all duration-200 group cursor-pointer transform hover:scale-[1.02]"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
    >
      <div className="relative">
        {stream.stream_icon ? (
          <img
            src={stream.stream_icon}
            alt={stream.name}
            className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-200"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        ) : (
          <div className="w-full h-32 bg-slate-700 flex items-center justify-center">
            <FiTv className="h-8 w-8 text-slate-400" />
          </div>
        )}
        
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
          <FiPlay className="h-8 w-8 text-white" />
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="font-semibold text-white text-sm mb-2 line-clamp-2">
          {stream.name}
        </h3>
        
        {/* Current program info */}
        {currentProgram ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-xs text-green-400">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                LIVE
              </div>
              <span className="text-xs text-slate-500">
                {currentProgram.startTime.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
            </div>
            
            <p className="text-xs text-slate-300 line-clamp-1">
              {currentProgram.title}
            </p>
            
            {/* Progress bar */}
            <div className="w-full bg-slate-600 rounded-full h-1">
              <div
                className="bg-sky-500 h-1 rounded-full transition-all duration-1000"
                style={{ width: `${currentProgram.progress}%` }}
              />
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <div className="flex items-center text-xs text-slate-400">
              <div className="w-2 h-2 bg-slate-400 rounded-full mr-2"></div>
              No EPG data
            </div>
            
            {stream.stream_id && (
              <span className="text-xs text-slate-500">
                #{stream.stream_id}
              </span>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default LiveChannels;