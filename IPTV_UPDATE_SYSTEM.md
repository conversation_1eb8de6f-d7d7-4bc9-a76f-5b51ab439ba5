# Sistema de Detecção de Atualizações IPTV - Neko TV

## 🎯 **Objetivo**

Detectar automaticamente quando há atualizações no conteúdo IPTV (novos canais, filmes, séries, categorias) e exibir um modal para o usuário atualizar o cache.

## 🏗️ **Arquitetura**

### 1. **IptvUpdateDetector** (`src/services/iptvUpdateDetector.ts`)
- **Função**: Detecta mudanças no conteúdo IPTV
- **Método**: Compara snapshots do conteúdo atual vs anterior
- **Storage**: Salva snapshot no localStorage
- **Performance**: Usa amostragem (primeiras 3 categorias) para velocidade

### 2. **UpdateModal** (`src/components/UpdateModal.tsx`)
- **Função**: Exibe modal com detalhes das atualizações
- **Design**: Interface moderna com chips coloridos por tipo de atualização
- **Ações**: "Mais <PERSON>" ou "Atualizar Agora"

### 3. **useUpdateDetection** (`src/hooks/useUpdateDetection.ts`)
- **Função**: Hook para gerenciar estado do modal e atualizações
- **Features**: Auto-check, loading states, callbacks
- **Integração**: Conecta detector com modal

### 4. **Integração no App** (`src/App.tsx`)
- **Timing**: Verifica atualizações 2s após inicialização
- **Frequência**: Apenas na abertura do app (não fica fazendo polling)
- **Ação**: Limpa cache e recarrega página quando atualiza

## 🔄 **Fluxo de Funcionamento**

### 1. **Inicialização**
```
App inicia → Aguarda 2s → Verifica atualizações
```

### 2. **Detecção**
```
Busca categorias atuais → Conta conteúdo (amostra) → Compara com snapshot → Detecta mudanças
```

### 3. **Snapshot**
```json
{
  "channels": 150,
  "movies": 80,
  "series": 45,
  "categories": 25,
  "channelCategories": 12,
  "movieCategories": 8,
  "seriesCategories": 5,
  "timestamp": 1753634830329,
  "hash": "abc123def456"
}
```

### 4. **Detecção de Mudanças**
- **Canais**: Novos/removidos canais
- **Filmes**: Novos/removidos filmes  
- **Séries**: Novas/removidas séries
- **Categorias**: Novas/removidas categorias por tipo

### 5. **Modal de Atualização**
```
Mudanças detectadas → Modal aparece → Usuário escolhe → Atualiza ou ignora
```

### 6. **Processo de Atualização**
```
Limpa cache IPTV → Aguarda 1s → Recarrega página → Novo conteúdo disponível
```

## 🛠️ **Comandos de Debug**

### Console do Navegador:
```javascript
// Verifica atualizações manualmente
debugIptvUpdates.checkUpdates()

// Força nova verificação (limpa snapshot)
debugIptvUpdates.forceCheck()

// Mostra último snapshot salvo
debugIptvUpdates.getSnapshot()

// Limpa cache IPTV
debugIptvUpdates.clearCache()

// Simula atualização para teste
debugIptvUpdates.simulateUpdate()
```

## 📊 **Tipos de Atualização**

### **Adicionado** (Verde)
- ✅ Novos canais, filmes, séries ou categorias
- Exemplo: "+5 Canais Adultos adicionados"

### **Removido** (Vermelho)  
- ❌ Conteúdo removido do servidor
- Exemplo: "-2 Categorias removidas"

### **Atualizado** (Amarelo)
- 🔄 Mudanças em conteúdo existente
- Exemplo: "Categorias atualizadas"

## 🎨 **Interface do Modal**

```
┌─────────────────────────────────────┐
│ 🔄 Atualizações Disponíveis        │
│    5 itens atualizados              │
├─────────────────────────────────────┤
│                                     │
│ 📺 Canais                          │
│ [+] Adicionado (3) - Canais Adultos│
│                                     │
│ 🎬 Filmes                          │
│ [+] Adicionado (12) - Novos Filmes │
│                                     │
│ 📚 Categorias                      │
│ [+] Adicionado (1) - Nova Categoria│
│                                     │
│ 💡 As atualizações incluem novos   │
│    canais, filmes e séries...       │
│                                     │
├─────────────────────────────────────┤
│           [Mais Tarde] [Atualizar] │
└─────────────────────────────────────┘
```

## ⚡ **Performance**

### **Otimizações Implementadas**:
- ✅ **Amostragem**: Só verifica primeiras 3 categorias
- ✅ **Hash Comparison**: Compara hash antes de análise detalhada
- ✅ **Single Check**: Só verifica na abertura do app
- ✅ **Async Loading**: Não bloqueia inicialização
- ✅ **Error Handling**: Falhas não quebram o app

### **Tempo Estimado**:
- Verificação rápida (hash): ~100ms
- Verificação completa: ~2-5s
- Atualização: ~3s (com reload)

## 🔧 **Configuração**

### **Personalizar Verificação**:
```typescript
const updateDetection = useUpdateDetection({
  autoCheck: true,        // Auto-verifica
  checkOnMount: true,     // Verifica ao montar
  onUpdateDetected: (updates) => {
    console.log('Updates:', updates);
  },
  onUpdateComplete: () => {
    console.log('Atualização concluída');
  }
});
```

### **Desabilitar Sistema**:
```typescript
// Em App.tsx, comentar:
// const updateDetection = useUpdateDetection({...});
```

## 🧪 **Como Testar**

### 1. **Teste Manual**:
```javascript
// 1. Simula atualização
debugIptvUpdates.simulateUpdate()

// 2. Força verificação
debugIptvUpdates.forceCheck()

// 3. Verifica atualizações
debugIptvUpdates.checkUpdates()
```

### 2. **Teste Real**:
1. Adicione conteúdo no painel IPTV
2. Recarregue o app
3. Modal deve aparecer automaticamente

### 3. **Teste de Cache**:
```javascript
// Limpa cache e força reload
debugIptvUpdates.clearCache()
window.location.reload()
```

## 📈 **Cenários de Uso**

### **Cenário 1: Conteúdo Adulto Adicionado**
```
Painel: Adiciona categoria "Adulto" com 50 canais
App: Detecta +1 categoria, +50 canais
Modal: "📺 Canais: Adicionado (50), 📚 Categorias: Adicionado (1)"
Ação: Usuário clica "Atualizar" → Cache limpo → Novo conteúdo disponível
```

### **Cenário 2: Filmes Removidos**
```
Painel: Remove categoria de filmes antigos
App: Detecta -1 categoria, -25 filmes  
Modal: "🎬 Filmes: Removido (25), 📚 Categorias: Removido (1)"
Ação: Usuário atualiza → Conteúdo removido não aparece mais
```

### **Cenário 3: Sem Mudanças**
```
App: Hash igual ao anterior
Resultado: Nenhum modal, verificação silenciosa
Performance: ~100ms, não impacta UX
```

## ✅ **Status de Implementação**

- 🟢 **IptvUpdateDetector**: Implementado e testado
- 🟢 **UpdateModal**: Interface completa
- 🟢 **useUpdateDetection**: Hook funcional
- 🟢 **Integração App**: Conectado e ativo
- 🟢 **Debug Tools**: Comandos disponíveis
- 🟢 **Performance**: Otimizado para velocidade
- 🟢 **Error Handling**: Robusto contra falhas

O sistema está **100% funcional** e pronto para detectar atualizações IPTV automaticamente! 🎉
