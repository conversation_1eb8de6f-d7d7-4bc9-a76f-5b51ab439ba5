// Responsive Design System
// Mobile-first approach with progressive enhancement

// Breakpoints
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// Media query mixins
@mixin mobile-only {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: 768px) and (max-width: 991px) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: 992px) {
    @content;
  }
}

@mixin mobile-and-tablet {
  @media (max-width: 991px) {
    @content;
  }
}

@mixin tablet-and-desktop {
  @media (min-width: 768px) {
    @content;
  }
}

// Touch device optimizations
@mixin touch-device {
  @media (hover: none) and (pointer: coarse) {
    @content;
  }
}

@mixin hover-device {
  @media (hover: hover) and (pointer: fine) {
    @content;
  }
}

// Orientation mixins
@mixin landscape {
  @media (orientation: landscape) {
    @content;
  }
}

@mixin portrait {
  @media (orientation: portrait) {
    @content;
  }
}

// High DPI displays
@mixin retina {
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    @content;
  }
}

// Container responsive classes
.container-responsive {
  width: 100%;
  padding: 0 1rem;
  margin: 0 auto;
  
  @include tablet-and-desktop {
    padding: 0 2rem;
    max-width: 1200px;
  }
  
  @include desktop-only {
    padding: 0 3rem;
    max-width: 1400px;
  }
}

// Responsive spacing system
.spacing-responsive {
  &.xs { margin: 0.25rem; }
  &.sm { margin: 0.5rem; }
  &.md { margin: 1rem; }
  &.lg { margin: 1.5rem; }
  &.xl { margin: 2rem; }
  
  @include mobile-only {
    &.xs { margin: 0.125rem; }
    &.sm { margin: 0.25rem; }
    &.md { margin: 0.5rem; }
    &.lg { margin: 0.75rem; }
    &.xl { margin: 1rem; }
  }
  
  @include desktop-only {
    &.lg { margin: 2rem; }
    &.xl { margin: 3rem; }
  }
}

// Responsive typography
.text-responsive {
  &.h1 {
    font-size: 1.75rem;
    line-height: 1.2;
    
    @include mobile-only {
      font-size: 1.5rem;
    }
    
    @include desktop-only {
      font-size: 2.25rem;
    }
  }
  
  &.h2 {
    font-size: 1.5rem;
    line-height: 1.3;
    
    @include mobile-only {
      font-size: 1.25rem;
    }
    
    @include desktop-only {
      font-size: 1.875rem;
    }
  }
  
  &.body {
    font-size: 1rem;
    line-height: 1.5;
    
    @include mobile-only {
      font-size: 0.875rem;
    }
    
    @include desktop-only {
      font-size: 1.125rem;
    }
  }
  
  &.caption {
    font-size: 0.75rem;
    line-height: 1.4;
    
    @include mobile-only {
      font-size: 0.6875rem;
    }
    
    @include desktop-only {
      font-size: 0.875rem;
    }
  }
}

// Responsive grid system
.grid-responsive {
  display: grid;
  gap: 1rem;
  
  // Mobile: 1-2 columns
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  
  @include mobile-only {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }
  
  @include tablet-only {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
  }
  
  @include desktop-only {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  // Specific grid variants
  &.grid-2 {
    grid-template-columns: repeat(2, 1fr);
    
    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
  
  &.grid-3 {
    grid-template-columns: repeat(3, 1fr);
    
    @include mobile-only {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include tablet-only {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  &.grid-4 {
    grid-template-columns: repeat(4, 1fr);
    
    @include mobile-only {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include tablet-only {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// Responsive buttons
.btn-responsive {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 0.5rem;
  min-height: 44px; // Touch-friendly
  transition: all 0.2s ease;
  
  @include mobile-only {
    padding: 0.875rem 1.75rem;
    font-size: 1.125rem;
    min-height: 48px;
    border-radius: 0.75rem;
  }
  
  @include touch-device {
    min-height: 48px;
    padding: 1rem 2rem;
  }
  
  @include hover-device {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// Responsive cards
.card-responsive {
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
  
  @include mobile-only {
    border-radius: 0.5rem;
  }
  
  @include hover-device {
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  @include touch-device {
    &:active {
      transform: scale(0.98);
    }
  }
}

// Video player responsive styles
.video-player-responsive {
  position: relative;
  width: 100%;
  background: #000;
  
  // Aspect ratio containers
  &.aspect-16-9 {
    aspect-ratio: 16/9;
  }
  
  &.aspect-4-3 {
    aspect-ratio: 4/3;
  }
  
  // Fullscreen optimizations
  &.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    
    video {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
    }
  }
  
  // Mobile-specific player styles
  @include mobile-only {
    &.mobile-optimized {
      .plyr__controls {
        padding: 1rem !important;
        background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%) !important;
      }
      
      .plyr__control {
        min-height: 48px !important;
        min-width: 48px !important;
      }
      
      .plyr__volume {
        display: none !important;
      }
    }
  }
  
  // Landscape mobile optimization
  @include mobile-only {
    @include landscape {
      &.landscape-optimized {
        height: 100vh;
        
        .plyr__controls {
          padding: 0.5rem 1rem !important;
        }
      }
    }
  }
}

// Hide/show elements based on screen size
.hide-mobile {
  @include mobile-only {
    display: none !important;
  }
}

.hide-tablet {
  @include tablet-only {
    display: none !important;
  }
}

.hide-desktop {
  @include desktop-only {
    display: none !important;
  }
}

.show-mobile-only {
  display: none !important;
  
  @include mobile-only {
    display: block !important;
  }
}

.show-tablet-only {
  display: none !important;
  
  @include tablet-only {
    display: block !important;
  }
}

.show-desktop-only {
  display: none !important;
  
  @include desktop-only {
    display: block !important;
  }
}
