import { createClient } from '@supabase/supabase-js';
import { dbService } from './dbService';
import { getSupabaseClient } from './deviceService';

// Usar o cliente Supabase compartilhado em vez de criar um novo
const supabase = getSupabaseClient();

// Interface para o tipo de dados da lista IPTV no Supabase
interface IPTVConfig {
  id: number;
  url: string;
  username?: string;
  password?: string;
  updated_at: string;
  config_name?: string;
  active: boolean;
}

class IPTVUpdateService {
  private static instance: IPTVUpdateService;
  private checkInterval: number = 5 * 60 * 1000; // Verificar a cada 5 minutos
  private intervalId: NodeJS.Timeout | null = null;
  private lastCheckedHash: string | null = null;

  private constructor() {}

  public static getInstance(): IPTVUpdateService {
    if (!IPTVUpdateService.instance) {
      IPTVUpdateService.instance = new IPTVUpdateService();
    }
    return IPTVUpdateService.instance;
  }

  /**
   * Inicia o serviço de verificação periódica de atualizações da URL IPTV
   */
  public startUpdateCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    // Verificar imediatamente ao iniciar
    this.checkForUpdates();

    // Configurar verificação periódica
    this.intervalId = setInterval(() => {
      this.checkForUpdates();
    }, this.checkInterval);

    console.log(`Serviço de verificação de atualizações IPTV iniciado. Intervalo: ${this.checkInterval / 1000} segundos`);
  }

  /**
   * Para o serviço de verificação periódica
   */
  public stopUpdateCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('Serviço de verificação de atualizações IPTV parado');
    }
  }

  /**
   * Verifica se há atualizações disponíveis para a URL IPTV
   */
  private async checkForUpdates(): Promise<void> {
    try {
      console.log('Verificando atualizações na configuração IPTV...');

      // Obter a configuração IPTV atual do usuário
      const currentConnection = await dbService.getConnection();
      if (!currentConnection?.url) {
        console.log('Nenhuma conexão IPTV configurada localmente');
        return;
      }

      // Consultar o Supabase para obter a configuração mais recente
      const { data, error } = await supabase
        .from('iptv_configs')
        .select('*')
        .eq('active', true)
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('Erro ao verificar atualizações IPTV:', error);
        return;
      }

      if (!data) {
        console.log('Nenhuma configuração IPTV ativa encontrada no servidor');
        return;
      }

      const serverConfig = data as IPTVConfig;
      
      // Verificar se a URL, username ou password foram alterados
      if (
        serverConfig.url !== currentConnection.url ||
        serverConfig.username !== currentConnection.username ||
        serverConfig.password !== currentConnection.password
      ) {
        console.log('Nova configuração IPTV detectada no servidor');
        
        // Atualizar a conexão local
        await this.updateLocalConnection(serverConfig);
        
        // Mostrar notificação ao usuário
        this.showUpdateNotification(serverConfig);
      } else {
        console.log('A configuração IPTV está atualizada');
      }
    } catch (error) {
      console.error('Erro ao verificar atualizações IPTV:', error);
    }
  }

  /**
   * Atualiza a conexão local com os dados do servidor
   */
  private async updateLocalConnection(config: IPTVConfig): Promise<void> {
    try {
      // Atualizar a conexão no banco de dados local
      await dbService.setConnection({
        url: config.url,
        username: config.username,
        password: config.password,
        type: 'url',
        format: 'm3u',
        autoLogin: true,
        lastUsed: Date.now()
      });
      
      console.log('Conexão IPTV local atualizada com sucesso');
      
      // Limpar caches para forçar atualização da playlist
      await dbService.clearCache();
    } catch (error) {
      console.error('Erro ao atualizar conexão IPTV local:', error);
    }
  }

  /**
   * Exibe uma notificação ao usuário sobre a atualização da lista IPTV
   */
  private showUpdateNotification(config: IPTVConfig): void {
    // Criar elemento de notificação
    const notificationEl = document.createElement('div');
    notificationEl.className = 'iptv-update-notification';
    notificationEl.innerHTML = `
      <div class="notification-content">
        <h3>Atualização da Lista IPTV</h3>
        <p>O administrador atualizou sua lista de canais IPTV. As alterações foram aplicadas automaticamente.</p>
        <div class="notification-actions">
          <button id="reload-app-btn">Recarregar Agora</button>
          <button id="dismiss-notification-btn">Mais Tarde</button>
        </div>
      </div>
    `;

    // Estilizar a notificação
    const style = document.createElement('style');
    style.textContent = `
      .iptv-update-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        max-width: 400px;
        background-color: #1e293b;
        border-left: 4px solid #0ea5e9;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        overflow: hidden;
        animation: slideIn 0.3s ease-out;
      }
      
      .notification-content {
        padding: 16px;
      }
      
      .iptv-update-notification h3 {
        margin: 0 0 8px;
        color: #f8fafc;
        font-size: 18px;
        font-weight: 600;
      }
      
      .iptv-update-notification p {
        margin: 0 0 16px;
        color: #cbd5e1;
        font-size: 14px;
        line-height: 1.5;
      }
      
      .notification-actions {
        display: flex;
        gap: 8px;
      }
      
      .notification-actions button {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      
      #reload-app-btn {
        background-color: #0ea5e9;
        color: white;
      }
      
      #reload-app-btn:hover {
        background-color: #0284c7;
      }
      
      #dismiss-notification-btn {
        background-color: transparent;
        color: #94a3b8;
        border: 1px solid #334155;
      }
      
      #dismiss-notification-btn:hover {
        background-color: #334155;
        color: #cbd5e1;
      }
      
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;

    // Adicionar à página
    document.head.appendChild(style);
    document.body.appendChild(notificationEl);

    // Adicionar event listeners aos botões
    document.getElementById('reload-app-btn')?.addEventListener('click', () => {
      window.location.reload();
    });

    document.getElementById('dismiss-notification-btn')?.addEventListener('click', () => {
      notificationEl.remove();
    });

    // Remover automaticamente após 30 segundos se o usuário não interagir
    setTimeout(() => {
      if (document.body.contains(notificationEl)) {
        notificationEl.remove();
      }
    }, 30000);
  }

  /**
   * Força uma verificação manual de atualizações
   */
  public async checkUpdatesNow(): Promise<boolean> {
    try {
      // Obter a configuração IPTV atual do usuário
      const currentConnection = await dbService.getConnection();
      if (!currentConnection?.url) {
        return false;
      }

      // Consultar o Supabase para obter a configuração mais recente
      const { data, error } = await supabase
        .from('iptv_configs')
        .select('*')
        .eq('active', true)
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) {
        return false;
      }

      const serverConfig = data as IPTVConfig;
      
      // Verificar se a URL, username ou password foram alterados
      if (
        serverConfig.url !== currentConnection.url ||
        serverConfig.username !== currentConnection.username ||
        serverConfig.password !== currentConnection.password
      ) {
        // Atualizar a conexão local
        await this.updateLocalConnection(serverConfig);
        
        // Mostrar notificação ao usuário
        this.showUpdateNotification(serverConfig);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao verificar atualizações IPTV:', error);
      return false;
    }
  }
}

export const iptvUpdateService = IPTVUpdateService.getInstance();
