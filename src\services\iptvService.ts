import { dbService } from './dbService';
import { getSafeUrl } from '../utils/proxyUrl';
import moment from 'moment';
import storageService from './storageService';

// Interfaces
interface ChunkInfo {
  currentChunk: number;
  totalChunks: number;
  itemsPerChunk: number;
}

export interface Category {
  id: string;
  name: string;
}

export interface Stream {
  id: string;
  name: string;
  url?: string;
  cover?: string;
  thumbnail?: string;
  containerExtension?: string;
  direct_source?: string;
  stream_id?: string;
  username?: string;
  password?: string;
  description?: string;
  rating?: number;
  year?: string;
  genres?: string[];
  episodeCount?: number;
  episodeRun?: number;
  lastModified?: string;
  status?: string;
  backdropPath?: string;
  duration?: number;
  director?: string;
  actors?: string;
}

export interface EPGChannel {
  id: string;
  name: string;
  programs: EPGProgram[];
}

export interface EPGProgram {
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  _originalStart?: string;
  _originalEnd?: string;
}

export interface Episode {
  id: string;
  title: string;
  season: number;
  episode: number;
  url: string;
  thumbnail?: string;
  description?: string;
  duration?: number;
  containerExtension?: string;
  direct_source?: string;
  added?: string;
  releaseDate?: string;
  rating?: number;
}

export interface Season {
  id: number;
  name: string;
  episodes: Episode[];
  episodeCount: number;
}

export interface UserInfo {
  username: string;
  password: string;
  message: string;
  auth: number;
  status: string;
  exp_date: string;
  is_trial: string;
  active_cons: string;
  created_at: string;
  max_connections: string;
  allowed_output_formats: string[];
  formattedExpDate?: string;
  formattedCreatedAt?: string;
  isExpired?: boolean;
}

export interface ServerInfo {
  url: string;
  port: string;
  https_port: string;
  server_protocol: string;
  rtmp_port: string;
  timezone: string;
  timestamp_now: number;
  time_now: string;
  process: boolean;
}

interface EPGInfo {
  id: string;
  title: string;
  description: string;
  start_timestamp: number;
  stop_timestamp: number;
  channel_id: string;
  lang: string;
  category: string;
  start: string;
  end: string;
  now_playing?: boolean;
  has_archive?: boolean;
}

interface EPGListing {
  id: string;
  title: string;
  description: string;
  start_timestamp: number;
  stop_timestamp: number;
  channel_id: string;
  lang: string;
  category: string;
  name: string;
  icon?: string;
  epg_id?: string;
}

interface EPGResponse {
  epg_listings: EPGListing[];
}

interface XMLTVChannel {
  id: string | null;
  displayName: string;
  icon: string;
}

interface XMLTVProgramme {
  start: string | null;
  stop: string | null;
  channel: string | null;
  title: string;
  desc?: string | null;
}

interface XMLTV {
  channels: XMLTVChannel[];
  programmes: XMLTVProgramme[];
}

const CORS_PROXY = 'https://corsfook-9wnyvhw4h-fes-projects-3eaeabe4.vercel.app/api/xtream';

const CHUNK_SIZE = 100; // Reduced chunk size
const MAX_CHUNKS_IN_STORAGE = 10; // Limit number of chunks
const CACHE_PREFIX = 'iptv_playlist_chunk_';

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const MAX_RETRIES = 3;
const INITIAL_DELAY = 1000; // 1 second

export interface IPTVService {
  authenticate(): Promise<boolean>;
  getLiveCategories(): Promise<Category[]>;
  getMovieCategories(): Promise<Category[]>;
  getSeriesCategories(): Promise<Category[]>;
  getLiveStreams(categoryId: string): Promise<Stream[]>;
  getMovieStreams(categoryId: string): Promise<Stream[]>;
  getSeriesStreams(categoryId: string): Promise<Stream[]>;
  getEPG(): Promise<EPGChannel[]>;
  getStreamUrl(streamId: string, streamType: 'live' | 'movie' | 'series', direct_source?: string): string;
  getEPGUrl(): string;
  getUserInfo(): Promise<{ user_info: UserInfo; server_info: ServerInfo } | null>;
  getFullEPG(): Promise<EPGChannel[]>;
  getSeriesSeasons(seriesId: string): Promise<Season[]>;
  getSeriesEpisodes(seriesId: string, seasonNumber: number): Promise<Episode[]>;
  getEpisodes(seriesId: string, seasonId: string): Promise<Episode[]>;
  clearCache(): Promise<void>;
  refreshCache(type: 'epg' | 'categories' | 'streams' | 'all', categoryId?: string): Promise<void>;
  diagnoseEPGMatching(channelName: string): Promise<{
    channelName: string;
    normalizedName: string;
    possibleMatches: Array<{
      epgName: string;
      normalizedEpgName: string;
      similarity: number;
      wouldMatch: boolean;
    }>;
  }>;
  getMovieInfo(movieId: string): Promise<Stream | null>;
  getSeriesInfo(seriesId: string): Promise<any | null>;
  getConnection(): { url: string; username?: string; password?: string } | null;
  updateConnection(url: string, username?: string, password?: string): Promise<boolean>;
}

export class IPTVServiceImpl implements IPTVService {
  private baseUrl: string;
  private username: string;
  private password: string;
  private playlist: any[] = [];
  private m3uCache: string | null = null;

  // Queue para controlar as requisições de informações dos filmes
  private movieInfoQueue: { [key: string]: Promise<any> } = {};
  private lastRequestTime = 0;
  private REQUEST_DELAY = 500; // 500ms entre requisições

  private movieInfoRetries: { [key: string]: number } = {};
  private MAX_RETRIES_PER_MOVIE = 2;

  constructor(baseUrl: string, username: string, password: string) {
    this.baseUrl = baseUrl;
    this.username = username;
    this.password = password;
  }

  private async fetchWithRetry(url: string, retryCount = 0): Promise<any> {
    try {
      // Reduce logging to improve performance
      if (retryCount > 0) {
        console.log(`🔄 Attempt ${retryCount + 1} for URL:`, url);
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
          'User-Agent': 'IPTV Player'
        }
      });

      if (response.status === 429 && retryCount < MAX_RETRIES) {
        const delay = INITIAL_DELAY * Math.pow(2, retryCount);
        console.log(`⏳ Rate limited. Waiting ${delay}ms before retry...`);
        await sleep(delay);
        return this.fetchWithRetry(url, retryCount + 1);
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Minimize unnecessary logging
      const contentType = response.headers.get('content-type');
      const data = await response.text();

      if (!data.trim()) {
        return [];
      }

      if (contentType?.includes('application/json')) {
        return JSON.parse(data);
      }

      if (url.includes('type=m3u') || data.trim().startsWith('#EXTM3U')) {
        return data;
      }

      try {
        return JSON.parse(data);
      } catch {
        return data;
      }
    } catch (error) {
      console.error('❌ Error in fetch:', error);
      throw error;
    }
  }

  private buildApiUrl(): string {
    // Sempre certifique-se de que estamos usando player_api.php para obter informações do usuário
    if (!this.username || !this.password) {
      return `${this.baseUrl}/player_api.php`;
    }
    
    // Garantir que usamos um endpoint que fornece informações completas do usuário
    return `${this.baseUrl}/player_api.php?username=${encodeURIComponent(this.username)}&password=${encodeURIComponent(this.password)}`;
  }

  private buildPlayerApiUrl(action?: string, params?: Record<string, string>): string {
    const baseUrl = this.buildApiUrl();
    
    if (!action) {
      return baseUrl;
    }
    
    // Start with the base URL which already has username and password
    let url = baseUrl;
    
    // Add action parameter
    url += url.includes('?') ? '&' : '?';
    url += `action=${action}`;
    
    // Add additional parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url += `&${key}=${value}`;
      });
    }
    
    return url;
  }

  private async fetchWithProxy(url: string): Promise<any> {
    try {
      console.log('🔄 fetchWithProxy called with URL:', url);
      
      if (url.includes('username=') && url.includes('password=')) {
        console.log('📍 URL contains auth params, using directly');
        return this.fetchWithRetry(url);
      }

      const fullUrl = `${this.baseUrl}/player_api.php?username=${this.username}&password=${this.password}`;
      console.log('🌐 Fetching with base URL:', fullUrl);
      return this.fetchWithRetry(fullUrl);
    } catch (error) {
      console.error('❌ Error in fetchWithProxy:', error);
      throw error;
    }
  }

  private async cleanStorage(): Promise<void> {
    await dbService.clearPlaylistChunks();
  }

  private async storeChunk(chunk: any[], index: number): Promise<boolean> {
    try {
      console.log(`💾 Attempting to store chunk ${index} with ${chunk.length} items`);
      await dbService.setPlaylistChunk(index, chunk);
      console.log(`✅ Successfully stored chunk ${index}`);
      return true;
    } catch (error) {
      if (error instanceof Error) {
        console.error(`❌ Failed to store chunk ${index}:`, error.message);
      }
      return false;
    }
  }

  async authenticate(): Promise<boolean> {
    try {
      const response = await this.fetchWithRetry(this.buildApiUrl());
      return response?.user_info != null;
    } catch (error) {
      console.error('Authentication error:', error);
      return false;
    }
  }

  private parseM3U(content: string): any[] {
    console.log('📝 Starting M3U parsing...');
    const lines = content.split('\n');
    console.log(`📋 Total lines in playlist: ${lines.length}`);
    
    const items: any[] = [];
    let currentItem: any = null;
    let lineCount = 0;
    
    for (const line of lines) {
      lineCount++;
      if (lineCount % 100 === 0) {
        console.log(`🔄 Processing line ${lineCount}/${lines.length}`);
      }

      if (line.startsWith('#EXTINF:')) {
        try {
          const info = line.split(',')[1];
          const groupMatch = line.match(/group-title="([^"]+)"/);
          const logoMatch = line.match(/tvg-logo="([^"]+)"/);
          
          currentItem = {
            title: info?.trim() || 'Unknown',
            group: groupMatch?.[1] || 'Uncategorized',
            logo: logoMatch?.[1],
            type: 'channel'
          };
          console.log('📺 Found channel:', currentItem.title);
        } catch (error) {
          console.warn('⚠️ Error parsing EXTINF line:', error);
          currentItem = null;
        }
      } else if (line.trim() && !line.startsWith('#') && currentItem) {
        try {
          new URL(line.trim());
          currentItem.url = line.trim();
          
          const group = currentItem.group?.toLowerCase() || '';
          if (group.includes('movie')) {
            currentItem.type = 'movie';
          } else if (group.includes('series')) {
            currentItem.type = 'series';
          }
          
          items.push(currentItem);
          console.log(`✅ Added ${currentItem.type}: ${currentItem.title}`);
        } catch (error) {
          console.warn('⚠️ Invalid URL:', line.trim());
        }
        currentItem = null;
      }
    }

    console.log(`✅ Parsing complete. Found ${items.length} valid items`);
    return items;
  }

  private async saveChunkInfo(info: ChunkInfo): Promise<void> {
    await storageService.set('iptv_playlist_chunk_info', info);
  }

  private async getChunkInfo(): Promise<ChunkInfo | null> {
    try {
      return await storageService.get<ChunkInfo>('iptv_playlist_chunk_info');
    } catch {
      return null;
    }
  }

  async getPlaylistChunk(chunkIndex: number): Promise<any[]> {
    try {
      const chunk = await dbService.getPlaylistChunk(chunkIndex);
      return chunk || [];
    } catch (error) {
      console.error(`Error loading chunk ${chunkIndex}:`, error);
      return [];
    }
  }

  async getPlaylistInfo(): Promise<any> {
    try {
      return await dbService.getPlaylistInfo();
    } catch (error) {
      console.error('Error loading playlist info:', error);
      return null;
    }
  }

  private buildProxyUrl(baseUrl: string, params: Record<string, string>): string {
    // Constrói a URL base com os parâmetros sem codificação dupla
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    
    const apiUrl = `${baseUrl}/player_api.php?${queryString}`;
    
    // Codifica a URL uma única vez para o proxy
    return `${CORS_PROXY}?url=${encodeURIComponent(apiUrl)}`;
  }

  private async fetchWithFallback(url: string): Promise<any> {
    try {
      // Primeira tentativa: direto
      const response = await fetch(url);
      const data = await response.json();
      return data;
    } catch (error) {
      // Se falhar (provavelmente por CORS), tenta com o proxy
      console.log('Direct request failed, trying with proxy...');
      
      // Decodifica a URL antes de enviar para o proxy
      const decodedUrl = decodeURIComponent(url);
      
      // Remove caracteres especiais e espaços extras
      const cleanUrl = decodedUrl
        .replace(/\s+/g, '') // Remove espaços extras
        .replace(/[^\w\s\-\.\?\=\&\:\/]/g, ''); // Remove caracteres especiais mantendo alguns necessários
      
      const proxyUrl = `${CORS_PROXY}?url=${cleanUrl}`;
      console.log('Clean proxy URL:', proxyUrl);
      
      const proxyResponse = await fetch(proxyUrl);
      const data = await proxyResponse.json();
      return data;
    }
  }

  async getLiveCategories(): Promise<Category[]> {
    try {
      // Check cache first (24 hours expiration)
      const cachedData = await dbService.getCategoriesCache('live', 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      // Try Supabase edge function first
      try {
        console.log('🔄 Tentando buscar categorias via Supabase Edge Function...');
        const supabaseUrl = 'https://kzvhuqfkqoncgsyezaaq.supabase.co';
        const response = await fetch(`${supabaseUrl}/functions/v1/channels?categories=true`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data?.categories) {
            console.log('✅ Categorias obtidas via Supabase Edge Function');
            const categories = data.data.categories.map((cat: any) => ({
              id: cat.id.toString(),
              name: cat.name
            }));
            
            // Store in cache
            if (categories.length > 0) {
              await dbService.setCategoriesCache('live', categories);
            }
            
            return categories;
          }
        }
      } catch (supabaseError) {
        console.log('⚠️ Supabase Edge Function falhou, usando fallback IPTV API:', supabaseError);
      }

      // Fallback to original IPTV API
      console.log('🔄 Usando fallback para IPTV API original...');
      const response = await this.fetchWithRetry(this.buildApiUrl() + '&action=get_live_categories');
      const categories = Array.isArray(response) ? response.map(cat => ({
        id: cat.category_id,
        name: cat.category_name
      })) : [];

      // Store in cache
      if (categories.length > 0) {
        await dbService.setCategoriesCache('live', categories);
      }

      return categories;
    } catch (error) {
      console.error('Error getting live categories:', error);
      return [];
    }
  }

  async getMovieCategories(): Promise<Category[]> {
    try {
      // Check cache first (24 hours expiration)
      const cachedData = await dbService.getCategoriesCache('movie', 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      // Try Supabase edge function first
      try {
        console.log('🔄 Tentando buscar categorias de filmes via Supabase Edge Function...');
        const supabaseUrl = 'https://kzvhuqfkqoncgsyezaaq.supabase.co';
        const response = await fetch(`${supabaseUrl}/functions/v1/movies?categories=true`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data?.categories) {
            console.log('✅ Categorias de filmes obtidas via Supabase Edge Function');
            const categories = data.data.categories.map((cat: any) => ({
              id: cat.id.toString(),
              name: cat.name
            }));
            
            // Store in cache
            if (categories.length > 0) {
              await dbService.setCategoriesCache('movie', categories);
            }
            
            return categories;
          }
        }
      } catch (supabaseError) {
        console.log('⚠️ Supabase Edge Function falhou, usando fallback IPTV API:', supabaseError);
      }

      // Fallback to original IPTV API
      console.log('🔄 Usando fallback para IPTV API original...');
      const response = await this.fetchWithRetry(this.buildApiUrl() + '&action=get_vod_categories');
      const categories = Array.isArray(response) ? response.map(cat => ({
        id: cat.category_id,
        name: cat.category_name
      })) : [];

      // Store in cache
      if (categories.length > 0) {
        await dbService.setCategoriesCache('movie', categories);
      }

      return categories;
    } catch (error) {
      console.error('Error getting movie categories:', error);
      return [];
    }
  }

  async getSeriesCategories(): Promise<Category[]> {
    try {
      // Check cache first (24 hours expiration)
      const cachedData = await dbService.getCategoriesCache('series', 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      // Try Supabase edge function first
      try {
        console.log('🔄 Tentando buscar categorias de séries via Supabase Edge Function...');
        const supabaseUrl = 'https://kzvhuqfkqoncgsyezaaq.supabase.co';
        const response = await fetch(`${supabaseUrl}/functions/v1/series?categories=true`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data?.categories) {
            console.log('✅ Categorias de séries obtidas via Supabase Edge Function');
            const categories = data.data.categories.map((cat: any) => ({
              id: cat.id.toString(),
              name: cat.name
            }));
            
            // Store in cache
            if (categories.length > 0) {
              await dbService.setCategoriesCache('series', categories);
            }
            
            return categories;
          }
        }
      } catch (supabaseError) {
        console.log('⚠️ Supabase Edge Function falhou, usando fallback IPTV API:', supabaseError);
      }

      // Fallback to original IPTV API
      console.log('🔄 Usando fallback para IPTV API original...');
      const response = await this.fetchWithRetry(this.buildApiUrl() + '&action=get_series_categories');
      const categories = Array.isArray(response) ? response.map(cat => ({
        id: cat.category_id,
        name: cat.category_name
      })) : [];

      // Store in cache
      if (categories.length > 0) {
        await dbService.setCategoriesCache('series', categories);
      }

      return categories;
    } catch (error) {
      console.error('Error getting series categories:', error);
      return [];
    }
  }

  async getLiveStreams(categoryId: string): Promise<Stream[]> {
    try {
      // Check cache first (24 hours expiration)
      const cachedData = await dbService.getStreamsCache(categoryId, 'live', 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      const response = await this.fetchWithRetry(this.buildApiUrl() + '&action=get_live_streams&category_id=' + categoryId);
      const streams = Array.isArray(response) ? response.map(stream => ({
        id: stream.stream_id,
        name: stream.name,
        url: this.getStreamUrl(stream.stream_id, 'live'),
        thumbnail: stream.stream_icon
      })) : [];

      // Store in cache
      if (streams.length > 0) {
        await dbService.setStreamsCache(categoryId, 'live', streams);
      }

      return streams;
    } catch (error) {
      console.error('Error getting live streams:', error);
      return [];
    }
  }

  async getMovieStreams(categoryId: string): Promise<Stream[]> {
    try {
      // Check cache first (24 hours expiration)
      const cachedData = await dbService.getStreamsCache(categoryId, 'movie', 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      const response = await this.fetchWithRetry(this.buildApiUrl() + '&action=get_vod_streams&category_id=' + categoryId);
      const streams = Array.isArray(response) ? response.map(stream => {
        // Process cover and backdrop paths
        const coverPath = typeof stream.stream_icon === 'string' ? stream.stream_icon : '';
        const backdropPath = typeof stream.backdrop_path === 'string' ? stream.backdrop_path : '';
        
        // Process cover URL
        const cover = coverPath.startsWith('http') 
          ? coverPath 
          : coverPath.startsWith('/') 
            ? `https://image.tmdb.org/t/p/w600_and_h900_bestv2${coverPath}`
            : coverPath 
              ? `https://image.tmdb.org/t/p/w600_and_h900_bestv2/${coverPath}`
              : '';
            
        // Process backdrop URL
        const backdrop = backdropPath.startsWith('http') 
          ? backdropPath 
          : backdropPath.startsWith('/') 
            ? `https://image.tmdb.org/t/p/w1280${backdropPath}`
            : backdropPath
              ? `https://image.tmdb.org/t/p/w1280/${backdropPath}`
              : '';

        // Retorna o objeto sem tentar buscar informações adicionais
        return {
          id: stream.stream_id,
          name: stream.name,
          url: stream.direct_source || '',
          thumbnail: cover || backdrop,
          description: stream.plot || stream.overview || '',
          rating: parseFloat(stream.rating) || 0,
          year: stream.year || stream.release_date?.substring(0, 4) || '',
          genres: stream.genre?.split(',').map((g: string) => g.trim()) || [],
          cover: cover,
          duration: parseInt(stream.duration) || 0,
          director: stream.director || '',
          actors: stream.cast || stream.actors || '',
          backdropPath: backdrop,
          containerExtension: stream.container_extension || 'mp4'
        };
      }) : [];

      // Store in cache
      if (streams.length > 0) {
        await dbService.setStreamsCache(categoryId, 'movie', streams);
      }

      return streams;
    } catch (error) {
      console.error('Error getting movie streams:', error);
      return [];
    }
  }

  private async delayIfNeeded(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.REQUEST_DELAY) {
      await new Promise(resolve => setTimeout(resolve, this.REQUEST_DELAY - timeSinceLastRequest));
    }
    this.lastRequestTime = Date.now();
  }

  async getMovieInfo(movieId: string): Promise<Stream | null> {
    try {
      // Check cache first (24 hours expiration)
      const cacheKey = `movie_info_${movieId}`;
      const cachedData = await dbService.getEPGCache(cacheKey, 86400000); // 24 hours
      if (cachedData) {
        return cachedData as Stream;
      }

      const params = new URLSearchParams({
        action: 'get_vod_info',
        vod_id: movieId
      });
      
      const url = this.buildApiUrl() + '&' + params.toString();
      
      const response = await this.fetchWithRetry(url);
      
      if (!response?.info || !response?.movie_data) {
        return null;
      }

      // Map the response to a Stream object
      const movieInfo: Stream = {
        id: response.info.stream_id || response.movie_data.stream_id || movieId,
        name: response.info.name || response.movie_data.name,
        cover: response.info.cover || response.movie_data.cover,
        thumbnail: response.info.movie_image || response.movie_data.movie_image,
        description: response.info.plot || response.movie_data.plot,
        rating: parseFloat(response.info.rating || response.movie_data.rating) || undefined,
        year: response.info.releasedate || response.movie_data.releasedate,
        genres: response.info.genre ? response.info.genre.split(',').map((g: string) => g.trim()) : undefined,
        director: response.info.director || response.movie_data.director,
        actors: response.info.cast || response.movie_data.cast,
        duration: parseInt(response.info.duration || response.movie_data.duration) || undefined,
        direct_source: response.movie_data.stream_url || response.info.stream_url
      };

      // Store in cache
      await dbService.setEPGCache(cacheKey, movieInfo);

      return movieInfo;
    } catch (error) {
      console.error('Error getting movie info:', error);
      return null;
    }
  }

  async getSeriesStreams(categoryId: string): Promise<Stream[]> {
    try {
      // Check cache first (24 hours expiration)
      const cachedData = await dbService.getStreamsCache(categoryId, 'series', 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      const url = categoryId 
        ? this.buildApiUrl() + '&action=get_series&category_id=' + categoryId
        : this.buildApiUrl() + '&action=get_series';
        
      const response = await this.fetchWithRetry(url);
      const streams = Array.isArray(response) ? response.map(stream => {
        // Ensure cover and backdrop_path are strings
        const coverPath = typeof stream.cover === 'string' ? stream.cover : 
                         typeof stream.poster_path === 'string' ? stream.poster_path : 
                         '';
        
        const backdropPath = typeof stream.backdrop_path === 'string' ? stream.backdrop_path : '';
        
        // Process cover URL
        const cover = coverPath.startsWith('http') 
          ? coverPath 
          : coverPath.startsWith('/') 
            ? `https://image.tmdb.org/t/p/w600_and_h900_bestv2${coverPath}`
            : coverPath 
              ? `https://image.tmdb.org/t/p/w600_and_h900_bestv2/${coverPath}`
              : '';
            
        // Process backdrop URL
        const backdrop = backdropPath.startsWith('http') 
          ? backdropPath 
          : backdropPath.startsWith('/') 
            ? `https://image.tmdb.org/t/p/w1280${backdropPath}`
            : backdropPath
              ? `https://image.tmdb.org/t/p/w1280/${backdropPath}`
              : '';
        
        return {
          id: stream.series_id,
          name: stream.name,
          // Não há URL direta para séries, apenas para episódios
          thumbnail: cover || backdrop,
          cover: cover,
          backdropPath: backdrop,
          description: stream.plot || '',
          rating: parseFloat(stream.rating) || 0,
          year: stream.year || '',
          genres: stream.genre?.split(',').map((g: string) => g.trim()) || [],
          episodeCount: parseInt(stream.episode_count) || 0,
          episodeRun: parseInt(stream.episode_run_time) || 0,
          lastModified: stream.last_modified || '',
          status: stream.status || '',
        };
      }) : [];

      // Store in cache
      if (streams.length > 0) {
        await dbService.setStreamsCache(categoryId, 'series', streams);
      }

      return streams;
    } catch (error) {
      console.error('Error getting series streams:', error);
      return [];
    }
  }

  getStreamUrl(streamId: string, streamType: 'live' | 'movie' | 'series', direct_source?: string): string {
    try {
      let streamUrl: string;
      
      // If direct_source is provided, use it directly
      if (direct_source) {
        streamUrl = direct_source;
      } else {
        // For live streams, use m3u8
        if (streamType === 'live') {
          streamUrl = `${this.baseUrl}/live/${this.username}/${this.password}/${streamId}.m3u8`;
        }
        // For movies, use the original service URL
        else if (streamType === 'movie') {
          streamUrl = `${this.baseUrl}/movie/${this.username}/${this.password}/${streamId}.mp4`;
        }
        // For series, use the original service URL
        else if (streamType === 'series') {
          streamUrl = `${this.baseUrl}/series/${this.username}/${this.password}/${streamId}.mp4`;
        } else {
          throw new Error('Invalid stream type');
        }
      }
      
      console.log(`📺 Stream URL gerado (${streamType}): ${streamUrl}`);
      
      // Retornar a URL diretamente sem proxy
      return streamUrl;
    } catch (error) {
      console.error('Error generating stream URL:', error);
      throw new Error('Failed to generate stream URL');
    }
  }

  getEPGUrl(): string {
    const params = new URLSearchParams({
      username: this.username,
      password: this.password
    });
    return `${this.baseUrl}/xmltv.php?${params.toString()}`;
  }

  private async getStoredConfig(): Promise<{ url: string; username?: string; password?: string } | null> {
    try {
      const config = await dbService.getConnection();
      return config || null;
    } catch {
      return null;
    }
  }

  private buildUrl(config: { url: string; username?: string; password?: string }, action: string): string {
    const { url, username = '', password = '' } = config;
    const params = new URLSearchParams({
      username,
      password,
      action
    });
    return `${url}?${params.toString()}`;
  }

  private async getStoredPlaylist(): Promise<any[] | null> {
    try {
      const info = await dbService.getPlaylistInfo();
      if (!info) return null;

      const chunks = [];
      for (let i = 0; i < info.totalChunks; i++) {
        const chunk = await dbService.getPlaylistChunk(i);
        if (chunk) chunks.push(...chunk);
      }
      return chunks;
    } catch {
      return null;
    }
  }

  async getUserInfo(): Promise<{ user_info: UserInfo; server_info: ServerInfo } | null> {
    try {
      // Use direct player_api URL without additional parameters to get user info
      const apiUrl = this.buildApiUrl();
      console.log('Fetching user info from:', apiUrl);
      
      const response = await this.fetchWithRetry(apiUrl);
      if (!response || !response.user_info) {
        console.error('Invalid user info response:', response);
        return null;
      }
      
      // Format dates for display
      if (response.user_info) {
        // Garantir que is_trial seja uma string e tenha o valor correto
        if (response.user_info.hasOwnProperty('is_trial')) {
          // Forçar para string e verificar se é '0' ou '1'
          response.user_info.is_trial = String(response.user_info.is_trial).replace(/\s+/g, '');
          console.log('Trial status (original):', response.user_info.is_trial);
          
          // Se não for nem '0' nem '1', definir como '0' (não é teste)
          if (response.user_info.is_trial !== '0' && response.user_info.is_trial !== '1') {
            response.user_info.is_trial = response.user_info.is_trial === 'true' ? '1' : '0';
          }
          
          console.log('Trial status (processed):', response.user_info.is_trial);
        } else {
          // Se o campo não existir, definir como '0' (não é teste)
          response.user_info.is_trial = '0';
        }
        
        // Format expiration date
        try {
          if (response.user_info.exp_date) {
            const expTimestamp = parseInt(response.user_info.exp_date) * 1000;
            response.user_info.formattedExpDate = new Date(expTimestamp).toLocaleDateString();
            response.user_info.isExpired = Date.now() > expTimestamp;
          }
        } catch (err) {
          console.error('Error formatting expiration date:', err);
        }
        
        // Format creation date
        try {
          if (response.user_info.created_at) {
            const createTimestamp = parseInt(response.user_info.created_at) * 1000;
            response.user_info.formattedCreatedAt = new Date(createTimestamp).toLocaleDateString();
          }
        } catch (err) {
          console.error('Error formatting creation date:', err);
        }
      }
      
      console.log('User info retrieved successfully:', response);
      return response;
    } catch (error) {
      console.error('Error getting user info:', error);
      return null;
    }
  }

  async getEPG(): Promise<EPGChannel[]> {
    try {
      // Check cache first (6 hours expiration)
      const cachedData = await dbService.getEPGCache('epg', 21600000); // 6 hours
      if (cachedData) {
        return cachedData;
      }

      const epgData = await this.getFullEPG();
      
      // Store in cache
      if (epgData.length > 0) {
        await dbService.setEPGCache('epg', epgData);
      }
      
      return epgData;
    } catch (error) {
      console.error('Error getting EPG:', error);
      return [];
    }
  }

  async getShortEPG(streamId: string): Promise<EPGInfo[]> {
    try {
      // Check cache first (6 hours expiration)
      const cacheKey = `short_epg_${streamId}`;
      const cachedData = await dbService.getEPGCache(cacheKey, 21600000); // 6 hours
      if (cachedData) {
        return cachedData;
      }

      const params = new URLSearchParams({
        action: 'get_short_epg',
        stream_id: streamId
      });
      const url = this.buildApiUrl() + '&' + params.toString();
      const data = await this.fetchWithRetry(url) as EPGResponse;

      if (!Array.isArray(data?.epg_listings)) {
        return [];
      }

      const epgData = data.epg_listings.map(program => ({
        ...program,
        start: new Date(program.start_timestamp * 1000).toLocaleString(),
        end: new Date(program.stop_timestamp * 1000).toLocaleString(),
        now_playing: this.isCurrentlyPlaying(program.start_timestamp, program.stop_timestamp)
      })).sort((a, b) => a.start_timestamp - b.start_timestamp);
      
      // Store in cache
      if (epgData.length > 0) {
        await dbService.setEPGCache(cacheKey, epgData);
      }

      return epgData;
    } catch (error) {
      console.error('Error getting short EPG:', error);
      return [];
    }
  }

  private isCurrentlyPlaying(start: number, end: number): boolean {
    const now = Math.floor(Date.now() / 1000);
    return now >= start && now <= end;
  }

  private parseXMLEPG(xmlText: string): XMLTV {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
    
    const channels: XMLTVChannel[] = [];
    const programmes: XMLTVProgramme[] = [];

    const channelElements = xmlDoc.getElementsByTagName('channel');
    for (let i = 0; i < channelElements.length; i++) {
      const channel = channelElements[i];
      const displayNameEl = channel.getElementsByTagName('display-name')[0];
      const iconEl = channel.getElementsByTagName('icon')[0];
      
      channels.push({
        id: channel.getAttribute('id') || null,
        displayName: displayNameEl?.textContent || '',
        icon: iconEl?.getAttribute('src') || ''
      });
    }

    const programmeElements = xmlDoc.getElementsByTagName('programme');
    for (let i = 0; i < programmeElements.length; i++) {
      const programme = programmeElements[i];
      const titleEl = programme.getElementsByTagName('title')[0];
      const descEl = programme.getElementsByTagName('desc')[0];
      
      const start = programme.getAttribute('start');
      const stop = programme.getAttribute('stop');
      const channel = programme.getAttribute('channel');
      
      if (start && stop && channel) {
        programmes.push({
          start,
          stop,
          channel,
          title: titleEl?.textContent || '',
          desc: descEl?.textContent || null
        });
      }
    }

    return { channels, programmes };
  }

  private normalizeChannelName(name: string): string {
    // Convert to lowercase
    let normalized = name.toLowerCase();
    
    // Remove quality indicators and special variations
    normalized = normalized
      .replace(/\[.*?\]/g, '') // Remove content in brackets
      .replace(/\(.*?\)/g, '') // Remove content in parentheses
      .replace(/\bfhd\b|\bhd\b|\bsd\b|\bh265\b|\bhevc\b|\b4k\b/gi, '') // Remove quality indicators as whole words
      .replace(/\+/g, 'plus') // Replace + with 'plus' to avoid confusion
      .replace(/\&/g, 'and') // Replace & with 'and'
      .replace(/\s+/g, '') // Remove all whitespace
      .replace(/[^a-z0-9]/g, ''); // Remove special characters
    
    // Keep at least 4 characters to avoid overly short strings
    return normalized.length < 4 ? name.toLowerCase().replace(/\s+/g, '') : normalized;
  }

  private findMatchingChannel(channelName: string, epgChannels: EPGChannel[]): EPGChannel | undefined {
    const normalizedName = this.normalizeChannelName(channelName);
    
    // Try exact match first
    let match = epgChannels.find(ch => 
      this.normalizeChannelName(ch.name) === normalizedName
    );

    if (!match) {
      // Try more specific partial match with stricter rules
      match = epgChannels.find(ch => {
        const epgNormalized = this.normalizeChannelName(ch.name);
        
        // Only consider a match if one name fully contains the other AND they share at least 70% similarity
        const includesOther = normalizedName.includes(epgNormalized) || epgNormalized.includes(normalizedName);
        
        if (!includesOther) return false;
        
        // Calculate similarity ratio (Jaccard similarity on character sets)
        const set1 = new Set(normalizedName);
        const set2 = new Set(epgNormalized);
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        
        const similarity = intersection.size / union.size;
        
        // Minimum similarity threshold to consider it a match
        return similarity >= 0.7; // At least 70% similar
      });
    }

    return match;
  }

  async getFullEPG(): Promise<EPGChannel[]> {
    try {
      const xmltvUrl = this.getEPGUrl();
      const response = await this.fetchWithRetry(xmltvUrl);
      
      if (typeof response !== 'string') {
        throw new Error('Invalid XMLTV response');
      }

      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(response, 'text/xml');
      
      // Check for parsing errors
      const parserError = xmlDoc.querySelector('parsererror');
      if (parserError) {
        console.error('XML parsing error:', parserError.textContent);
        return [];
      }

      const channels: EPGChannel[] = [];
      const programmesByChannel = new Map<string, EPGProgram[]>();

      // First, process all programmes and group them by channel
      const programmes = xmlDoc.querySelectorAll('programme');
      let programCount = 0;
      programmes.forEach(programme => {
        const channelId = programme.getAttribute('channel');
        if (!channelId) {
          console.warn('Programme found without channel ID:', programme.innerHTML);
          return;
        }

        const title = programme.querySelector('title')?.textContent || 'Unknown Program';
        const desc = programme.querySelector('desc')?.textContent;
        const startAttr = programme.getAttribute('start');
        const stopAttr = programme.getAttribute('stop');

        if (startAttr && stopAttr) {
          // Armazenar os atributos originais para fins de debug
          const originalStart = startAttr;
          const originalStop = stopAttr;

          // Converter para timestamp
          const startTime = this.parseXMLTVDate(startAttr);
          const endTime = this.parseXMLTVDate(stopAttr);

          // Formatar para o formato esperado pelo resto da aplicação
          const startFormatted = moment(startTime * 1000).format('YYYY-MM-DD HH:mm:ss');
          const endFormatted = moment(endTime * 1000).format('YYYY-MM-DD HH:mm:ss');

          // Log para debug - apenas uma pequena amostra
          if (programCount % 1000 === 0) {
            console.log(`EPG Date Processing:
              Original start: ${originalStart}
              Parsed start: ${startFormatted}
              Original end: ${originalStop}
              Parsed end: ${endFormatted}
            `);
          }

          const program: EPGProgram = {
            title,
            description: desc || '',
            startTime: startFormatted,
            endTime: endFormatted,
            // Adicionar campos extras para debug
            _originalStart: originalStart,
            _originalEnd: originalStop
          };

          if (!programmesByChannel.has(channelId)) {
            programmesByChannel.set(channelId, []);
          }
          programmesByChannel.get(channelId)?.push(program);
          programCount++;
        }
      });

      // Then, create channel objects with their programmes
      const channelElements = xmlDoc.querySelectorAll('channel');
      let channelsWithPrograms = 0;
      channelElements.forEach(channelElement => {
        const id = channelElement.getAttribute('id');
        const displayName = channelElement.querySelector('display-name')?.textContent;
        
        if (!displayName) {
          console.warn('Channel found without display name:', channelElement.innerHTML);
          return;
        }

        const name = displayName;
        const programs = programmesByChannel.get(id || '') || [];
        
        if (programs.length > 0) {
          channelsWithPrograms++;
        }
        
        channels.push({
          id: id || this.generateChannelId(channelElement),
          name,
          programs: programs.filter(program => {
            try {
              const start = moment(program.startTime);
              const end = moment(program.endTime);
              return start.isValid() && end.isValid() && end.isAfter(start);
            } catch {
              return false;
            }
          }).sort((a, b) => moment(a.startTime).valueOf() - moment(b.startTime).valueOf())
        });
      });

      console.log(`EPG Statistics:
        - Total channels: ${channels.length}
        - Channels with programs: ${channelsWithPrograms}
        - Total programs: ${programCount}
        - Average programs per channel: ${(programCount / channelsWithPrograms).toFixed(2)}
      `);

      return channels;
    } catch (error) {
      console.error('Error getting full EPG:', error);
      return [];
    }
  }

  private parseXMLTVDate(dateStr: string): number {
    try {
      // Exemplo: "20250322093000 -0300"
      const year = parseInt(dateStr.substring(0, 4));
      const month = parseInt(dateStr.substring(4, 6)) - 1; // JS months are 0-based
      const day = parseInt(dateStr.substring(6, 8));
      const hour = parseInt(dateStr.substring(8, 10));
      const minute = parseInt(dateStr.substring(10, 12));
      const second = parseInt(dateStr.substring(12, 14));
      
      // Process timezone
      const tzPart = dateStr.substring(14).trim();
      
      // Criar data usando componentes locais (sem conversões de fuso)
      const localDate = new Date(year, month, day, hour, minute, second);
      
      // Se há informação de fuso horário no formato "+0100" ou "-0300"
      if (tzPart && (tzPart.startsWith('+') || tzPart.startsWith('-'))) {
        // Extrair offset de fuso horário em minutos
        const tzSign = tzPart.charAt(0) === '+' ? 1 : -1;
        const tzHours = parseInt(tzPart.substring(1, 3));
        const tzMinutes = parseInt(tzPart.substring(3, 5));
        const tzOffsetMinutes = tzSign * (tzHours * 60 + tzMinutes);
        
        // Obter offset local do navegador em minutos
        const browserOffsetMinutes = new Date().getTimezoneOffset();
        
        // Ajustar pelo diferencial entre o fuso fornecido e o fuso local
        const offsetDiff = browserOffsetMinutes + tzOffsetMinutes;
        localDate.setMinutes(localDate.getMinutes() - offsetDiff);
      }
      
      // Convertido para timestamp em segundos
      const timestamp = Math.floor(localDate.getTime() / 1000);
      
      // Log para debug
      if (Math.random() < 0.01) {
        console.log(`📅 XML Date conversion:
          Original: ${dateStr}
          Parsed local time: ${localDate.toLocaleString()}
          Timestamp: ${timestamp}
          Human readable: ${new Date(timestamp * 1000).toLocaleString()}
        `);
      }
      
      return timestamp;
    } catch (error) {
      console.error(`Error parsing date ${dateStr}:`, error);
      return Math.floor(Date.now() / 1000); // Fallback
    }
  }

  private generateChannelId(channelElement: Element): string {
    const displayName = channelElement.querySelector('display-name')?.textContent;
    if (!displayName) return '';
    
    // Remove special characters and spaces, convert to lowercase
    return displayName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  private async getM3UContent(): Promise<string | null> {
    if (this.m3uCache !== null) {
      return this.m3uCache;
    }

    try {
      const m3uUrl = `${this.baseUrl}/get.php?username=${this.username}&password=${this.password}&type=series&output=ts`;
      console.log('Fetching M3U content from:', m3uUrl);
      
      const response = await this.fetchWithRetry(m3uUrl);
      
      if (typeof response === 'string' && response.trim()) {
        console.log('Caching new M3U content');
        this.m3uCache = response;
        return response;
      }
      
      console.log('No valid M3U content received');
      return null;
    } catch (error) {
      console.error('Error getting M3U content:', error);
      return null;
    }
  }

  private findM3UUrl(m3uContent: string, seasonNum: number, episodeNum: number): string | null {
    const lines = m3uContent.split('\n');
    const seasonStr = String(seasonNum).padStart(2, '0');
    const episodeStr = String(episodeNum).padStart(2, '0');
    const pattern = `S${seasonStr}E${episodeStr}`;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(pattern)) {
        // Next line should be the URL
        const nextLine = lines[i + 1]?.trim();
        if (nextLine && nextLine.startsWith('http')) {
          return nextLine;
        }
      }
    }
    
    return null;
  }

  async getSeriesSeasons(seriesId: string): Promise<Season[]> {
    try {
      // Check cache first (24 hours expiration)
      const cacheKey = `seasons_${seriesId}`;
      const cachedData = await dbService.getEPGCache(cacheKey, 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      const params = new URLSearchParams({
        action: 'get_series_info',
        series_id: seriesId
      });
      
      const url = this.buildApiUrl() + '&' + params.toString();
      
      const [response, m3uContent] = await Promise.all([
        this.fetchWithRetry(url),
        this.getM3UContent()
      ]);
      
      if (!response?.episodes || !response?.seasons) {
        return [];
      }

      const seasonMap = new Map<number, Episode[]>();
      
      // Process episodes
      for (const [seasonNum, episodes] of Object.entries(response.episodes)) {
        if (Array.isArray(episodes)) {
          const processedEpisodes = episodes.map((ep: any) => {
            let episodeUrl = ep.direct_source;
            
            // If we have M3U content and no direct_source, try to find the URL in M3U
            if (!episodeUrl && m3uContent) {
              episodeUrl = this.findM3UUrl(m3uContent, parseInt(seasonNum), ep.episode_num);
            }

            // If still no URL, use fallback
            if (!episodeUrl) {
              episodeUrl = `${this.baseUrl}/series/${this.username}/${this.password}/${ep.id}.${ep.container_extension || 'mp4'}`;
            }
            
            // Usar URL direta sem proxy
            console.log(`📺 URL do episódio ${ep.episode_num}: ${episodeUrl}`);
            
            return {
              id: ep.id,
              title: ep.title || `Episódio ${ep.episode_num}`,
              season: parseInt(seasonNum),
              episode: ep.episode_num,
              description: ep.info?.plot || ep.info?.description || '',
              duration: ep.info?.duration_secs || 0,
              url: episodeUrl,
              thumbnail: ep.info?.movie_image || '',
              containerExtension: ep.container_extension || 'mp4'
            };
          });
          
          seasonMap.set(parseInt(seasonNum), processedEpisodes);
        }
      }

      const seasons = Array.from(seasonMap.entries())
        .sort(([a], [b]) => a - b)
        .map(([seasonNumber, episodes]) => ({
          id: seasonNumber,
          name: `Temporada ${seasonNumber}`,
          episodes: episodes.sort((a, b) => a.episode - b.episode),
          episodeCount: episodes.length
        }));
      
      // Store in cache
      if (seasons.length > 0) {
        await dbService.setEPGCache(cacheKey, seasons);
      }

      return seasons;

    } catch (error) {
      console.error('Error getting series seasons:', error);
      return [];
    }
  }

  async getSeriesEpisodes(seriesId: string, seasonNumber: number): Promise<Episode[]> {
    try {
      // Check cache first (24 hours expiration)
      const cacheKey = `series_episodes_${seriesId}_${seasonNumber}`;
      const cachedData = await dbService.getEPGCache(cacheKey, 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      const response = await this.fetchWithRetry(this.buildApiUrl() + `&action=get_series_episodes&series_id=${seriesId}&season=${seasonNumber}`);
      
      const episodes = Array.isArray(response.episodes) ? response.episodes.map((episode: any) => ({
        id: episode.id,
        episode_num: episode.episode_num,
        title: episode.title,
        container_extension: episode.container_extension,
        info: {
          movie_image: episode.info?.movie_image,
          plot: episode.info?.plot,
          duration_secs: episode.info?.duration_secs,
          duration: episode.info?.duration,
          releasedate: episode.info?.releasedate
        },
        custom_sid: episode.custom_sid,
        added: episode.added,
        season: seasonNumber
      })) : [];

      // Store in cache
      if (episodes.length > 0) {
        await dbService.setEPGCache(cacheKey, episodes);
      }

      return episodes;
    } catch (error) {
      console.error('Error fetching series episodes:', error);
      return [];
    }
  }

  // Método de conveniência para obter episódios - similar ao getSeriesEpisodes, mas mantém compatibilidade
  async getEpisodes(seriesId: string, seasonId: string): Promise<Episode[]> {
    try {
      const seasonNumber = parseInt(seasonId, 10);
      if (isNaN(seasonNumber)) {
        console.error('Invalid season number:', seasonId);
        return [];
      }
      return this.getSeriesEpisodes(seriesId, seasonNumber);
    } catch (error) {
      console.error('Error in getEpisodes:', error);
      return [];
    }
  }

  // Add a method to clear the entire cache
  async clearCache(): Promise<void> {
    try {
      await dbService.clearCache();
      console.log('✅ Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // Adiciona um novo método para atualizar um tipo específico de dados em cache
  async refreshCache(type: 'epg' | 'categories' | 'streams' | 'all', categoryId?: string): Promise<void> {
    try {
      console.log(`🔄 Refreshing cache for ${type}...`);
      
      if (type === 'all') {
        await dbService.clearCache();
        console.log('✅ All cache cleared successfully');
        return;
      }
      
      if (type === 'epg') {
        await dbService.clearEPGCache();
        console.log('✅ EPG cache cleared successfully');
      } else if (type === 'categories') {
        await dbService.clearCategoriesCache();
        console.log('✅ Categories cache cleared successfully');
      } else if (type === 'streams' && categoryId) {
        await dbService.clearStreamsCache(categoryId);
        console.log(`✅ Streams cache for category ${categoryId} cleared successfully`);
      } else if (type === 'streams') {
        await dbService.clearStreamsCache();
        console.log('✅ All streams cache cleared successfully');
      }
    } catch (error) {
      console.error('Error refreshing cache:', error);
    }
  }

  // Helper function to diagnose EPG matching issues
  async diagnoseEPGMatching(channelName: string): Promise<{
    channelName: string;
    normalizedName: string;
    possibleMatches: Array<{
      epgName: string;
      normalizedEpgName: string;
      similarity: number;
      wouldMatch: boolean;
    }>;
  }> {
    const epgData = await this.getEPG();
    const normalizedName = this.normalizeChannelName(channelName);
    
    const possibleMatches = epgData.map(epg => {
      const normalizedEpgName = this.normalizeChannelName(epg.name);
      
      // Calculate similarity
      const set1 = new Set(normalizedName);
      const set2 = new Set(normalizedEpgName);
      const intersection = new Set([...set1].filter(x => set2.has(x)));
      const union = new Set([...set1, ...set2]);
      const similarity = intersection.size / union.size;
      
      // Check if it would match with current algorithm
      const includesOther = normalizedName.includes(normalizedEpgName) || normalizedEpgName.includes(normalizedName);
      const wouldMatch = normalizedEpgName === normalizedName || 
                         (includesOther && similarity >= 0.7);
      
      return {
        epgName: epg.name,
        normalizedEpgName,
        similarity,
        wouldMatch
      };
    })
    .filter(match => match.similarity > 0.4) // Only show somewhat relevant matches
    .sort((a, b) => b.similarity - a.similarity);
    
    return {
      channelName,
      normalizedName,
      possibleMatches: possibleMatches.slice(0, 10) // Return top 10 potential matches
    };
  }

  async getSeriesInfo(seriesId: string): Promise<any | null> {
    try {
      // Check cache first (24 hours expiration)
      const cacheKey = `series_info_${seriesId}`;
      const cachedData = await dbService.getEPGCache(cacheKey, 86400000); // 24 hours
      if (cachedData) {
        return cachedData;
      }

      const params = new URLSearchParams({
        action: 'get_series_info',
        series_id: seriesId
      });
      
      const url = this.buildApiUrl() + '&' + params.toString();
      
      const response = await this.fetchWithRetry(url);
      
      if (!response?.episodes || !response?.seasons) {
        return null;
      }

      // Store in cache
      if (response.episodes.length > 0 || response.seasons.length > 0) {
        await dbService.setEPGCache(cacheKey, response);
      }

      return response;
    } catch (error) {
      console.error('Error getting series info:', error);
      return null;
    }
  }

  getConnection(): { url: string; username?: string; password?: string } | null {
    return {
      url: this.baseUrl,
      username: this.username,
      password: this.password
    };
  }

  async updateConnection(url: string, username?: string, password?: string): Promise<boolean> {
    try {
      // Extrair os dados originais da URL
      let originalUrl = url.trim();
      let cleanUrl = originalUrl;
      let parsedUsername = username || '';
      let parsedPassword = password || '';
      let fullPath = '';
      
      try {
        // Analisar a URL para extrair todos os componentes
        const urlObj = new URL(cleanUrl);
        
        // Extrair username e password da URL se existirem e não foram fornecidos
        const params = new URLSearchParams(urlObj.search);
        if (params.has('username') && !parsedUsername) {
          parsedUsername = params.get('username') || '';
        }
        if (params.has('password') && !parsedPassword) {
          parsedPassword = params.get('password') || '';
        }
        
        // Extrair o caminho completo para uso como identificador exclusivo da lista
        fullPath = urlObj.pathname + urlObj.search;
        
        // Usar apenas o origin para a base da URL (protocolo + domínio + porta)
        cleanUrl = urlObj.origin;
      } catch (error) {
        console.warn('URL não pôde ser parseada adequadamente:', error);
        // Garantir que tem protocolo
        if (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://')) {
          cleanUrl = `http://${cleanUrl}`;
        }
      }
      
      // Verificar se estamos realmente mudando a conexão
      const currentConnection = await this.getStoredConfig();
      
      // Criar ID único para esta conexão (incluindo caminho e usuário)
      // Isso garante que listas diferentes no mesmo servidor sejam tratadas como conexões diferentes
      const connectionId = cleanUrl + fullPath + '_' + parsedUsername;
      const currentConnectionId = currentConnection ? 
        (currentConnection.url + '_' + currentConnection.username) : '';
      
      const isNewConnection = !currentConnection || 
        connectionId !== currentConnectionId;
      
      // Testar a conexão
      const testUrl = `${cleanUrl}/player_api.php?username=${encodeURIComponent(parsedUsername || '')}&password=${encodeURIComponent(parsedPassword || '')}`;
      console.log('Testando nova conexão:', testUrl);
      
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
          'User-Agent': 'IPTV Player'
        }
      });
      
      if (!response.ok) {
        console.error('Falha ao validar nova conexão');
        return false;
      }
      
      const data = await response.json();
      if (!data || !data.user_info) {
        console.error('Resposta inválida do novo servidor');
        return false;
      }
      
      // Atualizar propriedades internas
      this.baseUrl = cleanUrl;
      this.username = parsedUsername;
      this.password = parsedPassword;
      
      // Resetar cache interno
      this.m3uCache = null;
      
      // Se estamos mudando a conexão
      if (isNewConnection) {
        console.log('Alterando para uma nova lista IPTV com ID:', connectionId);
        console.log('Conexão anterior tinha ID:', currentConnectionId);
        console.log('Os dados de usuário anteriores serão descartados');
        
        // Limpar dados específicos do usuário (não limpar filmes/séries vistos)
        try {
          // Limpar somente a informação do usuário
          await dbService.setUserInfo({} as any);
          console.log('Informações de usuário anterior limpas com sucesso');
        } catch (error) {
          console.error('Erro ao limpar informações de usuário:', error);
        }
      }
      
      // Salvar no banco de dados com todos os parâmetros e a URL original completa
      // Isso é importante para garantir que cada lista seja identificada corretamente
      await dbService.setConnection({
        url: originalUrl, // Salvar URL completa original
        username: parsedUsername,
        password: parsedPassword,
        type: 'url',
        format: 'm3u',
        autoLogin: true,
        lastUsed: Date.now()
      });
      
      console.log('Conexão atualizada com sucesso');
      
      // Se estamos mudando a conexão, informar o usuário e redirecionar
      if (isNewConnection) {
        // Mostrar notificação estilosa em vez de alert
        this.showStylishUpdateNotification();
      
        // Redirecionar para a página inicial após um atraso maior para ver a animação
        setTimeout(() => {
          window.location.href = '/access?refresh=' + Date.now();
        }, 3000);
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar conexão:', error);
      return false;
    }
  }

  // Exibe uma notificação moderna e estilosa para atualizações importantes
  private showStylishUpdateNotification() {
    // Criar overlay completo para a notificação importante
    const overlayId = 'neko-update-overlay';
    
    // Remover se já existir
    const existingOverlay = document.getElementById(overlayId);
    if (existingOverlay) {
      existingOverlay.remove();
    }
    
    // Criar overlay com efeito de blur
    const overlayEl = document.createElement('div');
    overlayEl.id = overlayId;
    overlayEl.className = 'neko-update-overlay';
    
    // HTML com animação e design moderno
    overlayEl.innerHTML = `
      <div class="modern-update-modal">
        <div class="modal-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
        </div>
        <div class="modal-content">
          <h2>Lista IPTV Atualizada</h2>
          <p>Sua lista de canais foi atualizada com sucesso.<br>Aplicando mudanças automaticamente.</p>
          <div class="loading-bar">
            <div class="loading-progress"></div>
          </div>
        </div>
      </div>
    `;
    
    // Adicionar estilos modernos e animados
    const style = document.createElement('style');
    style.textContent = `
      .neko-update-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 9999;
        animation: fadeIn 0.3s ease-out;
      }
      
      .modern-update-modal {
        background: linear-gradient(135deg, #1e293b, #0f172a);
        border-radius: 16px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        padding: 32px;
        max-width: 400px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        overflow: hidden;
        position: relative;
        animation: slideUp 0.5s cubic-bezier(0.16, 1, 0.3, 1);
      }
      
      .modal-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        border-radius: 50%;
        margin-bottom: 24px;
        color: white;
        box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.3);
        animation: pulse 2s infinite;
      }
      
      .modal-content h2 {
        color: #f8fafc;
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 12px;
      }
      
      .modal-content p {
        color: #cbd5e1;
        font-size: 16px;
        line-height: 1.6;
        margin: 0 0 24px;
      }
      
      .loading-bar {
        width: 100%;
        height: 6px;
        background-color: #334155;
        border-radius: 10px;
        overflow: hidden;
        margin-top: 8px;
      }
      
      .loading-progress {
        height: 100%;
        background: linear-gradient(90deg, #0ea5e9, #38bdf8);
        border-radius: 10px;
        width: 0%;
        animation: loadProgress 2.5s ease-in-out forwards;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      
      @keyframes pulse {
        0% { transform: scale(1); box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.3); }
        50% { transform: scale(1.05); box-shadow: 0 15px 20px -3px rgba(14, 165, 233, 0.4); }
        100% { transform: scale(1); box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.3); }
      }
      
      @keyframes loadProgress {
        0% { width: 0%; }
        20% { width: 30%; }
        50% { width: 60%; }
        80% { width: 80%; }
        100% { width: 100%; }
      }
    `;
    
    // Adicionar à página
    document.head.appendChild(style);
    document.body.appendChild(overlayEl);
  }
}

export async function createIPTVService(): Promise<IPTVService> {
  const connection = await dbService.getConnection();
  if (!connection?.url) {
    throw new Error('No connection URL found');
  }

  console.log('Creating IPTV service with URL:', connection.url);

  // Parse URL to extract components
  let serverUrl = connection.url.replace(/\/+$/, '');
  let username = connection.username || '';
  let password = connection.password || '';
  
  // Usaremos a URL completa para identificar unicamente a lista
  let serverUrlOriginal = serverUrl;

  // Check if credentials are in the URL as query parameters
  try {
    const urlObj = new URL(serverUrl);
    const params = new URLSearchParams(urlObj.search);
    
    // Extract username and password from URL if available
    if (params.has('username') && !username) {
      username = params.get('username') || '';
      console.log('Extracted username from URL:', username);
    }
    
    if (params.has('password') && !password) {
      password = params.get('password') || '';
      console.log('Extracted password from URL:', password);
    }
    
    // Preserve the full path including any subdirectories, but remove specific endpoints
    // to ensure API calls work correctly. This maintains the unique identity of the list.
    const knownEndpoints = ['/xmltv.php', '/get.php', '/player_api.php', '/panel_api.php'];
    let baseFound = false;
    
    // Apenas remova o endpoint específico, mantendo o resto do caminho
    for (const endpoint of knownEndpoints) {
      if (urlObj.pathname.endsWith(endpoint)) {
        // Obter o caminho excluindo apenas o endpoint específico
        const pathWithoutEndpoint = urlObj.pathname.substring(0, urlObj.pathname.length - endpoint.length);
        serverUrl = urlObj.origin + pathWithoutEndpoint;
        console.log('Mantendo caminho completo da URL:', serverUrl);
        baseFound = true;
        break;
      }
    }
    
    // Se nenhum endpoint específico foi encontrado, mantenha o caminho completo
    if (!baseFound) {
      serverUrl = urlObj.origin + urlObj.pathname;
    }
    
    // Ensure we have http:// prefix
    if (!serverUrl.startsWith('http://') && !serverUrl.startsWith('https://')) {
      serverUrl = `http://${serverUrl}`;
      console.log('Added http:// prefix to URL:', serverUrl);
    }
    
    // Remove trailing slash if any
    serverUrl = serverUrl.replace(/\/+$/, '');
    
  } catch (error) {
    console.error('Error parsing IPTV URL:', error);
    
    // Handle URLs that might not be properly formatted
    if (!serverUrl.startsWith('http')) {
      serverUrl = `http://${serverUrl}`;
    }
  }
  
  console.log('IPTV service configured with:', { 
    serverUrl, 
    originalUrl: serverUrlOriginal, 
    hasUsername: !!username, 
    hasPassword: !!password 
  });

  return new IPTVServiceImpl(serverUrl, username, password);
}

// Export only the types that are needed externally
export type { EPGInfo };