import { openDB, DBSchema, IDBPDatabase } from 'idb';
import {
  Channel,
  Playlist,
  WatchHistoryItem,
  FavoriteChannel,
  UserPreferences,
  DEFAULT_PREFERENCES,
  StorageSchema,
  DBStoreName
} from '../../types/Channel';

const DB_NAME = 'iptv_player_db';
const DB_VERSION = 1;

interface IPTVPlayerDB extends DBSchema {
  channels: {
    key: string;
    value: Channel;
    indexes: {
      'by-playlist': string;
      'by-group': string;
      'by-category': string;
    };
  };
  playlists: {
    key: string;
    value: Playlist;
  };
  watchHistory: {
    key: string;
    value: WatchHistoryItem;
    indexes: {
      'by-channel': string;
      'by-playlist': string;
      'by-timestamp': string;
    };
  };
  favorites: {
    key: string;
    value: FavoriteChannel;
    indexes: {
      'by-playlist': string;
    };
  };
  preferences: {
    key: string;
    value: UserPreferences;
  };
}

class IndexedDBStorage {
  private db: IDBPDatabase<IPTVPlayerDB> | null = null;

  async initialize(): Promise<void> {
    if (this.db) return;

    this.db = await openDB<IPTVPlayerDB>(DB_NAME, DB_VERSION, {
      upgrade(db) {
        // Channels store
        if (!db.objectStoreNames.contains('channels')) {
          const channelStore = db.createObjectStore('channels', { keyPath: 'id' });
          channelStore.createIndex('by-playlist', 'playlistId');
          channelStore.createIndex('by-group', 'group');
          channelStore.createIndex('by-category', 'category');
        }

        // Playlists store
        if (!db.objectStoreNames.contains('playlists')) {
          db.createObjectStore('playlists', { keyPath: 'id' });
        }

        // Watch history store
        if (!db.objectStoreNames.contains('watchHistory')) {
          const historyStore = db.createObjectStore('watchHistory', { keyPath: 'id' });
          historyStore.createIndex('by-channel', 'channelId');
          historyStore.createIndex('by-playlist', 'playlistId');
          historyStore.createIndex('by-timestamp', 'timestamp');
        }

        // Favorites store
        if (!db.objectStoreNames.contains('favorites')) {
          const favoritesStore = db.createObjectStore('favorites', { keyPath: 'id' });
          favoritesStore.createIndex('by-playlist', 'playlistId');
        }

        // Preferences store
        if (!db.objectStoreNames.contains('preferences')) {
          const preferencesStore = db.createObjectStore('preferences', { keyPath: 'id' });
          preferencesStore.put({
            ...DEFAULT_PREFERENCES,
            id: 'user'
          });
        }
      }
    });
  }

  private async getDB(): Promise<IDBPDatabase<IPTVPlayerDB>> {
    if (!this.db) {
      await this.initialize();
    }
    return this.db!;
  }

  // Channel operations
  async saveChannel(channel: Channel): Promise<void> {
    const db = await this.getDB();
    await db.put('channels', channel);
  }

  async getChannel(id: string): Promise<Channel | undefined> {
    const db = await this.getDB();
    return db.get('channels', id);
  }

  async getChannels(): Promise<Channel[]> {
    const db = await this.getDB();
    return db.getAll('channels');
  }

  async getChannelsByPlaylist(playlistId: string): Promise<Channel[]> {
    const db = await this.getDB();
    return db.getAllFromIndex('channels', 'by-playlist', playlistId);
  }

  async deleteChannel(id: string): Promise<void> {
    const db = await this.getDB();
    await db.delete('channels', id);
  }

  // Playlist operations
  async savePlaylist(playlist: Playlist): Promise<void> {
    const db = await this.getDB();
    await db.put('playlists', playlist);
  }

  async getPlaylist(id: string): Promise<Playlist | undefined> {
    const db = await this.getDB();
    return db.get('playlists', id);
  }

  async getPlaylists(): Promise<Playlist[]> {
    const db = await this.getDB();
    return db.getAll('playlists');
  }

  async deletePlaylist(id: string): Promise<void> {
    const db = await this.getDB();
    const tx = db.transaction(['playlists', 'channels'], 'readwrite');

    // Delete playlist
    await tx.objectStore('playlists').delete(id);

    // Delete associated channels
    const channelStore = tx.objectStore('channels');
    const channelIndex = channelStore.index('by-playlist');
    const channels = await channelIndex.getAllKeys(id);
    
    await Promise.all(
      channels.map(channelId => channelStore.delete(channelId))
    );

    await tx.done;
  }

  // Watch history operations
  async addToHistory(item: Omit<WatchHistoryItem, 'id'>): Promise<void> {
    const db = await this.getDB();
    const historyItem: WatchHistoryItem = {
      ...item,
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString()
    };
    await db.put('watchHistory', historyItem);
  }

  async updateWatchProgress(
    channelId: string,
    position: number,
    duration: number
  ): Promise<void> {
    const db = await this.getDB();
    const tx = db.transaction(['channels', 'watchHistory'], 'readwrite');

    // Update channel
    const channel = await tx.objectStore('channels').get(channelId);
    if (channel) {
      channel.watchProgress = position / duration;
      channel.lastWatched = new Date().toISOString();
      await tx.objectStore('channels').put(channel);
    }

    // Update latest history entry
    const historyIndex = tx.objectStore('watchHistory').index('by-channel');
    const latestEntry = await historyIndex.getAll(channelId, 1);
    
    if (latestEntry.length > 0) {
      const entry = latestEntry[0];
      entry.position = position;
      entry.duration = duration;
      await tx.objectStore('watchHistory').put(entry);
    }

    await tx.done;
  }

  async getHistory(limit?: number): Promise<WatchHistoryItem[]> {
    const db = await this.getDB();
    const tx = db.transaction('watchHistory', 'readonly');
    const index = tx.store.index('by-timestamp');
    const items = await index.getAll();
    
    items.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
    return limit ? items.slice(0, limit) : items;
  }

  async clearHistory(): Promise<void> {
    const db = await this.getDB();
    const tx = db.transaction(['watchHistory', 'channels'], 'readwrite');

    // Clear watch history
    await tx.objectStore('watchHistory').clear();

    // Reset watch progress on channels
    const channels = await tx.objectStore('channels').getAll();
    await Promise.all(
      channels.map(channel => {
        channel.watchProgress = 0;
        channel.lastWatched = undefined;
        return tx.objectStore('channels').put(channel);
      })
    );

    await tx.done;
  }

  // Favorites operations
  async addToFavorites(channel: FavoriteChannel): Promise<void> {
    const db = await this.getDB();
    const tx = db.transaction(['favorites', 'channels'], 'readwrite');

    // Add to favorites
    await tx.objectStore('favorites').put(channel);

    // Update channel
    const channelStore = tx.objectStore('channels');
    const existingChannel = await channelStore.get(channel.id);
    if (existingChannel) {
      existingChannel.isFavorite = true;
      await channelStore.put(existingChannel);
    }

    await tx.done;
  }

  async removeFromFavorites(channelId: string): Promise<void> {
    const db = await this.getDB();
    const tx = db.transaction(['favorites', 'channels'], 'readwrite');

    // Remove from favorites
    await tx.objectStore('favorites').delete(channelId);

    // Update channel
    const channelStore = tx.objectStore('channels');
    const channel = await channelStore.get(channelId);
    if (channel) {
      channel.isFavorite = false;
      await channelStore.put(channel);
    }

    await tx.done;
  }

  async getFavorites(playlistId?: string): Promise<FavoriteChannel[]> {
    const db = await this.getDB();
    if (playlistId) {
      return db.getAllFromIndex('favorites', 'by-playlist', playlistId);
    }
    return db.getAll('favorites');
  }

  async isFavorite(channelId: string): Promise<boolean> {
    const db = await this.getDB();
    const favorite = await db.get('favorites', channelId);
    return !!favorite;
  }

  // Preferences operations
  async getPreferences(): Promise<UserPreferences> {
    const db = await this.getDB();
    const prefs = await db.get('preferences', 'user');
    return prefs || { ...DEFAULT_PREFERENCES, id: 'user' };
  }

  async savePreferences(preferences: Partial<Omit<UserPreferences, 'id'>>): Promise<void> {
    const db = await this.getDB();
    const currentPrefs = await this.getPreferences();
    await db.put('preferences', {
      ...currentPrefs,
      ...preferences
    });
  }

  // Data management
  async clearAllData(): Promise<void> {
    const db = await this.getDB();
    const tx = db.transaction(
      ['channels', 'playlists', 'watchHistory', 'favorites'],
      'readwrite'
    );

    await Promise.all([
      tx.objectStore('channels').clear(),
      tx.objectStore('playlists').clear(),
      tx.objectStore('watchHistory').clear(),
      tx.objectStore('favorites').clear()
    ]);

    await tx.done;

    // Reset preferences to defaults
    await this.savePreferences(DEFAULT_PREFERENCES);
  }
}

export const indexedDBStorage = new IndexedDBStorage();
