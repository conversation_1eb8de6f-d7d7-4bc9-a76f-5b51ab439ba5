import React, { useState } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  Box,
  useTheme,
  alpha,
  Fade
} from '@mui/material';
import { motion } from 'framer-motion';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

interface EnhancedInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'password' | 'url';
  placeholder?: string;
  icon?: React.ReactNode;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  disabled?: boolean;
  multiline?: boolean;
  rows?: number;
}

const MotionBox = motion.create(Box);

const EnhancedInput: React.FC<EnhancedInputProps> = ({
  label,
  value,
  onChange,
  type = 'text',
  placeholder,
  icon,
  error = false,
  helperText,
  fullWidth = true,
  disabled = false,
  multiline = false,
  rows = 1
}) => {
  const theme = useTheme();
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const isDarkMode = theme.palette.mode === 'dark';

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  const inputType = type === 'password' ? (showPassword ? 'text' : 'password') : type;

  return (
    <MotionBox
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      sx={{ mb: 2, position: 'relative' }}
    >
      {/* Efeito de brilho quando focado */}
      <Fade in={isFocused}>
        <Box
          sx={{
            position: 'absolute',
            inset: -2,
            borderRadius: 2,
            background: `linear-gradient(135deg, 
              ${alpha(theme.palette.primary.main, 0.3)}, 
              ${alpha(theme.palette.secondary.main, 0.3)})`,
            filter: 'blur(8px)',
            zIndex: -1,
            opacity: isFocused ? 1 : 0,
            transition: 'opacity 0.3s ease'
          }}
        />
      </Fade>

      <TextField
        label={label}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        type={inputType}
        placeholder={placeholder}
        error={error}
        helperText={helperText}
        fullWidth={fullWidth}
        disabled={disabled}
        multiline={multiline}
        rows={rows}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        InputProps={{
          startAdornment: icon && (
            <InputAdornment position="start">
              <Box
                sx={{
                  color: isFocused 
                    ? theme.palette.primary.main 
                    : theme.palette.text.secondary,
                  transition: 'color 0.3s ease'
                }}
              >
                {icon}
              </Box>
            </InputAdornment>
          ),
          endAdornment: type === 'password' && (
            <InputAdornment position="end">
              <IconButton
                onClick={handleTogglePassword}
                edge="end"
                sx={{
                  color: isFocused 
                    ? theme.palette.primary.main 
                    : theme.palette.text.secondary,
                  transition: 'color 0.3s ease',
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1)
                  }
                }}
              >
                {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
              </IconButton>
            </InputAdornment>
          ),
          sx: {
            borderRadius: 2,
            background: isDarkMode
              ? alpha('#1a1f35', 0.6)
              : alpha('#ffffff', 0.8),
            backdropFilter: 'blur(10px)',
            transition: 'all 0.3s ease',
            '&:hover': {
              background: isDarkMode
                ? alpha('#1a1f35', 0.8)
                : alpha('#ffffff', 0.9),
            },
            '&.Mui-focused': {
              background: isDarkMode
                ? alpha('#1a1f35', 0.9)
                : alpha('#ffffff', 1),
              boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.3)}`,
            },
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: alpha(theme.palette.primary.main, 0.3),
              borderWidth: 1,
              transition: 'all 0.3s ease'
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: alpha(theme.palette.primary.main, 0.5),
              borderWidth: 1.5,
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: theme.palette.primary.main,
              borderWidth: 2,
              boxShadow: `0 0 10px ${alpha(theme.palette.primary.main, 0.2)}`
            },
            '&.Mui-error .MuiOutlinedInput-notchedOutline': {
              borderColor: theme.palette.error.main,
              boxShadow: `0 0 10px ${alpha(theme.palette.error.main, 0.2)}`
            }
          }
        }}
        InputLabelProps={{
          sx: {
            color: theme.palette.text.secondary,
            '&.Mui-focused': {
              color: theme.palette.primary.main,
              fontWeight: 600
            },
            '&.Mui-error': {
              color: theme.palette.error.main
            }
          }
        }}
        FormHelperTextProps={{
          sx: {
            mx: 0,
            mt: 1,
            fontSize: '0.875rem',
            fontWeight: 500,
            color: error 
              ? theme.palette.error.main 
              : theme.palette.text.secondary
          }
        }}
      />
    </MotionBox>
  );
};

export default EnhancedInput;