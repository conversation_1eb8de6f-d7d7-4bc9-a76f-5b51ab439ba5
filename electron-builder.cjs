// Configuração do electron-builder
const path = require('path');

module.exports = {
  appId: "com.iptv.player",
  productName: "IPTV Player",
  directories: {
    output: "dist_electron"
  },
  files: [
    "dist/**/*",
    "electron/**/*"
  ],
  // Hooks para scripts personalizados
  afterPack: path.join(__dirname, "scripts/after-pack.cjs"),
  beforePack: path.join(__dirname, "scripts/before-pack.cjs"),
  // Configurações específicas para Windows
  win: {
    target: [
      "nsis"
    ]
  },
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: "IPTV Player"
  },
  // Configurações específicas para macOS
  mac: {
    target: "dmg",
    category: "public.app-category.video"
  },
  // Configurações específicas para Linux
  linux: {
    target: [
      "AppImage",
      "deb"
    ],
    category: "Video"
  }
};