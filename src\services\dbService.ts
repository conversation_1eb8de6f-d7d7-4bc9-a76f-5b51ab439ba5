import { openDB, IDBPDatabase, deleteDB } from 'idb';

interface DBSchema {
  connection: {
    key: string;
    value: {
      url: string;
      username?: string;
      password?: string;
      type: 'url' | 'file' | 'local';
      format: 'm3u' | 'hls' | 'mpegts';
      autoLogin: boolean;
      lastUsed: number;
    };
  };
  user_info: {
    key: string;
    value: {
      username: string;
      password: string;
      message: string;
      auth: number;
      status: string;
      exp_date: string;
      is_trial: string;
      active_cons: string;
      created_at: string;
      max_connections: string;
      allowed_output_formats: string[];
    };
  };
  playlist_chunks: {
    key: number;
    value: {
      chunkIndex: number;
      items: any[];
    };
  };
  playlist_info: {
    key: string;
    value: {
      totalItems: number;
      totalChunks: number;
      chunkSize: number;
      lastUpdated: number;
    };
  };
  saved_connections: {
    key: string;
    value: {
      id: string;
      url: string;
      username?: string;
      password?: string;
      type: 'url' | 'file' | 'local';
      format: 'm3u' | 'hls' | 'mpegts';
      autoLogin: boolean;
      lastUsed: number;
    };
  };
  categories_cache: {
    key: string;
    value: {
      data: any[];
      timestamp: number;
    };
  };
  streams_cache: {
    key: string;
    value: {
      data: any[];
      timestamp: number;
    };
  };
  epg_cache: {
    key: string;
    value: {
      data: any;
      timestamp: number;
    };
  };
  watched_movies: {
    key: string;
    value: {
      id: string;
      name: string;
      thumbnail: string;
      cover?: string;
      progress: number;
      duration: number;
      lastWatched: number;
      categoryId?: string;
    };
  };
  watched_series: {
    key: string;
    value: {
      id: string;
      name: string;
      thumbnail: string;
      cover?: string;
      progress: number;
      duration: number;
      lastWatched: number;
      categoryId?: string;
      seasonId?: string;
      episodeId?: string;
      currentEpisode?: {
        number: number;
        title: string;
        season: number;
      };
    };
  };
  watched_episodes: {
    key: string;
    value: {
      id: string;
      name: string;
      thumbnail: string;
      progress: number;
      duration: number;
      lastWatched: number;
      seriesId: string;
      seasonId: string;
      episodeNumber: number;
      seasonNumber: number;
    };
  };
  watched_channels: {
    key: string;
    value: {
      id: string;
      name: string;
      thumbnail: string;
      lastWatched: number;
      categoryId?: string;
    };
  };
  favorites: {
    key: string;
    value: {
      id: string;
      name: string;
      thumbnail: string;
      url: string;
      playlistId?: string;
      addedAt: number;
    };
  };
}

class DBService {
  private db: IDBPDatabase<DBSchema> | null = null;
  private static instance: DBService;
  private initPromise: Promise<void> | null = null;
  private currentServerHash: string | null = null;
  private readonly DB_NAME = 'iptv-player';

  private constructor() {
    // Inicializar o hash do servidor ao criar o serviço
    this.updateServerHash();
  }

  static getInstance(): DBService {
    if (!DBService.instance) {
      DBService.instance = new DBService();
    }
    return DBService.instance;
  }

  async initialize(): Promise<void> {
    if (this.db) return;
    
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = new Promise(async (resolve, reject) => {
      try {
        this.db = await openDB<DBSchema>(this.DB_NAME, 5, {
          upgrade(db, oldVersion, newVersion) {
            // Connection store
            if (!db.objectStoreNames.contains('connection')) {
              db.createObjectStore('connection');
            }

            // User info store
            if (!db.objectStoreNames.contains('user_info')) {
              db.createObjectStore('user_info');
            }

            // Playlist chunks store
            if (!db.objectStoreNames.contains('playlist_chunks')) {
              db.createObjectStore('playlist_chunks');
            }

            // Playlist info store
            if (!db.objectStoreNames.contains('playlist_info')) {
              db.createObjectStore('playlist_info');
            }
            
            // Cache stores
            if (!db.objectStoreNames.contains('categories_cache')) {
              db.createObjectStore('categories_cache');
            }
            
            if (!db.objectStoreNames.contains('streams_cache')) {
              db.createObjectStore('streams_cache');
            }
            
            if (!db.objectStoreNames.contains('epg_cache')) {
              db.createObjectStore('epg_cache');
            }
            
            // Watched movies store
            if (!db.objectStoreNames.contains('watched_movies')) {
              db.createObjectStore('watched_movies');
            }
            
            // Watched series store
            if (!db.objectStoreNames.contains('watched_series')) {
              db.createObjectStore('watched_series');
            }
            
            // Watched episodes store
            if (!db.objectStoreNames.contains('watched_episodes')) {
              db.createObjectStore('watched_episodes');
            }
            
            // Watched channels store
            if (!db.objectStoreNames.contains('watched_channels')) {
              db.createObjectStore('watched_channels');
            }
            
            // Favorites store
            if (!db.objectStoreNames.contains('favorites')) {
              db.createObjectStore('favorites');
            }
            
            // Saved connections store
            if (!db.objectStoreNames.contains('saved_connections')) {
              db.createObjectStore('saved_connections');
            }
          },
        });
        resolve();
      } catch (error) {
        reject(error);
      }
    });

    return this.initPromise;
  }

  // Método para gerar um hash da URL do servidor atual
  private async updateServerHash(): Promise<void> {
    try {
      const connection = await this.getConnection();
      if (connection?.url) {
        // Extrair a URL completa incluindo caminho para o namespace
        try {
          const url = new URL(connection.url);
          
          // Gerar um identificador completo que inclui domínio + caminho + username
          let fullIdentifier = url.hostname;
          
          // Adicionar o caminho ao identificador (sem parâmetros de consulta)
          if (url.pathname && url.pathname !== '/') {
            fullIdentifier += url.pathname.replace(/\//g, '_');
          }
          
          // Adicionar parâmetros relevantes como username ao identificador
          // mas excluir a senha por segurança
          const params = new URLSearchParams(url.search);
          if (params.has('username') || connection.username) {
            const username = params.get('username') || connection.username;
            if (username) {
              fullIdentifier += '_' + username;
            }
          }
          
          // Normalizar o identificador removendo caracteres não permitidos
          this.currentServerHash = fullIdentifier.replace(/[^a-zA-Z0-9_]/g, '_');
          console.log('Cache namespace completo atualizado:', this.currentServerHash);
        } catch (e) {
          // Se falhar ao analisar a URL, usar a URL completa como fallback
          this.currentServerHash = connection.url
            .replace(/^https?:\/\//, '')
            .replace(/[^a-zA-Z0-9]/g, '_');
          
          // Adicionar username ao hash se disponível
          if (connection.username) {
            this.currentServerHash += '_' + connection.username;
          }
          
          console.log('Cache namespace atualizado (fallback):', this.currentServerHash);
        }
      } else {
        this.currentServerHash = 'default';
      }
    } catch (e) {
      console.error('Erro ao atualizar hash do servidor:', e);
      this.currentServerHash = 'default';
    }
  }

  // Criar chave de cache com namespace
  private createNamespacedKey(key: string): string {
    if (!this.currentServerHash) {
      this.updateServerHash();
      return `default_${key}`;
    }
    return `${this.currentServerHash}_${key}`;
  }

  async setConnection(data: DBSchema['connection']['value']): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.put('connection', {
      ...data,
      lastUsed: Date.now(),
      autoLogin: data.autoLogin ?? false
    }, 'current');
    
    // Atualizar o namespace do cache quando a conexão muda
    await this.updateServerHash();
  }

  async getConnection(): Promise<DBSchema['connection']['value'] | undefined> {
    await this.initialize();
    const connection = await this.db?.get('connection', 'current');
    
    // Se temos uma conexão, registrar o acesso à playlist
    if (connection) {
      this.trackPlaylistFetch();
    }
    
    return connection;
  }
  
  // Registra no Supabase que o usuário acessou a playlist IPTV
  private async trackPlaylistFetch(): Promise<void> {
    try {
      // Obter informações da licença para rastrear o acesso
      const userInfo = await this.getUserInfo();
      const licenseKey = userInfo?.username || '';
      const deviceId = localStorage.getItem('iptv_device_id') || '';
      
      if (!licenseKey || !deviceId) {
        return;
      }
      
      // Importar o getSupabaseClient da deviceService
      const { getSupabaseClient } = await import('./deviceService');
      const supabase = getSupabaseClient();
      
      const { data, error } = await supabase
        .rpc('track_playlist_fetch', {
          key: licenseKey,
          device: deviceId
        });
      
      if (error) {
        console.error('Erro ao registrar acesso à playlist:', error);
      } else {
        console.log('Acesso à playlist registrado com sucesso:', data);
      }
    } catch (err) {
      console.error('Erro ao tentar registrar acesso à playlist:', err);
    }
  }

  async getLastConnection(): Promise<DBSchema['connection']['value'] | undefined> {
    await this.initialize();
    const tx = this.db?.transaction('connection', 'readonly');
    const store = tx?.objectStore('connection');
    const connections = await store?.getAll();
    
    if (!connections || connections.length === 0) return undefined;
    
    // Return the most recently used connection
    return connections.sort((a, b) => (b.lastUsed || 0) - (a.lastUsed || 0))[0];
  }
  
  async getAllConnections(): Promise<DBSchema['connection']['value'][]> {
    await this.initialize();
    const tx = this.db?.transaction('connection', 'readonly');
    const store = tx?.objectStore('connection');
    const connections = await store?.getAll();
    
    if (!connections || connections.length === 0) return [];
    
    // Filter out the 'current' connection key and sort by last used
    return connections
      .filter(conn => conn && typeof conn === 'object')
      .sort((a, b) => (b.lastUsed || 0) - (a.lastUsed || 0));
  }

  async setAutoLogin(enabled: boolean): Promise<void> {
    await this.initialize();
    const current = await this.getConnection();
    if (current) {
      await this.setConnection({
        ...current,
        autoLogin: enabled
      });
    }
  }

  async hasAutoLogin(): Promise<boolean> {
    const connection = await this.getConnection();
    return connection?.autoLogin ?? false;
  }

  async setUserInfo(data: DBSchema['user_info']['value']): Promise<void> {
    await this.initialize();
    await this.db?.put('user_info', data, 'current');
  }

  async getUserInfo(): Promise<DBSchema['user_info']['value'] | undefined> {
    await this.initialize();
    return this.db?.get('user_info', 'current');
  }

  async setPlaylistChunk(chunkIndex: number, data: any[]): Promise<void> {
    await this.initialize();
    await this.db?.put('playlist_chunks', { items: data }, chunkIndex);
  }

  async getPlaylistChunk(chunkIndex: number): Promise<any[] | undefined> {
    await this.initialize();
    const chunk = await this.db?.get('playlist_chunks', chunkIndex);
    return chunk?.items;
  }

  async setPlaylistInfo(data: DBSchema['playlist_info']['value']): Promise<void> {
    await this.initialize();
    await this.db?.put('playlist_info', data, 'current');
  }

  async getPlaylistInfo(): Promise<DBSchema['playlist_info']['value'] | undefined> {
    await this.initialize();
    return this.db?.get('playlist_info', 'current');
  }

  async clearPlaylistChunks(): Promise<void> {
    await this.initialize();
    const tx = this.db?.transaction('playlist_chunks', 'readwrite');
    await tx?.objectStore('playlist_chunks').clear();
  }

  async deleteDatabase(): Promise<void> {
    try {
      // Fechar a conexão atual com o banco de dados
      if (this.db) {
        const name = this.db.name;
        this.db.close();
        this.db = null;
        this.initPromise = null;
        await deleteDB(name);
      } else {
        // Se não houver conexão ativa, excluir pelo nome conhecido
        await deleteDB(this.DB_NAME);
      }
      
      // Reinicializar o banco de dados
      await this.initialize();
      console.log('Banco de dados excluído e reinicializado com sucesso');
    } catch (e) {
      console.error('Erro ao excluir banco de dados:', e);
      throw e;
    }
  }

  async setCategoriesCache(type: string, data: any[]): Promise<void> {
    await this.initialize();
    // Usar namespace para a chave
    const namespacedKey = this.createNamespacedKey(type);
    
    await this.db?.put('categories_cache', {
      data,
      timestamp: Date.now()
    }, namespacedKey);
    
    console.log(`Cache de categorias salvo com namespace: ${namespacedKey}`);
  }

  async getCategoriesCache(type: string, maxAge: number = 86400000): Promise<any[] | null> {
    await this.initialize();
    // Usar namespace para a chave
    const namespacedKey = this.createNamespacedKey(type);
    
    const cache = await this.db?.get('categories_cache', namespacedKey);
    
    if (cache && (Date.now() - cache.timestamp < maxAge)) {
      return cache.data;
    }
    
    return null;
  }

  async setStreamsCache(categoryId: string, type: string, data: any[]): Promise<void> {
    await this.initialize();
    // Usar namespace para a chave
    const key = `${categoryId}_${type}`;
    const namespacedKey = this.createNamespacedKey(key);
    
    await this.db?.put('streams_cache', {
      data,
      timestamp: Date.now()
    }, namespacedKey);
    
    console.log(`Cache de streams salvo com namespace: ${namespacedKey}`);
  }

  async getStreamsCache(categoryId: string, type: string, maxAge: number = 86400000): Promise<any[] | null> {
    await this.initialize();
    // Usar namespace para a chave
    const key = `${categoryId}_${type}`;
    const namespacedKey = this.createNamespacedKey(key);
    
    const cache = await this.db?.get('streams_cache', namespacedKey);
    
    if (cache && (Date.now() - cache.timestamp < maxAge)) {
      return cache.data;
    }
    
    return null;
  }

  async setEPGCache(key: string, data: any): Promise<void> {
    await this.initialize();
    // Usar namespace para a chave
    const namespacedKey = this.createNamespacedKey(key);
    
    await this.db?.put('epg_cache', {
      data,
      timestamp: Date.now()
    }, namespacedKey);
    
    console.log(`Cache de EPG salvo com namespace: ${namespacedKey}`);
  }

  async getEPGCache(key: string, maxAge: number = 21600000): Promise<any | null> {
    await this.initialize();
    // Usar namespace para a chave
    const namespacedKey = this.createNamespacedKey(key);
    
    const cache = await this.db?.get('epg_cache', namespacedKey);
    
    if (cache && (Date.now() - cache.timestamp < maxAge)) {
      return cache.data;
    }
    
    return null;
  }

  // Limpar todo o cache deve manter namespaces separados
  async clearCache(): Promise<void> {
    await this.initialize();
    
    if (!this.currentServerHash) {
      await this.updateServerHash();
    }
    
    // Limpar apenas o cache do servidor atual
    console.log('Limpando cache para o namespace:', this.currentServerHash);
    
    // Limpar categorias
    const categoriesTx = this.db?.transaction('categories_cache', 'readwrite');
    const categoriesStore = categoriesTx?.objectStore('categories_cache');
    const categoriesKeys = await categoriesStore?.getAllKeys() || [];
    for (const key of categoriesKeys) {
      if (key.toString().startsWith(this.currentServerHash + '_')) {
        await categoriesStore?.delete(key);
      }
    }
    
    // Limpar streams
    const streamsTx = this.db?.transaction('streams_cache', 'readwrite');
    const streamsStore = streamsTx?.objectStore('streams_cache');
    const streamsKeys = await streamsStore?.getAllKeys() || [];
    for (const key of streamsKeys) {
      if (key.toString().startsWith(this.currentServerHash + '_')) {
        await streamsStore?.delete(key);
      }
    }
    
    // Limpar EPG
    const epgTx = this.db?.transaction('epg_cache', 'readwrite');
    const epgStore = epgTx?.objectStore('epg_cache');
    const epgKeys = await epgStore?.getAllKeys() || [];
    for (const key of epgKeys) {
      if (key.toString().startsWith(this.currentServerHash + '_')) {
        await epgStore?.delete(key);
      }
    }
    
    console.log('Cache limpo com sucesso para namespace:', this.currentServerHash);
  }

  // Métodos modificados para limpar apenas o namespace atual
  async clearCategoriesCache(type?: string): Promise<void> {
    await this.initialize();
    const tx = this.db?.transaction('categories_cache', 'readwrite');
    
    if (type) {
      // Limpar uma categoria específica
      const namespacedKey = this.createNamespacedKey(type);
      await tx?.objectStore('categories_cache').delete(namespacedKey);
    } else {
      // Limpar todas as categorias do namespace atual
      const store = tx?.objectStore('categories_cache');
      const keys = await store?.getAllKeys() || [];
      for (const key of keys) {
        if (key.toString().startsWith(this.currentServerHash + '_')) {
          await store?.delete(key);
        }
      }
    }
  }

  async clearStreamsCache(categoryId?: string, type?: string): Promise<void> {
    await this.initialize();
    const tx = this.db?.transaction('streams_cache', 'readwrite');
    
    if (categoryId && type) {
      // Limpar streams específicos de uma categoria
      const key = `${categoryId}_${type}`;
      const namespacedKey = this.createNamespacedKey(key);
      await tx?.objectStore('streams_cache').delete(namespacedKey);
    } else if (categoryId) {
      // Limpar todos os streams de uma categoria específica
      const store = tx?.objectStore('streams_cache');
      const keys = await store?.getAllKeys() || [];
      for (const key of keys) {
        const keyStr = key.toString();
        if (keyStr.startsWith(this.currentServerHash + '_') && 
            keyStr.includes(`${categoryId}_`)) {
          await store?.delete(key);
        }
      }
    } else {
      // Limpar todos os streams do namespace atual
      const store = tx?.objectStore('streams_cache');
      const keys = await store?.getAllKeys() || [];
      for (const key of keys) {
        if (key.toString().startsWith(this.currentServerHash + '_')) {
          await store?.delete(key);
        }
      }
    }
  }

  async clearEPGCache(key?: string): Promise<void> {
    await this.initialize();
    const tx = this.db?.transaction('epg_cache', 'readwrite');
    
    if (key) {
      // Limpar EPG específico
      const namespacedKey = this.createNamespacedKey(key);
      await tx?.objectStore('epg_cache').delete(namespacedKey);
    } else {
      // Limpar todo o EPG do namespace atual
      const store = tx?.objectStore('epg_cache');
      const keys = await store?.getAllKeys() || [];
      for (const key of keys) {
        if (key.toString().startsWith(this.currentServerHash + '_')) {
          await store?.delete(key);
        }
      }
    }
  }
  
  // Limpar todos os caches de todos os servidores
  async clearAllCaches(): Promise<void> {
    await this.initialize();
    
    if (!this.db) return;
    
    // Limpar todos os caches de categorias
    if (this.db.objectStoreNames.contains('categories_cache')) {
      const categoriesTx = this.db.transaction('categories_cache', 'readwrite');
      await categoriesTx.objectStore('categories_cache').clear();
      console.log('Cache de categorias limpo');
    }
    
    // Limpar todos os caches de streams
    if (this.db.objectStoreNames.contains('streams_cache')) {
      const streamsTx = this.db.transaction('streams_cache', 'readwrite');
      await streamsTx.objectStore('streams_cache').clear();
      console.log('Cache de streams limpo');
    }
    
    // Limpar todos os caches de EPG
    if (this.db.objectStoreNames.contains('epg_cache')) {
      const epgTx = this.db.transaction('epg_cache', 'readwrite');
      await epgTx.objectStore('epg_cache').clear();
      console.log('Cache de EPG limpo');
    }
    
    // Limpar histórico de assistidos
    try {
      if (this.db.objectStoreNames.contains('watched_movies')) {
        const watchedTx = this.db.transaction('watched_movies', 'readwrite');
        await watchedTx.objectStore('watched_movies').clear();
        console.log('Histórico de filmes assistidos limpo');
      }
      
      if (this.db.objectStoreNames.contains('watched_series')) {
        const watchedSeriesTx = this.db.transaction('watched_series', 'readwrite');
        await watchedSeriesTx.objectStore('watched_series').clear();
        console.log('Histórico de séries assistidas limpo');
      }
      
      // Verificar se os object stores existem antes de tentar acessá-los
      if (this.db.objectStoreNames.contains('watched_episodes')) {
        const watchedEpisodesTx = this.db.transaction('watched_episodes', 'readwrite');
        await watchedEpisodesTx.objectStore('watched_episodes').clear();
        console.log('Histórico de episódios assistidos limpo');
      }
      
      if (this.db.objectStoreNames.contains('watched_channels')) {
        const watchedChannelsTx = this.db.transaction('watched_channels', 'readwrite');
        await watchedChannelsTx.objectStore('watched_channels').clear();
        console.log('Histórico de canais assistidos limpo');
      }
    } catch (e) {
      console.error('Erro ao limpar histórico de assistidos:', e);
    }
    
    // Limpar favoritos
    try {
      if (this.db.objectStoreNames.contains('favorites')) {
        const favoritesTx = this.db.transaction('favorites', 'readwrite');
        await favoritesTx.objectStore('favorites').clear();
        console.log('Favoritos limpos');
      }
    } catch (e) {
      console.error('Erro ao limpar favoritos:', e);
    }
    
    console.log('Todos os caches de todos os servidores foram limpos');
  }
  
  // Remover uma conexão salva pelo ID
  async saveConnection(connection: Omit<DBSchema['saved_connections']['value'], 'id'>): Promise<string> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Verificar se o object store 'saved_connections' existe
      if (this.db.objectStoreNames.contains('saved_connections')) {
        // Gerar um ID único para a conexão
        const id = `conn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        // Salvar a conexão com o ID gerado
        await this.db.put('saved_connections', {
          ...connection,
          id,
          lastUsed: Date.now()
        }, id);
        
        console.log(`Conexão ${id} salva com sucesso`);
        return id;
      } else {
        console.log('Object store saved_connections não existe, ignorando salvamento');
        throw new Error('Object store saved_connections não existe');
      }
    } catch (e) {
      console.error('Erro ao salvar conexão:', e);
      throw e;
    }
  }
  
  async updateSavedConnection(id: string, connectionData: Partial<Omit<DBSchema['saved_connections']['value'], 'id'>>): Promise<boolean> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Verificar se o object store 'saved_connections' existe
      if (this.db.objectStoreNames.contains('saved_connections')) {
        // Obter a conexão existente
        const existingConnection = await this.getSavedConnection(id);
        
        if (!existingConnection) {
          console.log(`Conexão ${id} não encontrada para atualização`);
          return false;
        }
        
        // Atualizar a conexão com os novos dados
        await this.db.put('saved_connections', {
          ...existingConnection,
          ...connectionData,
          id, // Manter o mesmo ID
          lastUsed: Date.now() // Atualizar o timestamp
        }, id);
        
        console.log(`Conexão ${id} atualizada com sucesso`);
        return true;
      } else {
        console.log('Object store saved_connections não existe, ignorando atualização');
        return false;
      }
    } catch (e) {
      console.error(`Erro ao atualizar conexão ${id}:`, e);
      return false;
    }
  }
  
  async getSavedConnection(id: string): Promise<DBSchema['saved_connections']['value'] | undefined> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Verificar se o object store 'saved_connections' existe
      if (this.db.objectStoreNames.contains('saved_connections')) {
        const connection = await this.db.get('saved_connections', id);
        return connection;
      } else {
        console.log(`Object store saved_connections não existe`);
        return undefined;
      }
    } catch (e) {
      console.error(`Erro ao obter conexão ${id}:`, e);
      return undefined;
    }
  }
  
  async removeConnection(id: string): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Verificar se o object store 'saved_connections' existe
      if (this.db.objectStoreNames.contains('saved_connections')) {
        await this.db.delete('saved_connections', id);
        console.log(`Conexão ${id} removida com sucesso`);
      } else {
        console.log(`Object store saved_connections não existe, ignorando remoção de ${id}`);
      }
    } catch (e) {
      console.error(`Erro ao remover conexão ${id}:`, e);
      throw e;
    }
  }
  
  // Remover todas as conexões salvas
  async getSavedConnections(): Promise<DBSchema['saved_connections']['value'][]> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Verificar se o object store 'saved_connections' existe
      if (this.db.objectStoreNames.contains('saved_connections')) {
        const tx = this.db.transaction('saved_connections', 'readonly');
        const store = tx.objectStore('saved_connections');
        const connections = await store.getAll();
        
        // Ordenar por último uso (mais recente primeiro)
        return connections.sort((a, b) => (b.lastUsed || 0) - (a.lastUsed || 0));
      } else {
        console.log('Object store saved_connections não existe');
        return [];
      }
    } catch (e) {
      console.error('Erro ao obter conexões salvas:', e);
      return [];
    }
  }
  
  async clearAllConnections(): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Verificar se o object store 'saved_connections' existe
      if (this.db.objectStoreNames.contains('saved_connections')) {
        const tx = this.db.transaction('saved_connections', 'readwrite');
        await tx.objectStore('saved_connections').clear();
        console.log('Todas as conexões salvas foram removidas');
      } else {
        console.log('Object store saved_connections não existe, ignorando');
      }
      
      // Também limpar a conexão atual
      if (this.db.objectStoreNames.contains('connection')) {
        await this.db.delete('connection', 'current');
        console.log('Conexão atual removida');
      } else {
        console.log('Object store connection não existe, ignorando');
      }
    } catch (e) {
      console.error('Erro ao remover todas as conexões:', e);
      throw e;
    }
  }

  async saveWatchedMovie(movie: DBSchema['watched_movies']['value']): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    await this.db.put('watched_movies', movie, movie.id);
  }

  async getWatchedMovie(id: string): Promise<DBSchema['watched_movies']['value'] | undefined> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    return this.db.get('watched_movies', id);
  }

  async getRecentlyWatchedMovies(limit: number = 10): Promise<DBSchema['watched_movies']['value'][]> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    const tx = this.db.transaction('watched_movies', 'readonly');
    const store = tx.objectStore('watched_movies');
    const movies = await store.getAll();
    
    return movies
      .sort((a, b) => b.lastWatched - a.lastWatched)
      .slice(0, limit);
  }

  async clearWatchedMovies(): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    const tx = this.db.transaction('watched_movies', 'readwrite');
    await tx.objectStore('watched_movies').clear();
  }

  async removeWatchedMovie(id: string): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    await this.db.delete('watched_movies', id);
  }

  // Series watching history methods
  async saveWatchedSeries(series: DBSchema['watched_series']['value']): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    
    // Validate the data before saving
    if (!series || typeof series.id !== 'string') {
      console.error('Invalid series data:', series);
      throw new Error('Invalid series data: id must be a string');
    }

    // Ensure all required fields are present
    const requiredFields = ['name', 'thumbnail', 'progress', 'duration', 'lastWatched'];
    for (const field of requiredFields) {
      if (!(field in series)) {
        console.error(`Missing required field: ${field}`, series);
        throw new Error(`Invalid series data: missing ${field}`);
      }
    }

    // Convert numeric fields to ensure they are numbers
    series.progress = Number(series.progress);
    series.duration = Number(series.duration);
    series.lastWatched = Number(series.lastWatched);

    // Save to database
    await this.db.put('watched_series', series, series.id);
  }

  async getWatchedSeries(id: string): Promise<DBSchema['watched_series']['value'] | undefined> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    return this.db.get('watched_series', id);
  }

  async getRecentlyWatchedSeries(limit: number = 10): Promise<DBSchema['watched_series']['value'][]> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');

    // Get all watched series
    const series = await this.db.getAll('watched_series');

    // Group by series ID (ignoring episode-specific entries)
    const seriesMap = new Map<string, DBSchema['watched_series']['value']>();
    
    series.forEach(item => {
      if (!item || typeof item.id !== 'string') {
        console.warn('Invalid series item found:', item);
        return;
      }

      // Skip episode-specific entries (those with underscores in ID)
      if (item.id.includes('_')) {
        return;
      }

      const existingItem = seriesMap.get(item.id);
      if (!existingItem || (item.lastWatched > existingItem.lastWatched)) {
        seriesMap.set(item.id, item);
      }
    });

    // Convert map to array and sort by lastWatched
    const result = Array.from(seriesMap.values())
      .sort((a, b) => (b.lastWatched || 0) - (a.lastWatched || 0))
      .slice(0, limit);

    return result;
  }

  async clearWatchedSeries(): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    const tx = this.db.transaction('watched_series', 'readwrite');
    await tx.objectStore('watched_series').clear();
  }

  async removeWatchedSeries(id: string): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Database not initialized');
    await this.db.delete('watched_series', id);
    
    // If it's a main series ID, also remove all related episode entries
    if (!id.includes('_')) {
      const tx = this.db.transaction('watched_series', 'readwrite');
      const store = tx.objectStore('watched_series');
      const allItems = await store.getAllKeys();
      
      for (const key of allItems) {
        if (key.toString().startsWith(`${id}_`)) {
          await store.delete(key);
        }
      }
    }
  }
}

export const dbService = DBService.getInstance();