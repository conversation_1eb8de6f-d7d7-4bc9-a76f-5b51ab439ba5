directories:
  output: dist_electron
  buildResources: build
appId: com.iptv.fucking.player.pro
productName: IPTV FUCKING PLAYER PRO
files:
  - filter:
      - dist/**/*
      - electron/**/*
win:
  target:
    - nsis
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: IPTV FUCKING PLAYER PRO
mac:
  target: dmg
  category: public.app-category.video
linux:
  target:
    - AppImage
    - deb
  category: Video
electronVersion: 28.3.3
