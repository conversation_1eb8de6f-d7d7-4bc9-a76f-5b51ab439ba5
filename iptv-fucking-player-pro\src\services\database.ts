// Simplified database service using localStorage
export class DatabaseService {
  private static instance: DatabaseService
  private initialized = false

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      console.log('🔧 Initializing database (localStorage mode)...')
      this.initialized = true
      console.log('✅ Database initialized successfully (localStorage mode)')
    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      throw error
    }
  }

  // Cache management (localStorage based)
  public setCache<T>(key: string, data: T, ttl?: number): void {
    try {
      const cacheData = {
        data,
        expires: ttl ? Date.now() + ttl : null
      }
      localStorage.setItem(`cache_${key}`, JSON.stringify(cacheData))
    } catch (error) {
      console.error('Error setting cache:', error)
    }
  }

  public getCache<T>(key: string): T | null {
    try {
      const cached = localStorage.getItem(`cache_${key}`)
      if (!cached) return null
      
      const parsedCache = JSON.parse(cached)
      
      // Check if expired
      if (parsedCache.expires && Date.now() > parsedCache.expires) {
        this.deleteCache(key)
        return null
      }
      
      return parsedCache.data
    } catch (error) {
      console.error('Error getting cache:', error)
      return null
    }
  }

  public deleteCache(key: string): void {
    try {
      localStorage.removeItem(`cache_${key}`)
    } catch (error) {
      console.error('Error deleting cache:', error)
    }
  }

  public clearCache(pattern?: string): void {
    try {
      if (pattern) {
        const keys = Object.keys(localStorage)
        const cacheKeys = keys.filter(k => k.startsWith('cache_') && k.includes(pattern.replace('*', '')))
        cacheKeys.forEach(key => localStorage.removeItem(key))
      } else {
        const keys = Object.keys(localStorage)
        const cacheKeys = keys.filter(k => k.startsWith('cache_'))
        cacheKeys.forEach(key => localStorage.removeItem(key))
      }
    } catch (error) {
      console.error('Error clearing cache:', error)
    }
  }

  // Streams management
  public getStreams(type: 'live' | 'movie' | 'series', categoryId?: string): any[] {
    try {
      const cacheKey = `${type}_streams_${categoryId || 'all'}`
      
      // Try to get from localStorage cache first
      const cached = localStorage.getItem(cacheKey)
      if (cached) {
        const parsedCache = JSON.parse(cached)
        const now = Date.now()
        if (parsedCache.expires > now) {
          return parsedCache.data
        }
      }

      // If not in cache, return empty array (will be populated by API calls)
      return []
    } catch (error) {
      console.error('Error getting streams from cache:', error)
      return []
    }
  }

  public saveStreams(streams: any[], type: 'live' | 'movie' | 'series', categoryId?: string): void {
    try {
      const cacheKey = `${type}_streams_${categoryId || 'all'}`
      const cacheData = {
        data: streams,
        expires: Date.now() + (6 * 60 * 60 * 1000) // 6 hours TTL
      }
      
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))
      console.log(`💾 Cached ${streams.length} ${type} streams`)
    } catch (error) {
      console.error('Error saving streams to cache:', error)
    }
  }

  // Connection management (simplified)
  public saveConnection(connection: any): void {
    try {
      const connections = this.getAllConnections()
      const existingIndex = connections.findIndex(c => c.id === connection.id)
      
      if (existingIndex >= 0) {
        connections[existingIndex] = connection
      } else {
        connections.push(connection)
      }
      
      localStorage.setItem('connections', JSON.stringify(connections))
      console.log('💾 Connection saved to localStorage')
    } catch (error) {
      console.error('Error saving connection:', error)
    }
  }

  public getConnection(): any | null {
    try {
      const connections = this.getAllConnections()
      return connections.length > 0 ? connections[0] : null
    } catch (error) {
      console.error('Error getting connection:', error)
      return null
    }
  }

  public getAllConnections(): any[] {
    try {
      const stored = localStorage.getItem('connections')
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error getting all connections:', error)
      return []
    }
  }

  public deleteConnection(id: string): void {
    try {
      const connections = this.getAllConnections()
      const filtered = connections.filter(c => c.id !== id)
      localStorage.setItem('connections', JSON.stringify(filtered))
      console.log('🗑️ Connection deleted from localStorage')
    } catch (error) {
      console.error('Error deleting connection:', error)
    }
  }

  // User preferences
  public setPreference(key: string, value: any): void {
    try {
      localStorage.setItem(`pref_${key}`, JSON.stringify(value))
    } catch (error) {
      console.error('Error setting preference:', error)
    }
  }

  public getPreference(key: string): any {
    try {
      const stored = localStorage.getItem(`pref_${key}`)
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('Error getting preference:', error)
      return null
    }
  }

  public close(): void {
    this.initialized = false
  }
}

export default DatabaseService