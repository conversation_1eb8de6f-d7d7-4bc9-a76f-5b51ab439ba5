import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { resolve } from 'path';
import { VitePWA } from 'vite-plugin-pwa';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const isElectron = process.env.ELECTRON === 'true';
  const isProduction = mode === 'production';

  console.log(`Building for ${isElectron ? 'Electron' : 'Web'}, mode: ${mode}`);

  return {
    plugins: [
      react()
    ],
    base: './',
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@utils': resolve(__dirname, './src/utils'),
        '@types': resolve(__dirname, './src/types'),
        '@hooks': resolve(__dirname, './src/hooks'),
        '@contexts': resolve(__dirname, './src/contexts'),
        '@services': resolve(__dirname, './src/services'),
        '@assets': resolve(__dirname, './src/assets'),
        '@styles': resolve(__dirname, './src/styles')
      }
    },
    server: {
      port: 5173,
      host: true,
      strictPort: true,
      watch: {
        usePolling: true
      }
    },
    preview: {
      port: 5173,
      host: true,
      strictPort: true
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      chunkSizeWarningLimit: 2000,
      assetsDir: 'assets',
      emptyOutDir: true,
      minify: isProduction ? 'esbuild' : false,
      target: 'es2015',
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Replicando o padrão exato do build bem-sucedido
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
                return 'react-vendor';
              }
              if (id.includes('hls.js') || id.includes('video.js') || id.includes('plyr') ||
                id.includes('shaka-player') || id.includes('react-player')) {
                return 'video-vendor';
              }
              if (id.includes('react-player/lib/players')) {
                // Players específicos como visto no build bem-sucedido
                const playerName = id.split('/').pop()?.replace('.js', '');
                if (playerName) {
                  return playerName;
                }
              }
            }
          },
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              return 'assets/[name]-[hash].css';
            }
            return 'assets/[name]-[hash].[ext]';
          }
        },
        external: isElectron ? [] : undefined,
        onwarn(warning, warn) {
          // Ignore certain warnings
          if (warning.code === 'MODULE_LEVEL_DIRECTIVE') return;
          if (warning.code === 'SOURCEMAP_ERROR') return;
          warn(warning);
        }
      }
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'hls.js',
        'video.js',
        'plyr',
        'react-player'
      ],
      exclude: isElectron ? ['child_process', 'crypto', 'node-machine-id'] : []
    },
    css: {
      modules: {
        localsConvention: 'camelCase'
      },
      preprocessorOptions: {
        scss: {
          additionalData: `
            $primary-color: #007bff;
            $secondary-color: #6c757d;
            $success-color: #28a745;
            $danger-color: #dc3545;
            $warning-color: #ffc107;
            $info-color: #17a2b8;
            $light-color: #f8f9fa;
            $dark-color: #343a40;
            $body-bg: #f8f9fa;
            $text-color: #212529;
            $spacing-xs: 0.25rem;
            $spacing-sm: 0.5rem;
            $spacing-md: 1rem;
            $spacing-lg: 1.5rem;
            $spacing-xl: 3rem;
          `,
          // Usar a API moderna do Sass para evitar o aviso de depreciação
          api: 'modern'
        }
      }
    },
    define: {
      'process.env': {},
      ...(isElectron ? { 'window.isElectronApp': true } : {})
    }
  };
});
