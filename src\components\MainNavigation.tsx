import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Box, Tabs, Tab, AppBar, useTheme, useMediaQuery, IconButton, Drawer, List, ListItemText, ListItemIcon, ListItemButton, Chip, Typography, Dialog, DialogTitle, DialogContent, Paper, Avatar, Badge } from '@mui/material';
import { styled, alpha } from '@mui/material/styles';
import LiveTvIcon from '@mui/icons-material/LiveTv';
import MovieIcon from '@mui/icons-material/Movie';
import TvIcon from '@mui/icons-material/Tv';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PersonIcon from '@mui/icons-material/Person';
import MenuIcon from '@mui/icons-material/Menu';
import SettingsIcon from '@mui/icons-material/Settings';
import CloseIcon from '@mui/icons-material/Close';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import DnsIcon from '@mui/icons-material/Dns';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import LogoutIcon from '@mui/icons-material/Logout';
import CachedIcon from '@mui/icons-material/Cached';
import EventNoteIcon from '@mui/icons-material/EventNote';
import CloudSyncIcon from '@mui/icons-material/CloudSync';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import HistoryIcon from '@mui/icons-material/History';
import CleaningServicesIcon from '@mui/icons-material/CleaningServices';
import { UserInfo, ServerInfo } from '../services/iptvService';
import WindowControls from './WindowControls';
import InstallPWA from './InstallPWA';
import MessageTicker from './MessageTicker';
import FastMessageTicker from './FastMessageTicker';
import TickerConfigPanel from './TickerConfigPanel';
import { useSimpleMessageTicker } from '../hooks/useMessageTicker';
import { handleExternalLinkClick } from '../utils/openExternal';

// Importar os estilos CSS para garantir a visibilidade do AppBar
import './StyledAppBar.css';

// Responsive AppBar with improved mobile design
const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(to right, rgba(18, 18, 18, 0.95), rgba(30, 30, 30, 0.95))'
    : 'linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(245, 245, 245, 0.95))',
  backdropFilter: 'blur(10px)',
  borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.3)'
    : '0 4px 20px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  WebkitAppRegion: 'drag',
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  zIndex: theme.zIndex.drawer + 1,

  // Responsive height
  minHeight: '56px',
  [theme.breakpoints.up('sm')]: {
    minHeight: '64px',
  },
  [theme.breakpoints.up('md')]: {
    minHeight: '70px',
  },

  // Animation
  animation: 'fadeIn 0.3s ease-out',
  '@keyframes fadeIn': {
    '0%': {
      opacity: 0,
      transform: 'translateY(-100%)'
    },
    '100%': {
      opacity: 1,
      transform: 'translateY(0)'
    }
  }
}));

// Logo container com estilo aprimorado
const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0.5, 1),
  borderRadius: 8,
  background: theme.palette.mode === 'dark' 
    ? 'linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0))'
    : 'linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0))',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.15)' : 'rgba(14, 165, 233, 0.2)'}`,
  transition: 'all 0.3s ease',
  WebkitAppRegion: 'no-drag', // Torna o logo clicável
  '&:hover': {
    background: theme.palette.mode === 'dark' 
      ? 'linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(14, 165, 233, 0.05))'
      : 'linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(14, 165, 233, 0.05))',
  }
}));

// Estilizando as abas com scroll horizontal em telas pequenas
const StyledTabs = styled(Tabs)(({ theme }) => ({
  WebkitAppRegion: 'no-drag', // Permite clicar nos tabs mesmo com a barra arrastável
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    gap: theme.spacing(1),
    [theme.breakpoints.down('md')]: {
      display: 'inline-flex',
      overflowX: 'auto',
      // Ocultar a barra de rolagem mas permitir o scroll
      msOverflowStyle: 'none', // IE e Edge
      scrollbarWidth: 'none', // Firefox
      '&::-webkit-scrollbar': { // Chrome, Safari e Opera
        display: 'none'
      }
    }
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '100%',
    overflow: 'auto',
    paddingBottom: theme.spacing(0.5),
  }
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  WebkitAppRegion: 'no-drag', // Permite clicar nos tabs individuais
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '0.95rem',
  padding: theme.spacing(1.2, 2),
  borderRadius: 8,
  color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
  transition: 'all 0.3s ease',
  '&.Mui-selected': {
    color: theme.palette.mode === 'dark' ? '#38bdf8' : theme.palette.primary.main,
    fontWeight: 600,
    background: theme.palette.mode === 'dark' 
      ? 'rgba(14, 165, 233, 0.08)'
      : 'rgba(14, 165, 233, 0.08)',
  },
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' 
      ? 'rgba(255, 255, 255, 0.05)' 
      : 'rgba(0, 0, 0, 0.05)',
    color: theme.palette.mode === 'dark' ? '#38bdf8' : theme.palette.primary.main,
  },
  '& .MuiTab-iconWrapper': {
    marginRight: theme.spacing(1),
    color: 'inherit',
    transition: 'all 0.3s ease',
  },
  '&.Mui-selected .MuiTab-iconWrapper': {
    color: theme.palette.mode === 'dark' ? '#38bdf8' : theme.palette.primary.main,
  },
}));

const UserInfoChip = styled(Chip)(({ theme }) => ({
  WebkitAppRegion: 'no-drag', // Torna o chip clicável
  margin: theme.spacing(0, 0.5),
  background: theme.palette.mode === 'dark' 
    ? 'linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05))'
    : 'linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05))',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
  color: theme.palette.mode === 'dark' ? theme.palette.grey[200] : theme.palette.grey[800],
  fontWeight: 500,
  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
  '& .MuiChip-icon': {
    color: theme.palette.primary.main,
  },
  '&:hover': {
    background: theme.palette.mode === 'dark' 
      ? 'linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(14, 165, 233, 0.1))'
      : 'linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(14, 165, 233, 0.1))',
  },
  transition: 'all 0.3s ease',
}));

const ActionIconButton = styled(IconButton)(({ theme }) => ({
  WebkitAppRegion: 'no-drag', // Torna o botão clicável
  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(255, 255, 255, 0.2)',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.1)' : 'rgba(14, 165, 233, 0.1)',
    borderColor: theme.palette.primary.main,
  },
}));

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    boxSizing: 'border-box',
    width: 280,
    background: theme.palette.mode === 'dark' 
      ? 'linear-gradient(135deg, rgba(18, 18, 18, 0.97), rgba(30, 30, 30, 0.97))'
      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.97), rgba(245, 245, 245, 0.97))',
    backdropFilter: 'blur(10px)',
    boxShadow: '0 0 20px rgba(0, 0, 0, 0.3)',
    WebkitAppRegion: 'no-drag', // Torna todo o conteúdo do drawer interativo
  },
}));

const NavListItemButton = styled(ListItemButton)(({ theme }) => ({
  WebkitAppRegion: 'no-drag', // Torna os itens da lista clicáveis
  margin: theme.spacing(0.5, 1),
  borderRadius: 8,
  transition: 'all 0.3s ease',
  '&.Mui-selected': {
    backgroundColor: theme.palette.mode === 'dark' 
      ? 'rgba(14, 165, 233, 0.15)' 
      : 'rgba(14, 165, 233, 0.15)',
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
    },
    '& .MuiListItemText-primary': {
      color: theme.palette.mode === 'dark' ? '#38bdf8' : theme.palette.primary.main,
      fontWeight: 600,
    },
  },
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' 
      ? 'rgba(255, 255, 255, 0.05)' 
      : 'rgba(0, 0, 0, 0.05)',
  }
}));

const InfoCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: alpha(theme.palette.background.paper, 0.6),
  backdropFilter: 'blur(8px)',
  borderRadius: 12,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'}`,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  transition: 'all 0.3s ease',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    boxShadow: '0 6px 25px rgba(0, 0, 0, 0.12)',
    transform: 'translateY(-2px)',
    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.2)' : 'rgba(14, 165, 233, 0.3)'}`,
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '5px',
    height: '100%',
    background: 'linear-gradient(to bottom, #0ea5e9, #38bdf8)',
    opacity: 0.7,
  }
}));

interface MainNavigationProps {
  showTitle?: boolean;
  userInfo?: UserInfo | null;
  serverInfo?: ServerInfo | null;
}

const MainNavigation: React.FC<MainNavigationProps> = ({ showTitle = true, userInfo = null, serverInfo = null }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [configOpen, setConfigOpen] = useState(false);
  const [showTicker, setShowTicker] = useState(true);
  const [tickerConfigOpen, setTickerConfigOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Hook para gerenciar o ticker
  const { config: tickerConfig, loading, error } = useSimpleMessageTicker();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };



  // Para o FastMessageTicker, sempre mostra (ele gerencia a própria inicialização)
  const shouldShowTicker = showTicker;

  const handleChange = (_event: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  const getActiveTab = () => {
    if (location.pathname.includes('/channels')) return '/channels';
    if (location.pathname.includes('/movies')) return '/movies';
    if (location.pathname.includes('/series')) return '/series';
    return false;
  };

  const formatTimeLeft = (expDate: string) => {
    if (!expDate) return 'Desconhecido';
    
    try {
      // Handle both timestamp format and ISO date format
      const exp = expDate.includes('-') 
        ? new Date(expDate).getTime() / 1000 
        : parseInt(expDate);
        
      const now = Math.floor(Date.now() / 1000);
      const diff = exp - now;

      if (diff <= 0) return 'Expirado';
      const days = Math.floor(diff / (24 * 60 * 60));
      const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60));
      return `${days}d ${hours}h restantes`;
    } catch (error) {
      console.error('Error formatting time left:', error);
      return 'Desconhecido';
    }
  };

  const formatDate = (timestamp: string) => {
    if (!timestamp) return 'Desconhecido';
    
    try {
      // Check if the timestamp is a number
      if (/^\d+$/.test(timestamp)) {
        const date = new Date(parseInt(timestamp) * 1000);
        return date.toLocaleDateString();
      } 
      // If it's already a date string
      else if (timestamp.includes('-') || timestamp.includes('/')) {
        return new Date(timestamp).toLocaleDateString();
      }
      
      return 'Desconhecido';
    } catch (error) {
      console.error('Error formatting date:', error, timestamp);
      return 'Desconhecido';
    }
  };

  const getStatusColor = (status: string) => {
    if (!status) return '#94a3b8'; // Return default color if status is undefined
    if (status.toLowerCase() === 'active') return '#4ade80';
    if (status.toLowerCase() === 'expired') return '#f87171';
    return '#94a3b8';
  };

  const navItems = [
    { label: 'Canais', value: '/channels', icon: <LiveTvIcon /> },
    { label: 'Filmes', value: '/movies', icon: <MovieIcon /> },
    { label: 'Séries', value: '/series', icon: <TvIcon /> },
  ];

  const drawer = (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      background: theme.palette.mode === 'dark' 
        ? 'linear-gradient(180deg, rgba(15, 23, 42, 0.9), rgba(15, 23, 42, 0.8))'
        : 'linear-gradient(180deg, rgba(249, 250, 251, 0.9), rgba(249, 250, 251, 0.8))',
    }}>
      {userInfo && (
        <Box sx={{ 
          p: 3, 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
        }}>
          <Avatar 
            sx={{ 
              width: 60, 
              height: 60, 
              mb: 1.5,
              background: 'linear-gradient(135deg, #0ea5e9, #38bdf8)',
              boxShadow: '0 4px 10px rgba(14, 165, 233, 0.4)',
            }}
          >
            <AccountCircleIcon sx={{ fontSize: 40 }} />
          </Avatar>
          
          <Typography variant="h6" color="primary" fontWeight="600" sx={{ mb: 0.5 }}>
            {userInfo.username}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Badge
              variant="dot"
              sx={{ 
                mr: 1, 
                '& .MuiBadge-badge': { 
                  backgroundColor: getStatusColor(userInfo.status),
                  boxShadow: `0 0 0 2px ${theme.palette.background.paper}`
                } 
              }}
            />
            <Typography variant="body2" color="text.secondary">
              {userInfo.status}
            </Typography>
          </Box>
          
          <Typography 
            variant="caption" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              color: 'warning.main',
              fontWeight: 500,
              px: 1.5,
              py: 0.75,
              borderRadius: 10,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(250, 204, 21, 0.1)' : 'rgba(250, 204, 21, 0.1)',
            }}
          >
            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: 16 }} />
            {formatTimeLeft(userInfo.exp_date)}
          </Typography>
        </Box>
      )}
      
      {/* Install PWA Component */}
      <Box sx={{ px: 2, py: 1 }}>
        <InstallPWA />
      </Box>
      
      <List sx={{ px: 1, py: 2, flex: 1 }}>
        {navItems.map((item) => (
          <NavListItemButton 
            key={item.value} 
            onClick={() => {
              navigate(item.value);
              setMobileOpen(false);
            }}
            selected={getActiveTab() === item.value}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText 
              primary={item.label} 
              primaryTypographyProps={{
                fontWeight: getActiveTab() === item.value ? 600 : 400
              }}
            />
          </NavListItemButton>
        ))}
      </List>
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider', textAlign: 'center' }}>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ cursor: 'pointer' }}
          onClick={() => handleExternalLinkClick('https://nekotv.vercel.app/')}
          title="Visitar o site da Neko TV"
        >
          © 2023 Neko TV
        </Typography>
      </Box>
    </Box>
  );

  return (
    <>
      <StyledAppBar
        position="fixed"
        elevation={0}
        className="neko-app-bar"
      >
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: { xs: '0.5rem 1rem', md: '0.5rem 1.5rem' }
        }}>
          {showTitle && (
            <LogoContainer
              sx={{ mr: 2, cursor: 'pointer' }}
              onClick={() => handleExternalLinkClick('https://nekotv.vercel.app/')}
              title="Visitar o site da Neko TV"
            >
              <LiveTvIcon color="primary" sx={{ mr: 1 }} />
              <Box sx={{
                fontWeight: 600,
                fontSize: '1.25rem',
                color: theme.palette.mode === 'dark' ? '#38bdf8' : '#0ea5e9',
                textShadow: theme.palette.mode === 'dark'
                  ? '0 0 10px rgba(56, 189, 248, 0.3)'
                  : '0 0 10px rgba(14, 165, 233, 0.3)',
              }}>
                Neko TV
              </Box>
            </LogoContainer>
          )}

          {isMobile ? (
            <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center', WebkitAppRegion: 'no-drag' }}>
              {userInfo && (
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 1, WebkitAppRegion: 'no-drag' }}>
                  <UserInfoChip
                    icon={<AccessTimeIcon />}
                    label={formatTimeLeft(userInfo.exp_date)}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              )}
              <ActionIconButton
                color="inherit"
                aria-label="configurações"
                onClick={() => setConfigOpen(true)}
                size="small"
                sx={{ mr: 1 }}
              >
                <SettingsIcon fontSize="small" />
              </ActionIconButton>
              <ActionIconButton
                color="inherit"
                aria-label="abrir menu"
                edge="start"
                onClick={handleDrawerToggle}
                size="small"
              >
                <MenuIcon fontSize="small" />
              </ActionIconButton>
              <WindowControls />
            </Box>
          ) : (
            <>
              <Box sx={{ WebkitAppRegion: 'no-drag' }}>
                <StyledTabs
                  value={getActiveTab()}
                  onChange={handleChange}
                  indicatorColor="primary"
                  textColor="primary"
                  variant="standard"
                >
                  {navItems.map((item) => (
                    <StyledTab
                      key={item.value}
                      label={item.label}
                      value={item.value}
                      icon={item.icon}
                      iconPosition="start"
                    />
                  ))}
                </StyledTabs>
              </Box>

              <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center', WebkitAppRegion: 'no-drag' }}>
                {userInfo && (
                  <>
                    <UserInfoChip
                      icon={<PersonIcon />}
                      label={userInfo.username}
                      size="small"
                      variant="outlined"
                    />
                    <UserInfoChip
                      icon={<AccessTimeIcon />}
                      label={formatTimeLeft(userInfo.exp_date)}
                      size="small"
                      variant="outlined"
                    />
                  </>
                )}
                <ActionIconButton
                  color="inherit"
                  aria-label="configurações"
                  onClick={() => setConfigOpen(true)}
                  size="small"
                  sx={{ ml: 0.5 }}
                >
                  <SettingsIcon fontSize="small" />
                </ActionIconButton>
                <WindowControls />
              </Box>
            </>
          )}
        </Box>
      </StyledAppBar>

      {/* Ticker de mensagens - FORA do AppBar, bem abaixo de toda a navegação */}
      {shouldShowTicker && (
        <Box
          sx={{
            position: 'fixed',
            top: { xs: '72px', sm: '80px', md: '86px' }, // Mais abaixo para evitar sobreposição
            left: 0,
            right: 0,
            zIndex: 9999, // Muito alto para ficar acima de tudo, exceto AppBar (10000)
            pointerEvents: 'auto',
            backgroundColor: 'transparent', // Garantir que não há conflito de background
          }}
        >
          <FastMessageTicker />
        </Box>
      )}

      <Box sx={{
        height: shouldShowTicker
          ? { xs: '132px', sm: '144px', md: '156px' } // nav + ticker maior (60px) + espaço
          : { xs: '56px', sm: '64px', md: '70px' }, // apenas nav
        mb: shouldShowTicker ? 2 : 0 // Adicionar margem quando ticker está ativo
      }} className="main-content-below-appbar" />

      <Dialog 
        open={configOpen} 
        onClose={() => setConfigOpen(false)}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(15, 23, 42, 0.95)' : 'rgba(249, 250, 251, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: 3,
            boxShadow: '0 25px 50px rgba(0,0,0,0.25)',
            backgroundImage: theme.palette.mode === 'dark' 
              ? 'linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(15, 23, 42, 0) 50%)'
              : 'linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(249, 250, 251, 0) 50%)',
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid',
          borderColor: 'divider',
          py: 2,
          px: 3,
        }}>
          <Typography 
            variant="h5" 
            component="div" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              fontWeight: 600,
              background: 'linear-gradient(to right, #0ea5e9, #38bdf8)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            <SettingsIcon sx={{ mr: 1, color: '#0ea5e9' }} />
            Central de Configurações
          </Typography>
          <ActionIconButton onClick={() => setConfigOpen(false)} size="small">
            <CloseIcon fontSize="small" />
          </ActionIconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 4 }}>
          {userInfo && (
            <Box sx={{ mb: 4 }}>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                mb: 3, 
                pb: 1, 
                borderBottom: '1px solid',
                borderColor: 'divider'
              }}>
                <VerifiedUserIcon sx={{ color: '#0ea5e9', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }} color="primary">
                  Informações do Usuário
                </Typography>
              </Box>
              
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
                gap: 2.5
              }}>
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Nome de Usuário</Typography>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: 'primary.main', 
                      fontWeight: 600,
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <PersonIcon sx={{ mr: 0.5, fontSize: 18 }} />
                    {userInfo.username}
                  </Typography>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Status</Typography>
                  <Box sx={{ 
                    px: 1.5, 
                    py: 0.5, 
                    borderRadius: 10, 
                    bgcolor: userInfo.status.toLowerCase() === 'active' 
                      ? 'rgba(34, 197, 94, 0.15)' 
                      : 'rgba(239, 68, 68, 0.15)',
                    color: userInfo.status.toLowerCase() === 'active' ? '#22c55e' : '#ef4444',
                    fontSize: '0.8rem',
                    fontWeight: 600,
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <Box 
                      component="span" 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        bgcolor: userInfo.status.toLowerCase() === 'active' ? '#22c55e' : '#ef4444',
                        mr: 0.8,
                        boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
                      }} 
                    />
                    {userInfo.status}
                  </Box>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Expiração</Typography>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: 'warning.main', 
                      fontWeight: 600,
                      display: 'flex',
                      alignItems: 'center',
                      px: 1.5,
                      py: 0.5,
                      borderRadius: 10,
                      bgcolor: 'rgba(250, 204, 21, 0.1)'
                    }}
                  >
                    <AccessTimeIcon sx={{ mr: 0.5, fontSize: 18 }} />
                    {formatTimeLeft(userInfo.exp_date)}
                  </Typography>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Criado em</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {formatDate(userInfo.created_at)}
                  </Typography>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Conexões ativas</Typography>
                  <Box sx={{ 
                    px: 1.5, 
                    py: 0.5, 
                    borderRadius: 10, 
                    bgcolor: 'rgba(14, 165, 233, 0.1)',
                    color: '#0ea5e9',
                    fontSize: '0.85rem',
                    fontWeight: 600
                  }}>
                    {userInfo.active_cons} / {userInfo.max_connections}
                  </Box>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Conta de teste</Typography>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      px: 1.5, 
                      py: 0.5, 
                      borderRadius: 10, 
                      bgcolor: userInfo.is_trial === '0' 
                        ? 'rgba(100, 116, 139, 0.1)'
                        : 'rgba(250, 204, 21, 0.2)',
                      color: userInfo.is_trial === '0' 
                        ? theme.palette.mode === 'dark' ? '#94a3b8' : '#64748b'
                        : theme.palette.mode === 'dark' ? '#facc15' : '#ca8a04',
                      fontWeight: 600
                    }}
                  >
                    {userInfo.is_trial === '0' ? 'Não' : 'Sim'}
                  </Typography>
                </InfoCard>
                
                {userInfo.message && (
                  <InfoCard sx={{ gridColumn: '1 / -1' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Mensagem</Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: 'warning.main', 
                        fontWeight: 500,
                        maxWidth: '70%',
                        textAlign: 'right'
                      }}
                    >
                      {userInfo.message}
                    </Typography>
                  </InfoCard>
                )}
              </Box>
            </Box>
          )}
          
          {serverInfo && (
            <Box>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                mb: 3, 
                pb: 1, 
                borderBottom: '1px solid',
                borderColor: 'divider'
              }}>
                <DnsIcon sx={{ color: '#0ea5e9', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }} color="primary">
                  Informações do Servidor
                </Typography>
              </Box>
              
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
                gap: 2.5
              }}>
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>URL</Typography>
                  <Box sx={{ 
                    maxWidth: '100%',
                    overflow: 'hidden',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
                    px: 1,
                    py: 0.5,
                    borderRadius: 1,
                  }}>
                    <Typography 
                      variant="body2" 
                      noWrap 
                      sx={{ 
                        fontFamily: 'monospace',
                        fontSize: '0.75rem',
                        display: 'block',
                        width: '100%',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {/* Extrair apenas o domínio base da URL */}
                      {(() => {
                        try {
                          const url = new URL(serverInfo.url);
                          return url.origin;
                        } catch (e) {
                          return serverInfo.url;
                        }
                      })()}
                    </Typography>
                  </Box>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Protocolo</Typography>
                  <Typography 
                    variant="body2"
                    sx={{ 
                      fontFamily: 'monospace',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)',
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      fontSize: '0.75rem'
                    }}
                  >
                    {serverInfo.server_protocol || 'HTTP'}
                  </Typography>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Fuso horário</Typography>
                  <Typography variant="body2">
                    {serverInfo.timezone || 'America/Sao_Paulo'}
                  </Typography>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Hora do servidor</Typography>
                  <Typography variant="body2">
                    {serverInfo.time_now || new Date().toLocaleTimeString()}
                  </Typography>
                </InfoCard>
                
                <InfoCard>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>Status da conexão</Typography>
                  <Box sx={{ 
                    px: 1.5, 
                    py: 0.5, 
                    borderRadius: 10, 
                    bgcolor: 'rgba(34, 197, 94, 0.15)',
                    color: '#22c55e',
                    fontSize: '0.8rem',
                    fontWeight: 600,
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <Box 
                      component="span" 
                      sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        bgcolor: '#22c55e',
                        mr: 0.8,
                        boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
                      }} 
                    />
                    Conectado
                  </Box>
                </InfoCard>
              </Box>
            </Box>
          )}

          {/* Configurações do Ticker */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 3,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'divider'
            }}>
              <SettingsIcon sx={{ color: '#0ea5e9', mr: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 600 }} color="primary">
                Configurações do Ticker
              </Typography>
            </Box>
          </Box>

          {/* Opções de Conta */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 3,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'divider'
            }}>
              <AccountCircleIcon sx={{ color: '#0ea5e9', mr: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 600 }} color="primary">
                Opções de Conta
              </Typography>
            </Box>
            
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
              gap: 2.5
            }}>
              <Paper 
                onClick={async () => {
                  try {
                    if (confirm('Deseja realmente trocar de conta? Você será redirecionado para a tela de login.')) {
                      // Importar o dbService aqui para evitar problemas de dependência circular
                      const { dbService } = await import('../services/dbService');
                      
                      // Desativar o login automático da conta atual
                      const currentConnection = await dbService.getConnection();
                      if (currentConnection) {
                        await dbService.setConnection({
                          ...currentConnection,
                          autoLogin: false
                        });
                      }
                      
                      // Remover tokens e informações básicas
                      localStorage.removeItem('user_info');
                      localStorage.removeItem('auth_token');
                      localStorage.removeItem('server_info');
                      localStorage.removeItem('iptv_credentials');
                      localStorage.removeItem('session_data');
                      
                      // Fechar o menu de configurações
                      setConfigOpen(false);
                      
                      // Redirecionar para a tela de login com parâmetro para evitar cache
                      const switchParam = Date.now();
                      navigate(`/access?switch=${switchParam}`, { replace: true });
                    }
                  } catch (error) {
                    console.error('Erro ao trocar de conta:', error);
                    alert('Ocorreu um erro ao tentar trocar de conta. Por favor, tente novamente.');
                  }
                }}
                sx={{ 
                  p: 2, 
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  cursor: 'pointer',
                  border: '1px solid',
                  borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: 'primary.main',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.05)' : 'rgba(14, 165, 233, 0.05)',
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: 'primary.main', 
                      width: 40, 
                      height: 40,
                      mr: 2
                    }}
                  >
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                      Trocar de Conta
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Acessar com outro usuário mantendo configurações
                    </Typography>
                  </Box>
                </Box>
                <IconButton size="small" sx={{ color: 'primary.main' }}>
                  <ArrowForwardIcon />
                </IconButton>
              </Paper>
            </Box>
          </Box>
          
          {/* Gerenciamento de Cache */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              mb: 3, 
              pb: 1, 
              borderBottom: '1px solid',
              borderColor: 'divider'
            }}>
              <CachedIcon sx={{ color: '#0ea5e9', mr: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 600 }} color="primary">
                Gerenciamento de Cache
              </Typography>
            </Box>
            
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: '1fr',
              gap: 2.5
            }}>
              {/* Opção única: Limpar Cache */}
              <Paper 
                onClick={async () => {
                  try {
                    if (confirm('Limpar o cache e fazer logout? Isso irá remover todos os dados armazenados.')) {
                      // Limpar IndexedDB via dbService
                      try {
                        // Importar o dbService aqui para evitar problemas de dependência circular
                        const { dbService } = await import('../services/dbService');
                        
                        // Limpar TODOS os caches de TODAS as listas IPTV
                        await dbService.clearAllCaches();
                        
                        // Limpar também informações de usuário
                        await dbService.setUserInfo({} as any);
                        await dbService.setPlaylistInfo({} as any);
                        await dbService.clearPlaylistChunks();
                        
                        // Remover todas as conexões salvas
                        await dbService.clearAllConnections();
                        
                        // Limpar completamente o banco de dados
                        await dbService.deleteDatabase();
                        
                        console.log('Todos os caches e conexões do IndexedDB foram completamente limpos');
                      } catch (dbError) {
                        console.error('Erro ao limpar caches do IndexedDB:', dbError);
                      }
                    
                      // Garantir explicitamente que tokens e informações de usuário sejam removidos
                      localStorage.removeItem('user_info');
                      localStorage.removeItem('auth_token');
                      localStorage.removeItem('server_info');
                      localStorage.removeItem('iptv_credentials');
                      localStorage.removeItem('session_data');
                      
                      // Preservar apenas o tema
                      const themeMode = localStorage.getItem('theme_mode');
                      
                      // Limpar tudo completamente
                      localStorage.clear();
                      sessionStorage.clear();
                      
                      // Limpar Cache API (usado para armazenar imagens, scripts, etc.)
                      if ('caches' in window) {
                        try {
                          // Listar todos os caches
                          const cacheNames = await caches.keys();
                          
                          // Excluir cada cache
                          await Promise.all(
                            cacheNames.map(cacheName => caches.delete(cacheName))
                          );
                          
                          console.log('Cache API limpo com sucesso');
                        } catch (e) {
                          console.error('Erro ao limpar Cache API:', e);
                        }
                      }
                      
                      // Limpar Service Worker se existir
                      if ('serviceWorker' in navigator) {
                        try {
                          const registrations = await navigator.serviceWorker.getRegistrations();
                          for (const registration of registrations) {
                            await registration.unregister();
                          }
                          console.log('Service Workers removidos');
                        } catch (e) {
                          console.error('Erro ao remover Service Workers:', e);
                        }
                      }
                      
                      // Limpar cookies
                      const cookies = document.cookie.split(";");
                      for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i];
                        const eqPos = cookie.indexOf("=");
                        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                      }
                      
                      // Marcar explicitamente que o usuário deslogou
                      localStorage.setItem('logged_out', 'true');
                      
                      // Manter apenas o tema se existir
                      if (themeMode) {
                        localStorage.setItem('theme_mode', themeMode);
                      }
                      
                      // Fechar a janela de configurações
                      setConfigOpen(false);
                      
                      // Avisar ao usuário que o logout foi realizado
                      alert('Cache limpo com sucesso! Você será desconectado.');
                      
                      // Usar o navigate do React Router em vez de window.location.replace
                      const cleanParam = Date.now();
                      navigate(`/access?logout=true&clean=${cleanParam}`, { replace: true });
                    }
                  } catch (error) {
                    console.error('Erro ao limpar cache:', error);
                    alert('Ocorreu um erro ao limpar o cache. Por favor, tente novamente.');
                  }
                }}
                sx={{ 
                  p: 2.5, 
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  cursor: 'pointer',
                  border: '1px solid',
                  borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.2s ease',
                  maxWidth: '500px',
                  mx: 'auto',
                  '&:hover': {
                    borderColor: '#0ea5e9',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.05)' : 'rgba(14, 165, 233, 0.05)',
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: '#0ea5e9', 
                      width: 45, 
                      height: 45,
                      mr: 2
                    }}
                  >
                    <CleaningServicesIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, fontSize: '1.05rem' }}>
                      Limpar Cache e Sair
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Remove todos os dados e faz logout da sua conta
                    </Typography>
                  </Box>
                </Box>
                <IconButton size="small" sx={{ color: '#0ea5e9' }}>
                  <DeleteSweepIcon fontSize="medium" />
                </IconButton>
              </Paper>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>

      <StyledDrawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
        }}
      >
        {drawer}
      </StyledDrawer>

      {/* Painel de Configuração do Ticker */}
      <TickerConfigPanel
        open={tickerConfigOpen}
        onClose={() => setTickerConfigOpen(false)}
      />
    </>
  );
};

export default MainNavigation;