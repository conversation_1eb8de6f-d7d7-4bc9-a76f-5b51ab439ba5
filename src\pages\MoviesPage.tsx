import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  CircularProgress, 
  Grid, 
  Paper,
  Tabs,
  Tab,
  alpha,
  TextField,
  InputAdornment,
  useMediaQuery,
  Drawer,
  IconButton,
  Chip,
  Divider,
  useTheme,
  Button,
  Modal,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Fade,
  Rating,
  DialogTitle,
  DialogContent,
  DialogActions,
  Dialog,
  Tooltip,
  FormControl,
  Stack,
  Pagination
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { createIPTVService, IPTVService, Category, Stream } from '../services/iptvService';
import { dbService } from '../services/dbService';
import ContinueWatching from '../components/ContinueWatching/ContinueWatching';
import MovieCard from '../components/MovieCard/MovieCard';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import TuneIcon from '@mui/icons-material/Tune';
import CloseIcon from '@mui/icons-material/Close';
import AppsIcon from '@mui/icons-material/Apps';
import ViewListIcon from '@mui/icons-material/ViewList';
import MovieIcon from '@mui/icons-material/Movie';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SortIcon from '@mui/icons-material/Sort';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { motion } from 'framer-motion';
import StarIcon from '@mui/icons-material/Star';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import TheatersIcon from '@mui/icons-material/Theaters';
import { tmdbService } from '../services/tmdbService';
import VideoPlayer from '../components/VideoPlayer/VideoPlayer';
import SearchBar from '../components/SearchBar';
import { Searchable } from '../services/searchService';
import BackgroundEffects from '../components/BackgroundEffects';
import LiveTvIcon from '@mui/icons-material/LiveTv';

// Interface estendida para incluir propriedades de progresso
interface MovieWithProgress extends Stream {
  progress?: number;
  duration?: number;
  direct_source?: string;
}

const MotionGrid = motion.create(Grid);
const MotionBox = motion.create(Box);

const MoviesPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [movies, setMovies] = useState<Stream[]>([]);
  const [allMovies, setAllMovies] = useState<Stream[]>([]);
  const [iptvService, setIptvService] = useState<IPTVService | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [categoryPages, setCategoryPages] = useState<Record<string, number>>({});
  const [globalSearch, setGlobalSearch] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [searching, setSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<Stream[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const moviesPerPage = 20;
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const lastCategoryRef = useRef<string | null>(null);
  const [sortBy, setSortBy] = useState<'alpha' | 'recent' | 'rating'>('alpha');
  const [selectedMovie, setSelectedMovie] = useState<MovieWithProgress | null>(null);
  const [movieDetailsOpen, setMovieDetailsOpen] = useState(false);
  const [movieDetails, setMovieDetails] = useState<any>(null);
  const [movieLoading, setMovieLoading] = useState(false);
  const [playerOpen, setPlayerOpen] = useState(false);
  const [streamUrl, setStreamUrl] = useState<string>('');
  const playerRef = useRef<any>(null);

  useEffect(() => {
    const initService = async () => {
      try {
        const service = await createIPTVService();
        setIptvService(service);
        setError(null);
      } catch (error) {
        console.error('Error initializing IPTV service:', error);
        setError('Failed to initialize IPTV service. Please check your connection.');
        navigate('/', { replace: true });
      }
    };
    initService();
  }, [navigate]);

  useEffect(() => {
    if (iptvService) {
      loadCategories();
      loadAllMovies();
    }
  }, [iptvService]);

  const loadCategories = async () => {
    if (!iptvService) return;
    
    try {
      setLoading(true);
      const categories = await iptvService.getMovieCategories();
      
      // Sort categories alphabetically
      categories.sort((a, b) => a.name.localeCompare(b.name));
      
      setCategories(categories);
      if (categories.length > 0) {
        // If there's a previously selected category, try to select it again
        if (lastCategoryRef.current && categories.some(cat => cat.id === lastCategoryRef.current)) {
          setSelectedCategoryId(lastCategoryRef.current);
        } else {
          setSelectedCategoryId(categories[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Erro ao carregar categorias. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const loadAllMovies = async () => {
    if (!iptvService) return;
    
    try {
      const allMoviesArray: Stream[] = [];
      setLoading(true);
      
      // Get all movie categories
      const categories = await iptvService.getMovieCategories();
      
      // Load movies from each category and merge them
      for (const category of categories) {
        const categoryMovies = await iptvService.getMovieStreams(category.id);
        allMoviesArray.push(...categoryMovies);
      }
      
      // Remove duplicates (in case a movie appears in multiple categories)
      const uniqueMovies = allMoviesArray.filter((movie, index, self) =>
        index === self.findIndex((m) => m.id === movie.id)
      );
      
      setAllMovies(uniqueMovies);
    } catch (error) {
      console.error('Error loading all movies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedCategoryId && iptvService) {
      loadMovies(selectedCategoryId);
      lastCategoryRef.current = selectedCategoryId;
      setCategoryPages(prev => ({
        ...prev,
        [selectedCategoryId]: 1
      }));
    }
  }, [selectedCategoryId, iptvService]);

  const loadMovies = async (categoryId: string) => {
    if (!iptvService) return;
    
    try {
      setLoading(true);
      const streams = await iptvService.getMovieStreams(categoryId);
      // Sort alphabetically by default
      streams.sort((a, b) => a.name.localeCompare(b.name));
      setMovies(streams);
    } catch (error) {
      console.error('Error loading movies:', error);
      setError('Erro ao carregar filmes. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (_event: React.SyntheticEvent, newValue: string) => {
    setSelectedCategoryId(newValue);
  };

  const handleMovieSelect = async (movie: Stream) => {
    const selectedMovieObj = searchTerm && allMovies.length > 0
      ? allMovies.find(m => m.id === movie.id) 
      : movies.find(m => m.id === movie.id);

    if (selectedMovieObj) {
      setSelectedMovie(selectedMovieObj as MovieWithProgress);
      setMovieDetailsOpen(true);
      
      // Buscar informações detalhadas do filme da API IPTV
      if (iptvService) {
        try {
          const movieInfo = await iptvService.getMovieInfo(movie.id);
          if (movieInfo) {
            setSelectedMovie(prevMovie => ({...prevMovie, ...movieInfo}));
          }
        } catch (error) {
          console.error("Erro ao buscar informações detalhadas do filme:", error);
        }
      }
      
      // Buscar informações adicionais do TMDB
      try {
        const movieSearchResults = await tmdbService.searchMovie(movie.name);
        if (movieSearchResults && movieSearchResults.results && movieSearchResults.results.length > 0) {
          const movieId = movieSearchResults.results[0].id;
          const details = await tmdbService.getMovieDetails(movieId);
          setMovieDetails(details);
        }
      } catch (error) {
        console.error("Erro ao buscar dados do TMDB:", error);
      }
    }
  };

  const handleCloseMovieDetails = () => {
    setMovieDetailsOpen(false);
    setSelectedMovie(null);
    setMovieDetails(null);
  };

  const handlePlayMovie = async () => {
    if (selectedMovie && iptvService) {
      try {
        setMovieLoading(true);
        
        // Verificar se há um progresso salvo para este filme
        let startTime = 0;
        try {
          const watchedMovie = await dbService.getWatchedMovie(selectedMovie.id);
          if (watchedMovie && watchedMovie.progress > 0) {
            startTime = watchedMovie.progress;
            console.log(`Progresso encontrado: ${Math.floor(startTime / 60)}:${Math.floor(startTime % 60)}min`);
            
            // Atualizar o filme selecionado com o progresso
            setSelectedMovie(prev => ({
              ...prev!,
              progress: startTime
            } as MovieWithProgress));
          }
        } catch (err) {
          console.error('Erro ao buscar progresso do filme:', err);
        }
        
        const streamUrl = iptvService.getStreamUrl(selectedMovie.id, 'movie', selectedMovie.direct_source);
        console.log('Reproduzindo:', selectedMovie.name, streamUrl, startTime > 0 ? `(retomando de ${Math.floor(startTime / 60)}:${Math.floor(startTime % 60)}min)` : '(desde o início)');
        
        setStreamUrl(streamUrl);
        setPlayerOpen(true);
        setMovieDetailsOpen(false);
        
        // Não precisamos mais tentar aplicar o seek aqui, o VideoPlayer já cuida disso
        // usando o initialTime definido
      } catch (error) {
        console.error('Erro ao reproduzir filme:', error);
        alert('Não foi possível reproduzir este filme. Tente novamente.');
      } finally {
        setMovieLoading(false);
      }
    }
  };

  const handleSearch = useCallback((results: Searchable[], query: string) => {
    setSearchTerm(query);
    setSearchResults(results as Stream[]);
    
    if (query && results.length > 0) {
      setSearchModalOpen(true);
    } else if (query && results.length === 0) {
      // Se não houver resultados mas a busca foi realizada, também mostre o modal
      setSearchModalOpen(true);
    } else {
      setSearchModalOpen(false);
    }
  }, []);

  const handleGlobalSearchToggle = (enabled: boolean) => {
    setGlobalSearch(enabled);
  };

  const handleSelectSearchResult = (movieId: string) => {
    setSearchModalOpen(false);
    const movieToSelect = movies.find(m => m.id === movieId) || allMovies.find(m => m.id === movieId);
    if (movieToSelect) {
      handleMovieSelect(movieToSelect);
    }
  };

  const closeSearchModal = () => {
    setSearchModalOpen(false);
  };

  const filteredMovies = movies.filter(movie => 
    movie.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  // Função para ordenar os filmes
  const getSortedMovies = () => {
    const sorted = [...filteredMovies];
    
    switch (sortBy) {
      case 'alpha':
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
      case 'recent':
        // Ordenar por data se disponível, caso contrário ordem atual
        return sorted.sort((a, b) => {
          if (!a.year || !b.year) return 0;
          return parseInt(b.year) - parseInt(a.year);
        });
      case 'rating':
        // Ordenar por avaliação se disponível
        return sorted.sort((a, b) => {
          if (!a.rating || !b.rating) return 0;
          return b.rating - a.rating;
        });
      default:
        return sorted;
    }
  };

  // Resetar para página 1 quando mudar de categoria (se ainda não existir)
  useEffect(() => {
    if (selectedCategoryId && !categoryPages[selectedCategoryId]) {
      setCategoryPages(prev => ({
        ...prev,
        [selectedCategoryId]: 1
      }));
    }
  }, [selectedCategoryId, categoryPages]);

  // Pegar a página atual da categoria selecionada
  const getCurrentPage = useCallback(() => {
    return categoryPages[selectedCategoryId] || 1;
  }, [categoryPages, selectedCategoryId]);

  // Calcular o número total de páginas com base nos filmes filtrados
  const pageCount = Math.ceil(filteredMovies.length / moviesPerPage);

  // Função para mudar de página
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setCategoryPages(prev => ({
      ...prev,
      [selectedCategoryId]: value
    }));
    // Scroll to top after changing page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Aplicar ordenação aos resultados de pesquisa
  const handleSort = (type: 'alpha' | 'recent' | 'rating') => {
    setSortBy(type);
    setFilterDrawerOpen(false);
  };

  const handleClosePlayer = () => {
    setPlayerOpen(false);
    setStreamUrl('');
  };

  const handleTimeUpdate = (currentTime: number, duration: number) => {
    // Salvar o progresso de reprodução
    if (selectedMovie) {
      try {
        dbService.saveWatchedMovie({
          id: selectedMovie.id,
          name: selectedMovie.name,
          thumbnail: selectedMovie.thumbnail || '',
          cover: selectedMovie.cover,
          progress: currentTime,
          duration: duration,
          lastWatched: Date.now(),
          categoryId: selectedCategoryId
        });
      } catch (error) {
        console.error('Erro ao salvar progresso do filme:', error);
      }
    }
  };

  const playMovieDirectly = async (movie: Stream, startTime: number = 0) => {
    if (!iptvService) return;
    
    try {
      setSelectedMovie(movie as MovieWithProgress);
      setMovieLoading(true);
      
      // Buscar informações detalhadas para ter direct_source, se disponível
      let movieWithDetails = movie as MovieWithProgress;
      
      try {
        const movieInfo = await iptvService.getMovieInfo(movie.id);
        if (movieInfo) {
          movieWithDetails = {...movie, ...movieInfo};
          setSelectedMovie(movieWithDetails);
        }
      } catch (error) {
        console.error("Erro ao buscar informações detalhadas para reprodução direta:", error);
      }
      
      // Se não foi passado um startTime específico, vamos tentar buscar do banco de dados
      if (startTime <= 0) {
        try {
          const watchedMovie = await dbService.getWatchedMovie(movie.id);
          if (watchedMovie && watchedMovie.progress > 0) {
            startTime = watchedMovie.progress;
            console.log(`Progresso encontrado no banco de dados: ${Math.floor(startTime / 60)}:${Math.floor(startTime % 60)}min`);
          }
        } catch (err) {
          console.error("Erro ao buscar progresso salvo:", err);
        }
      }
      
      const streamUrl = iptvService.getStreamUrl(movie.id, 'movie', movieWithDetails.direct_source);
      console.log('Reproduzindo direto:', movie.name, streamUrl, startTime > 0 ? `(retomando de ${Math.floor(startTime / 60)}:${Math.floor(startTime % 60)}min)` : '');
      
      // Atualizar o estado do filme selecionado para incluir o progresso
      setSelectedMovie(prev => ({
        ...prev!,
        progress: startTime
      } as MovieWithProgress));
      
      setStreamUrl(streamUrl);
      setPlayerOpen(true);
      
      // Não precisamos mais tentar aplicar o seek aqui, o VideoPlayer vai cuidar disso
      // com o initialTime que foi definido
    } catch (error) {
      console.error('Erro ao reproduzir filme diretamente:', error);
      alert('Não foi possível reproduzir este filme. Tente novamente.');
    } finally {
      setMovieLoading(false);
    }
  };

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (!iptvService) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Inicializando serviço...</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Background Effects */}
      <BackgroundEffects type="movies" intensity="medium" />
      
      {/* Header section */}
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          mb: 3,
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 2, md: 0 }
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Filmes
          </Typography>
        </Box>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2,
          width: { xs: '100%', md: 'auto' }
        }}>
          <Box 
              sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              flexGrow: 1,
              maxWidth: 600,
              mb: { xs: 2, md: 0 }
            }}
          >
            {/* SEARCH BAR - IPTV Player */}
            <Box
              position="sticky"
              top={0}
              zIndex={10}
            >
              <Grid container alignItems="center" spacing={2} px={2}>
                <Grid item xs sm md>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <SearchBar
                      items={globalSearch ? allMovies : movies}
                      placeholder="Buscar filmes..."
                      onSearch={handleSearch}
                      onSelectItem={(movie) => handleSelectSearchResult(movie.id)}
                    />
                    <Box ml={1}>
                      <Tooltip title={globalSearch ? "Busca global ativada" : "Busca global desativada"}>
                        <FormControlLabel
                          control={
                            <Switch
                              size="small"
                              checked={globalSearch}
                              onChange={(e) => {
                                const isEnabled = e.target.checked;
                                handleGlobalSearchToggle(isEnabled);
                                console.log(`Busca global ${isEnabled ? 'ativada' : 'desativada'} (${movies.length} filmes locais, ${allMovies.length} filmes globais)`);
                              }}
                            />
                          }
                          label={
                            <Typography variant="caption">
                              Global
                            </Typography>
                          }
                        />
                      </Tooltip>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Box>
          
          <IconButton 
            onClick={() => setFilterDrawerOpen(true)}
            sx={{ 
              bgcolor: 'rgba(255, 255, 255, 0.05)',
              '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' }
            }}
          >
            <TuneIcon />
          </IconButton>
          
          {/* View mode toggle buttons removed as requested */}
        </Box>
      </Box>
      
      {/* Continue Watching Section */}
      <ContinueWatching 
        onMovieSelect={handleMovieSelect} 
        onMoviePlay={playMovieDirectly}
      />
      
      {/* Category Tabs */}
      <Box sx={{ 
        mb: 3, 
        mt: 4,
        borderBottom: 1, 
        borderColor: 'divider',
        position: 'relative'
      }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          Categorias
        </Typography>
        <Box sx={{ maxWidth: '100%', overflow: 'auto', pb: 1 }}>
          <Tabs
            value={selectedCategoryId}
            onChange={handleCategoryChange}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '& .MuiTabs-indicator': {
                height: 3,
                borderTopLeftRadius: 3,
                borderTopRightRadius: 3,
              },
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                minWidth: 'auto',
                px: 2,
                py: 1.5,
                mr: 1,
                borderRadius: 1,
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                  color: theme.palette.primary.main
                },
                '&.Mui-selected': {
                  fontWeight: 600
                }
              }
            }}
          >
            {categories.map((category) => (
              <Tab
                key={category.id}
                label={category.name}
                value={category.id}
                disableRipple
              />
            ))}
          </Tabs>
        </Box>
      </Box>
      
      {/* Movies Grid with Pagination at Top */}
      <Box sx={{ position: 'relative', minHeight: 300 }}>
        {loading ? (
          <Box 
            sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: 300 
            }}
          >
            <CircularProgress />
          </Box>
        ) : filteredMovies.length > 0 ? (
          <>
            {/* Title and Top Pagination */}
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
              mb: 4 
            }}>
              <Typography 
                variant="h4" 
                component="h2" 
                sx={{ 
                  fontWeight: 700, 
                  fontSize: { xs: '1.5rem', md: '2rem' },
                  borderLeft: `4px solid ${theme.palette.primary.main}`,
                  pl: 2,
                  py: 1
                }}
              >
                {categories.find(c => c.id === selectedCategoryId)?.name || 'Todos os Filmes'}
              </Typography>

              {/* Top pagination */}
              {pageCount > 1 && (
                <Pagination 
                  count={pageCount} 
                  page={getCurrentPage()} 
                  onChange={handlePageChange}
                  color="primary"
                  size={isSmallScreen ? "small" : "medium"}
                  showFirstButton
                  showLastButton
                  sx={{
                    '& .MuiPaginationItem-root': {
                      color: 'text.primary',
                    },
                    '& .Mui-selected': {
                      fontWeight: 'bold',
                    }
                  }}
                />
              )}
            </Box>

            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="show"
            >
              <MotionGrid 
                container 
                spacing={2}
              >
                {getSortedMovies().slice((getCurrentPage() - 1) * moviesPerPage, getCurrentPage() * moviesPerPage).map((movie) => (
                  <MotionGrid 
                    item 
                    xs={6} 
                    sm={4} 
                    md={3}
                    lg={viewMode === 'grid' ? 2.4 : 4}
                    key={movie.id}
                    variants={itemVariants}
                    transition={{ duration: 0.3 }}
                  >
                    <MovieCard movie={movie} onClick={() => handleMovieSelect(movie)} />
                  </MotionGrid>
                ))}
              </MotionGrid>
            </motion.div>
            
            {/* Bottom Pagination */}
            {pageCount > 1 && (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                mt: 4 
              }}>
                <Pagination 
                  count={pageCount} 
                  page={getCurrentPage()} 
                  onChange={handlePageChange}
                  color="primary"
                  size={isSmallScreen ? "small" : "medium"}
                  showFirstButton
                  showLastButton
                  sx={{
                    '& .MuiPaginationItem-root': {
                      color: 'text.primary',
                    },
                    '& .Mui-selected': {
                      fontWeight: 'bold',
                    }
                  }}
                />
              </Box>
            )}
          </>
        ) : (
          <Box 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column',
              justifyContent: 'center', 
              alignItems: 'center', 
              height: 300,
              gap: 2
            }}
          >
            <MovieIcon sx={{ fontSize: 64, color: 'text.disabled' }} />
            <Typography variant="h6" color="text.secondary" align="center">
              {searchTerm 
                ? 'Nenhum filme encontrado com esses critérios' 
                : 'Nenhum filme disponível nesta categoria'
              }
            </Typography>
            {searchTerm && (
              <Button 
                variant="text" 
                color="primary" 
                onClick={() => setSearchTerm('')}
              >
                Limpar busca
              </Button>
            )}
          </Box>
        )}
      </Box>
      
      {/* Filter Drawer */}
      <Drawer
        anchor="right"
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        PaperProps={{
          sx: {
            width: { xs: '80%', sm: '400px' },
            backgroundColor: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(10px)',
            p: 3
          }
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" fontWeight={600}>Filtros</Typography>
          <IconButton onClick={() => setFilterDrawerOpen(false)}>
            <CloseIcon />
          </IconButton>
        </Box>
        
        <Divider sx={{ mb: 3 }} />
        
        <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>Ordenar por</Typography>
        
        <List>
          <ListItem 
            onClick={() => handleSort('alpha')}
            sx={{ 
              borderRadius: 1,
              mb: 1,
              cursor: 'pointer',
              bgcolor: sortBy === 'alpha' ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': { bgcolor: sortBy === 'alpha' ? alpha(theme.palette.primary.main, 0.15) : 'rgba(255, 255, 255, 0.08)' }
            }}
          >
            <ListItemText primary="Ordem alfabética" />
          </ListItem>
          <ListItem 
            onClick={() => handleSort('recent')}
            sx={{ 
              borderRadius: 1,
              mb: 1,
              cursor: 'pointer',
              bgcolor: sortBy === 'recent' ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': { bgcolor: sortBy === 'recent' ? alpha(theme.palette.primary.main, 0.15) : 'rgba(255, 255, 255, 0.08)' }
            }}
          >
            <ListItemText primary="Mais recentes" />
          </ListItem>
          <ListItem 
            onClick={() => handleSort('rating')}
            sx={{ 
              borderRadius: 1,
              cursor: 'pointer',
              bgcolor: sortBy === 'rating' ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': { bgcolor: sortBy === 'rating' ? alpha(theme.palette.primary.main, 0.15) : 'rgba(255, 255, 255, 0.08)' }
            }}
          >
            <ListItemText primary="Melhor avaliação" />
          </ListItem>
        </List>
        
        <Divider sx={{ my: 3 }} />
        
        <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>Categorias</Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, maxHeight: '250px', overflowY: 'auto' }}>
          {categories.map((category) => (
            <Chip
              key={category.id}
              label={category.name}
              variant={selectedCategoryId === category.id ? "filled" : "outlined"}
              color={selectedCategoryId === category.id ? "primary" : "default"}
              onClick={() => {
                setSelectedCategoryId(category.id);
                setFilterDrawerOpen(false);
              }}
              sx={{ mb: 1 }}
            />
          ))}
        </Box>
      </Drawer>
      
      {/* Search Results Modal */}
      <Modal
        open={searchModalOpen}
        onClose={closeSearchModal}
        aria-labelledby="search-results-modal"
        sx={{
          zIndex: 100000
        }}
        BackdropProps={{
          style: {
            backdropFilter: 'blur(3px)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 99999
          }
        }}
      >
        <Fade in={searchModalOpen}>
          <Box sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '90%', sm: '80%', md: '70%' },
            maxWidth: 800,
            bgcolor: 'background.paper',
            borderRadius: 3,
            boxShadow: 24,
            display: 'flex',
            flexDirection: 'column',
            maxHeight: '80vh',
            overflow: 'hidden',
            zIndex: 100001
          }}>
            <Box sx={{ 
              p: 2,
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              borderBottom: 1,
              borderColor: 'divider'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SearchIcon color="primary" />
              <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 500 }}>
                {searching ? 'Buscando...' : searchResults.length > 0 
                  ? `Resultados para "${searchTerm}"` 
                  : 'Nenhum resultado encontrado'}
              </Typography>
                {searchResults.length > 0 && (
                  <Chip 
                    label={`${searchResults.length} encontrados`} 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                )}
              </Box>
              <IconButton onClick={closeSearchModal} size="small" sx={{ color: 'text.secondary' }}>
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
            
            <Box sx={{ 
              overflow: 'auto', 
              flex: 1,
              '&::-webkit-scrollbar': {
                width: '8px',
                backgroundColor: alpha(theme.palette.common.white, 0.05),
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.3),
                }
              },
            }}>
              {searching ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4, height: '200px' }}>
                  <CircularProgress size={40} />
                </Box>
              ) : searchResults.length > 0 ? (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="show"
                >
                  <Grid container spacing={2} sx={{ p: 2 }}>
                  {searchResults.map((movie) => (
                      <Grid item xs={12} sm={6} md={4} key={movie.id}>
                        <motion.div variants={itemVariants}>
                          <Paper
                            elevation={0}
                      sx={{ 
                              p: 1.5,
                              borderRadius: 2,
                        transition: 'all 0.2s ease',
                              bgcolor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.05) : alpha(theme.palette.common.black, 0.02),
                              border: '1px solid',
                              borderColor: 'transparent',
                              cursor: 'pointer',
                              display: 'flex',
                              height: '100%',
                              overflow: 'hidden',
                        '&:hover': { 
                                bgcolor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.08) : alpha(theme.palette.common.black, 0.04),
                                borderColor: alpha(theme.palette.primary.main, 0.3),
                                transform: 'translateY(-3px)',
                                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                              }
                            }}
                            onClick={() => handleSelectSearchResult(movie.id)}
                          >
                            <Box sx={{ display: 'flex', width: '100%' }}>
                              {/* Thumbnail */}
                              <Box 
                                sx={{ 
                                  width: 80,
                                  height: 120,
                                  flexShrink: 0,
                                  borderRadius: 1,
                                  overflow: 'hidden',
                                  mr: 2,
                                  position: 'relative',
                                  bgcolor: 'action.hover'
                                }}
                              >
                                <Box
                                  component="img"
                          src={movie.thumbnail} 
                          alt={movie.name}
                                  onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                                    e.currentTarget.src = '/default-poster.jpg';
                                  }}
                                  sx={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                  }}
                                />
                                {movie.year && (
                                  <Chip
                                    label={movie.year}
                                    size="small"
                                    sx={{
                                      position: 'absolute',
                                      bottom: 4,
                                      right: 4,
                                      height: 20,
                                      fontSize: '0.625rem',
                                      fontWeight: 'bold',
                                      backgroundColor: alpha(theme.palette.common.black, 0.6),
                                      color: 'white',
                                      '& .MuiChip-label': {
                                        px: 1
                                      }
                                    }}
                                  />
                                )}
                              </Box>
                              
                              {/* Info */}
                              <Box sx={{ 
                                display: 'flex', 
                                flexDirection: 'column', 
                                width: '100%',
                                overflow: 'hidden',
                                justifyContent: 'space-between'
                              }}>
                                <Box>
                                  <Typography
                                    variant="subtitle1"
                                    sx={{
                                      fontWeight: 'medium',
                                      mb: 0.5,
                                      lineHeight: 1.2,
                                      display: '-webkit-box',
                                      WebkitLineClamp: 2,
                                      WebkitBoxOrient: 'vertical',
                                      overflow: 'hidden',
                                    }}
                                  >
                                    {movie.name}
                                  </Typography>
                                  
                                  {movie.genres && (
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                      sx={{
                                        fontSize: '0.75rem',
                                        opacity: 0.7,
                                        mb: 0.5
                                      }}
                                    >
                                      Gênero: {movie.genres}
                                    </Typography>
                                  )}
                                </Box>
                                
                                <Box sx={{ 
                                  display: 'flex', 
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  mt: 'auto'
                                }}>
                                  {movie.rating && (
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <StarIcon sx={{ fontSize: '0.875rem', color: 'gold', mr: 0.5 }} />
                                      <Typography variant="caption">{movie.rating.toFixed(1)}/10</Typography>
                                    </Box>
                                  )}
                                  
                      <IconButton 
                                    size="small"
                        color="primary"
                        sx={{
                                      width: 32,
                                      height: 32,
                                      opacity: 0.8,
                          '&:hover': {
                                        opacity: 1,
                                        transform: 'scale(1.1)'
                                      },
                                      transition: 'all 0.2s'
                                    }}
                                  >
                                    <PlayArrowIcon fontSize="small" />
                      </IconButton>
                                </Box>
                              </Box>
                            </Box>
                          </Paper>
                        </motion.div>
                      </Grid>
                    ))}
                  </Grid>
                </motion.div>
              ) : (
                <Box sx={{ 
                  p: 6, 
                  textAlign: 'center', 
                  display: 'flex', 
                  flexDirection: 'column', 
                  justifyContent: 'center', 
                  alignItems: 'center' 
                }}>
                  <MovieIcon sx={{ fontSize: 50, color: 'text.disabled', mb: 2 }} />
                  <Typography sx={{ fontWeight: 500, mb: 1 }}>Nenhum resultado encontrado</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 400, mb: 3 }}>
                    {globalSearch 
                      ? 'Não encontramos filmes correspondentes ao termo de busca.' 
                      : 'Tente outros termos de busca ou ative a pesquisa global para procurar em todas as categorias.'
                    }
                  </Typography>
                  <Button 
                    variant="outlined" 
                    size="medium" 
                    startIcon={<SearchIcon />}
                    onClick={() => setSearchTerm('')}
                  >
                    Nova busca
                  </Button>
                </Box>
              )}
            </Box>
          </Box>
        </Fade>
      </Modal>
      
      {/* Movie Details Modal */}
      <Dialog
        open={movieDetailsOpen}
        onClose={handleCloseMovieDetails}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { 
            borderRadius: 2,
            bgcolor: 'background.paper',
            backgroundImage: movieDetails?.backdrop_path 
              ? `linear-gradient(rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.92)), url(https://image.tmdb.org/t/p/w1280${movieDetails.backdrop_path})`
              : 'none',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }
        }}
      >
        {movieLoading ? (
          <Box sx={{ p: 8, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <DialogTitle sx={{ 
              display: 'flex', 
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 3
            }}>
              <Typography variant="h5" component="div" fontWeight="bold">
                {selectedMovie?.name}
              </Typography>
              <IconButton 
                onClick={handleCloseMovieDetails}
                sx={{ 
                  color: 'white',
                  bgcolor: alpha(theme.palette.common.white, 0.1),
                  '&:hover': { bgcolor: alpha(theme.palette.common.white, 0.2) }
                }}
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ p: 3 }}>
              <Grid container spacing={4}>
                <Grid item xs={12} sm={4}>
                  <Box 
                    component="img"
                    src={movieDetails?.poster_path 
                      ? `https://image.tmdb.org/t/p/w500${movieDetails.poster_path}` 
                      : selectedMovie?.cover || selectedMovie?.thumbnail
                    }
                    alt={selectedMovie?.name}
                    sx={{ 
                      width: '100%', 
                      borderRadius: 2,
                      boxShadow: '0 5px 15px rgba(0,0,0,0.5)',
                      mb: 2
                    }}
                  />
                  
                  <Button 
                    variant="contained" 
                    color="primary"
                    fullWidth
                    size="large"
                    onClick={handlePlayMovie}
                    startIcon={<PlayArrowIcon />}
                    sx={{ 
                      py: 1.5,
                      borderRadius: 2,
                      fontSize: '1rem',
                      fontWeight: 600,
                    }}
                  >
                    Assistir
                  </Button>
                </Grid>
                
                <Grid item xs={12} sm={8}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 1.5 }}>
                    {movieDetails?.vote_average > 0 && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                        <Rating 
                          value={movieDetails.vote_average / 2} 
                          precision={0.5} 
                          readOnly 
                          emptyIcon={<StarIcon style={{ color: 'rgba(255,255,255,0.3)' }} />}
                        />
                        <Typography sx={{ ml: 1, fontWeight: 'bold' }}>
                          {movieDetails.vote_average.toFixed(1)}/10
                        </Typography>
                      </Box>
                    )}

                    {/* Mostrar classificação IPTV se disponível */}
                    {!movieDetails?.vote_average && selectedMovie?.rating && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                        <Rating 
                          value={selectedMovie?.rating / 2 || 0} 
                          precision={0.5} 
                          readOnly 
                          emptyIcon={<StarIcon style={{ color: 'rgba(255,255,255,0.3)' }} />}
                        />
                        <Typography sx={{ ml: 1, fontWeight: 'bold' }}>
                          {selectedMovie?.rating.toFixed(1)}/10
                        </Typography>
                      </Box>
                    )}
                    
                    {/* Ano de lançamento (TMDB ou IPTV) */}
                    {(movieDetails?.release_date || selectedMovie?.year) && (
                      <Chip 
                        icon={<CalendarMonthIcon />} 
                        label={movieDetails?.release_date 
                          ? new Date(movieDetails.release_date).getFullYear() 
                          : selectedMovie?.year}
                        sx={{ 
                          bgcolor: alpha(theme.palette.primary.main, 0.2),
                          color: theme.palette.primary.light
                        }}
                      />
                    )}
                    
                    {/* Duração (TMDB ou IPTV) */}
                    {(movieDetails?.runtime || selectedMovie?.duration) && (
                      <Chip 
                        icon={<AccessTimeIcon />} 
                        label={movieDetails?.runtime 
                          ? `${Math.floor(movieDetails.runtime / 60)}h ${movieDetails.runtime % 60}min`
                          : selectedMovie?.duration 
                            ? `${Math.floor(selectedMovie.duration / 60)}h ${selectedMovie.duration % 60}min`
                            : "Duração desconhecida"}
                        sx={{ 
                          bgcolor: alpha(theme.palette.secondary.main, 0.2),
                          color: theme.palette.secondary.light
                        }}
                      />
                    )}
                  </Box>
                  
                  {/* Gêneros (TMDB ou IPTV) */}
                  {(movieDetails?.genres?.length > 0 || (selectedMovie?.genres && selectedMovie.genres.length > 0)) && (
                    <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {movieDetails?.genres ? (
                        movieDetails.genres.map((genre: any) => (
                          <Chip 
                            key={genre.id}
                            label={genre.name}
                            size="small"
                            sx={{ 
                              bgcolor: alpha(theme.palette.common.white, 0.1),
                              '&:hover': { bgcolor: alpha(theme.palette.common.white, 0.15) }
                            }}
                          />
                        ))
                      ) : (
                        selectedMovie?.genres?.map((genre: string, idx: number) => (
                          <Chip 
                            key={idx}
                            label={genre}
                            size="small"
                            sx={{ 
                              bgcolor: alpha(theme.palette.common.white, 0.1),
                              '&:hover': { bgcolor: alpha(theme.palette.common.white, 0.15) }
                            }}
                          />
                        ))
                      )}
                    </Box>
                  )}
                  
                  {/* Sinopse (TMDB ou IPTV) */}
                  {(movieDetails?.overview || selectedMovie?.description) && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body1" paragraph>
                        {movieDetails?.overview || selectedMovie?.description}
                      </Typography>
                    </Box>
                  )}
                  
                  <Grid container spacing={3}>
                    {/* Direção (TMDB ou IPTV) */}
                    {(movieDetails?.credits?.crew || selectedMovie?.director) && (
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                          Direção
                        </Typography>
                        <Typography variant="body2">
                          {movieDetails?.credits?.crew 
                            ? movieDetails.credits.crew
                                .filter((person: any) => person.job === 'Director')
                                .map((director: any) => director.name)
                                .slice(0, 2)
                                .join(', ') 
                            : selectedMovie?.director || 'Não disponível'}
                        </Typography>
                      </Grid>
                    )}
                    
                    {/* Elenco (TMDB ou IPTV) */}
                    {(movieDetails?.credits?.cast || selectedMovie?.actors) && (
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                          Elenco Principal
                        </Typography>
                        <Typography variant="body2">
                          {movieDetails?.credits?.cast
                            ? movieDetails.credits.cast
                                .slice(0, 4)
                                .map((actor: any) => actor.name)
                                .join(', ') 
                            : selectedMovie?.actors || 'Não disponível'}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions sx={{ px: 3, pb: 3, justifyContent: 'flex-start' }}>
              <Button 
                variant="outlined" 
                onClick={handleCloseMovieDetails}
                sx={{ borderRadius: 2, py: 1, px: 3 }}
              >
                Fechar
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Movie Player Modal */}
      <Dialog
        open={playerOpen}
        onClose={handleClosePlayer}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: { 
            bgcolor: '#000',
            borderRadius: 1,
            overflow: 'hidden',
            m: { xs: 1, sm: 2 },
            height: { xs: 'calc(100% - 16px)', sm: 'calc(100% - 32px)' }
          }
        }}
      >
        <Box sx={{ 
          width: '100%', 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          position: 'relative'
        }}>
          <Box sx={{ 
            flexGrow: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            position: 'relative',
            minHeight: { xs: '300px', sm: '400px', md: '600px' }
          }}>
            {streamUrl && (
              <VideoPlayer
                ref={playerRef}
                url={streamUrl}
                title={selectedMovie?.name}
                initialTime={(selectedMovie as MovieWithProgress)?.progress || 0}
                autoPlay={true}
                onTimeUpdate={handleTimeUpdate}
                onClose={handleClosePlayer}
                style={{ width: '100%', height: '100%' }}
              />
            )}
          </Box>
        </Box>
      </Dialog>
    </Container>
  );
};

export default MoviesPage; 
