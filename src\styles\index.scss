@use './_theme.scss' as theme;
@use './_mobile-channels.scss' as mobile;
@use './_responsive.scss' as responsive;

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: theme.$body-bg;
  color: theme.$text-color;
  line-height: 1.5;

  /* Mobile optimizations */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;

  /* Prevent zoom on input focus on iOS */
  @media screen and (max-width: 768px) {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Root container */
#root {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

/* Responsive scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;

  @media (min-width: 768px) {
    width: 10px;
    height: 10px;
  }
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

/* Hide scrollbar on mobile for cleaner look */
@media (max-width: 767px) {
  * {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  *::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font: inherit;
  color: inherit;
}

/* Ensure proper cursor behavior for interactive elements */
button,
[role="button"],
input[type="button"],
input[type="submit"],
input[type="reset"],
a,
select,
.clickable {
  cursor: pointer !important;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="url"],
textarea {
  cursor: text !important;
}

/* Restore text selection for specific elements */
input,
textarea,
[contenteditable="true"],
.selectable-text {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

input,
button,
textarea,
select {
  font: inherit;
}

a {
  color: theme.$primary-color;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// Responsive utility classes
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// Responsive spacing utilities
.mt-1 { margin-top: theme.$spacing-xs; }
.mt-2 { margin-top: theme.$spacing-sm; }
.mt-3 { margin-top: theme.$spacing-md; }
.mt-4 { margin-top: theme.$spacing-lg; }
.mt-5 { margin-top: theme.$spacing-xl; }

.mb-1 { margin-bottom: theme.$spacing-xs; }
.mb-2 { margin-bottom: theme.$spacing-sm; }
.mb-3 { margin-bottom: theme.$spacing-md; }
.mb-4 { margin-bottom: theme.$spacing-lg; }
.mb-5 { margin-bottom: theme.$spacing-xl; }

.p-1 { padding: theme.$spacing-xs; }
.p-2 { padding: theme.$spacing-sm; }
.p-3 { padding: theme.$spacing-md; }
.p-4 { padding: theme.$spacing-lg; }
.p-5 { padding: theme.$spacing-xl; }

// Responsive display utilities
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

@media (max-width: 767px) {
  .d-none-mobile { display: none !important; }
  .d-block-mobile { display: block !important; }
  .d-flex-mobile { display: flex !important; }
}

@media (min-width: 768px) {
  .d-none-desktop { display: none !important; }
  .d-block-desktop { display: block !important; }
  .d-flex-desktop { display: flex !important; }
}

// Responsive width utilities
.w-100 { width: 100%; }
.w-75 { width: 75%; }
.w-50 { width: 50%; }
.w-25 { width: 25%; }

.h-100 { height: 100%; }
.h-75 { height: 75%; }
.h-50 { height: 50%; }
.h-25 { height: 25%; }

// Responsive video player styles
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  @media (max-width: 767px) {
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
    }
  }
}

// Responsive grid layouts
.responsive-grid {
  display: grid;
  gap: 1rem;
  padding: 1rem;

  // Mobile: 2 columns
  grid-template-columns: repeat(2, 1fr);

  @media (min-width: 480px) {
    // Small tablets: 3 columns
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    padding: 1.5rem;
  }

  @media (min-width: 768px) {
    // Tablets: 4 columns
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    padding: 2rem;
  }

  @media (min-width: 1024px) {
    // Desktop: 5-6 columns
    grid-template-columns: repeat(5, 1fr);
  }

  @media (min-width: 1440px) {
    // Large desktop: 6+ columns
    grid-template-columns: repeat(6, 1fr);
  }
}

// Touch-friendly buttons
.touch-button {
  min-height: 44px;
  min-width: 44px;
  padding: 8px 16px;

  @media (max-width: 767px) {
    min-height: 48px;
    min-width: 48px;
    padding: 12px 20px;
  }
}

// Responsive typography
.responsive-text {
  &.title {
    font-size: 1.5rem;
    line-height: 1.3;

    @media (max-width: 767px) {
      font-size: 1.25rem;
    }

    @media (min-width: 1024px) {
      font-size: 1.75rem;
    }
  }

  &.subtitle {
    font-size: 1rem;
    line-height: 1.4;

    @media (max-width: 767px) {
      font-size: 0.875rem;
    }

    @media (min-width: 1024px) {
      font-size: 1.125rem;
    }
  }

  &.body {
    font-size: 0.875rem;
    line-height: 1.5;

    @media (max-width: 767px) {
      font-size: 0.8rem;
    }

    @media (min-width: 1024px) {
      font-size: 1rem;
    }
  }
}

// Offline indicator
html.offline {
  &::before {
    content: 'Offline Mode';
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: theme.$warning-color;
    color: #000;
    text-align: center;
    padding: theme.$spacing-sm;
    z-index: 9999;
  }

  body {
    padding-top: 2.5rem;
  }
}

// Loading spinner
.loading-spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: theme.$primary-color;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Error message
.error-message {
  color: theme.$danger-color;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: theme.$spacing-md;
  margin: theme.$spacing-md 0;
}

// Success message
.success-message {
  color: theme.$success-color;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  padding: theme.$spacing-md;
  margin: theme.$spacing-md 0;
}

// Ensure proper background for content page
body.content-page,
body.content-page #root,
body.content-page #root > div {
  background: transparent !important;
  overflow: visible !important;
  backdrop-filter: none !important;
}

// Estilos específicos para a página de seleção de conteúdo
#content-selection-page {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
}

// Garantir que a imagem de fundo seja visível
.content-bg::before {
  content: "" !important;
  display: block !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-image: url('/icons/openart-image_HcXPUXj4_1742859866308_raw.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  z-index: -1 !important;
}

// Remover qualquer background ou overlay que possa estar interferindo
.MuiBox-root, 
.MuiContainer-root,
.MuiGrid-root {
  background: transparent;
}

// Force background image to always be visible on content page
.content-page .MuiBox-root {
  background: transparent !important;
}

// Override any potential z-index issues
[data-page="content-selection"] {
  position: relative;
  z-index: auto;
}

// Estilização direta para a página de conteúdo
.content-page-root {
  position: relative;
  padding: 0 !important;
  margin: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
  
  // Forçar a imagem de fundo no elemento root
  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background-image: url('/icons/openart-image_HcXPUXj4_1742859866308_raw.jpg') !important;
    background-size: cover !important;
    background-position: center !important;
    z-index: -1 !important;
    opacity: 1 !important;
  }
  
  // Reset de qualquer elemento que possa estar interferindo
  & > div {
    background: transparent !important;
    position: relative !important;
    height: 100% !important;
    width: 100% !important;
    overflow: auto !important;
  }
}

// Estilização para a página de login
body.login-page,
body.login-page #root,
body.login-page #root > div {
  background: transparent !important;
  overflow: visible !important;
  backdrop-filter: none !important;
}

.login-page-root {
  position: relative;
  padding: 0 !important;
  margin: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
  
  // Forçar a imagem de fundo no elemento root
  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background-image: url('/icons/openart-image_HcXPUXj4_1742859866308_raw.jpg') !important;
    background-size: cover !important;
    background-position: center !important;
    z-index: -1 !important;
    opacity: 1 !important;
  }
  
  // Reset de qualquer elemento que possa estar interferindo
  & > div {
    background: transparent !important;
    position: relative !important;
  }
}

// Estilização específica para a página de login
.login-container {
  position: relative;
  
  &::before {
    content: "" !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-image: url('/icons/openart-image_HcXPUXj4_1742859866308_raw.jpg') !important;
    background-size: cover !important;
    background-position: center !important;
    z-index: -1 !important;
    opacity: 1 !important;
    filter: none !important;
  }
}

/* PWA specific fixes for cursor behavior */
@media (display-mode: standalone) {
  html, body {
    /* Ensure cursor works properly in PWA mode */
    cursor: default !important;
    pointer-events: auto !important;
  }
  
  /* Fix for potential PWA cursor issues */
  * {
    pointer-events: auto !important;
  }
  
  /* Ensure interactive elements work in PWA */
  button,
  [role="button"],
  a,
  input,
  select,
  textarea,
  .MuiButton-root,
  .MuiIconButton-root,
  .MuiMenuItem-root {
    pointer-events: auto !important;
    cursor: pointer !important;
  }
  
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="search"],
  input[type="url"],
  textarea {
    cursor: text !important;
  }
}

/* Additional fix for fullscreen mode */
@media (display-mode: fullscreen) {
  html, body {
    cursor: default !important;
    pointer-events: auto !important;
  }
}

/* Global cursor fixes for PWA issues */
html {
  cursor: default !important;
  pointer-events: auto !important;
}

body {
  cursor: default !important;
  pointer-events: auto !important;
}

/* Ensure all interactive elements have proper cursor */
button,
[role="button"],
input[type="button"],
input[type="submit"],
input[type="reset"],
a,
select,
.clickable,
.MuiButton-root,
.MuiIconButton-root,
.MuiMenuItem-root,
.MuiListItem-root,
.MuiCard-root,
[onClick] {
  cursor: pointer !important;
  pointer-events: auto !important;
}

/* Text input elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="url"],
textarea,
[contenteditable="true"] {
  cursor: text !important;
  pointer-events: auto !important;
}

/* Video player specific fixes */
video,
.video-js,
.plyr,
.react-player {
  cursor: default !important;
  pointer-events: auto !important;
}

/* Ensure video controls are clickable */
.video-js .vjs-control-bar,
.plyr__controls,
.react-player__controls {
  pointer-events: auto !important;
  
  button,
  [role="button"] {
    cursor: pointer !important;
    pointer-events: auto !important;
  }
} 
/*
 Additional PWA cursor fixes */
@media all and (display-mode: standalone) {
  /* Force cursor visibility in PWA mode */
  * {
    cursor: inherit !important;
  }
  
  /* Specific fixes for common elements */
  div, span, p, h1, h2, h3, h4, h5, h6 {
    cursor: default !important;
  }
  
  /* Ensure Material-UI components work properly */
  .MuiBox-root,
  .MuiContainer-root,
  .MuiGrid-root,
  .MuiPaper-root {
    cursor: default !important;
    pointer-events: auto !important;
  }
  
  /* Fix for overlay elements that might block cursor */
  .MuiBackdrop-root,
  .MuiModal-root,
  .MuiDialog-root {
    cursor: default !important;
    pointer-events: auto !important;
  }
}

/* Force cursor for all elements - last resort fix */
html * {
  cursor: inherit !important;
}

/* Ensure body always has default cursor */
body {
  cursor: default !important;
}

/* Fix for any elements that might have cursor: none */
*[style*="cursor: none"],
*[style*="cursor:none"] {
  cursor: default !important;
}

/* Prevent any CSS from hiding the cursor */
* {
  cursor: inherit !important;
}

/* Override any potential cursor hiding */
html, body, #root {
  cursor: default !important;
}

/* Fix para modais de pesquisa ficarem sempre no topo */
.MuiModal-root[aria-labelledby="search-results-modal"] {
  z-index: 100000 !important;
}

.MuiModal-root[aria-labelledby="search-results-modal"] .MuiBackdrop-root {
  z-index: 99999 !important;
}

.MuiModal-root[aria-labelledby="search-results-modal"] .MuiBox-root {
  z-index: 100001 !important;
}

/* Garantir que o AppBar fique abaixo dos modais de pesquisa */
.neko-app-bar {
  z-index: 9999 !important;
}

/* Garantir que outros elementos não interfiram */
.MuiDrawer-root {
  z-index: 9998 !important;
}

.MuiDialog-root:not([aria-labelledby="search-results-modal"]) {
  z-index: 9997 !important;
}

/* Garantir que os modais de pesquisa não fiquem embaçados */
.MuiModal-root[aria-labelledby="search-results-modal"] {
  pointer-events: auto !important;
}

.MuiModal-root[aria-labelledby="search-results-modal"] .MuiBox-root {
  pointer-events: auto !important;
  opacity: 1 !important;
  filter: none !important;
  backdrop-filter: none !important;
}

/* Garantir que o backdrop dos modais de pesquisa seja clicável */
.MuiModal-root[aria-labelledby="search-results-modal"] .MuiBackdrop-root {
  pointer-events: auto !important;
  opacity: 1 !important;
}

/* Estilos específicos para a tela inicial de login */
.enhanced-access-page {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  /* Garantir que elementos não se sobreponham */
  & > * {
    position: relative;
    z-index: auto;
  }

  /* Espaçamento adequado entre elementos */
  .welcome-banner {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    width: 100%;
    max-width: 800px; // Aumentado para 800px
    padding: 0 16px;
  }

  .auth-card-container {
    margin-top: 140px;
    width: 100%;
    max-width: 420px;
    z-index: 2;
  }

  /* Responsividade para mobile */
  @media (max-width: 767px) {
    .welcome-banner {
      top: 15px;
      max-width: 95%;
      padding: 0 8px;
    }

    .auth-card-container {
      margin-top: 120px;
      max-width: 340px;
      padding: 0 16px;
    }
  }
}

/* Otimizações para o componente de partículas */
#particles-auth {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  pointer-events: none !important;
}

/* Garantir que elementos interativos funcionem corretamente */
.MuiButton-root,
.MuiIconButton-root,
.MuiTextField-root,
.MuiCheckbox-root {
  pointer-events: auto !important;
  z-index: 10 !important;
}

/* Melhorar a visibilidade de elementos sobre o fundo */
.MuiCard-root {
  backdrop-filter: blur(20px) !important;
  background: rgba(26, 31, 53, 0.95) !important;
  border: 1px solid rgba(14, 165, 233, 0.15) !important;
}

/* Garantir que textos sejam legíveis - melhor contraste */
.MuiTypography-root {
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Melhorar contraste específico do banner */
.welcome-banner-container .MuiTypography-root {
  color: #ffffff !important;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.5),
    0 1px 2px rgba(0, 0, 0, 0.7) !important;
  font-weight: 700 !important;
}

/* Estilos específicos para o banner promocional - melhor contraste */
.welcome-banner-container {
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(14, 165, 233, 0.3) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2) !important,
    0 0 0 1px rgba(14, 165, 233, 0.1) !important,
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  background: rgba(30, 41, 59, 0.95) !important; // Fundo mais opaco
}

/* Garantir que botões sejam clicáveis */
.welcome-banner-container .MuiLink-root {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 10 !important;
}

/* Garantir que o banner seja responsivo */
@media (max-width: 800px) {
  .welcome-banner-container {
    max-width: 95% !important;
    padding: 16px 20px !important;
  }
}

@media (max-width: 600px) {
  .welcome-banner-container {
    flex-direction: column !important;
    text-align: center !important;
    gap: 12px !important;
    padding: 12px 16px !important;
    background: rgba(30, 41, 59, 0.98) !important; // Ainda mais opaco no mobile
  }

  .welcome-banner-content {
    flex-direction: column !important;
    align-items: center !important;
    gap: 8px !important;
  }

  .welcome-banner-container .MuiTypography-root {
    font-size: 1rem !important;
    line-height: 1.3 !important;
  }
}

/* Garantir legibilidade em todos os temas */
.welcome-banner-container {
  /* Overlay adicional para garantir contraste */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.8) 0%,
      rgba(30, 41, 59, 0.8) 100%);
    border-radius: 20px;
    z-index: 0;
  }

  /* Garantir que o conteúdo fique acima do overlay */
  & > * {
    position: relative;
    z-index: 1;
  }
}

