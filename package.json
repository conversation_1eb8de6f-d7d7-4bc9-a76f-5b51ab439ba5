{"name": "iptv-player", "private": true, "version": "0.1.0", "description": "Neko TV - Seu player IPTV favorito", "author": {"name": "Neko TV Team", "email": "<EMAIL>"}, "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "npm run prebuild && vite build", "build:vercel": "npm run prebuild:vercel && vite build", "vercel-build": "npm run prebuild:vercel && vite build", "prebuild": "node scripts/prebuild.cjs", "prebuild:vercel": "node scripts/prebuild.cjs || echo 'Prebuild failed but continuing...'", "build:electron": "vite build", "create-app-pronto": "node scripts/copy-production-files.cjs", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "electron": "wait-on tcp:5173 && cross-env NODE_ENV=development electron .", "electron:dev": "concurrently -k \"npm run dev\" \"npm run electron\"", "clean": "node scripts/clean.cjs", "electron:build": "npm run clean && npm run build:electron && electron-builder --config electron-builder.cjs", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@chakra-ui/react": "^3.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.11", "@mui/lab": "^6.0.0-beta.31", "@mui/material": "^6.4.11", "@react-spring/web": "^10.0.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/postcss": "^4.1.11", "@tsparticles/react": "^3.0.0", "@types/styled-components": "^5.1.34", "@videojs/http-streaming": "^3.17.0", "axios": "^1.8.4", "electron-store": "^8.1.0", "framer-motion": "^12.23.9", "hls.js": "^1.5.20", "idb": "^7.1.1", "imgur": "^2.4.3", "moment": "^2.30.1", "node-machine-id": "^1.1.12", "plyr": "^3.7.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.15.1", "react-player": "^2.16.0", "react-router-dom": "^6.29.0", "react-tilt": "^1.0.2", "react-toastify": "^11.0.5", "react-zoom-pan-pinch": "^3.7.0", "shaka-player": "^4.13.4", "styled-components": "^6.1.15", "tsparticles": "^3.8.1", "tsparticles-slim": "^2.12.0", "video.js": "^8.21.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/node": "^20.4.5", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/testing-library__jest-dom": "^5.14.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "@vitest/coverage-v8": "^0.33.0", "@vitest/ui": "^0.33.0", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.6.4", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "fs-extra": "^11.3.0", "jsdom": "^22.1.0", "postcss": "^8.5.6", "sass": "^1.85.0", "tailwindcss": "^4.1.11", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-pwa": "^1.0.0", "vitest": "^0.33.0", "wait-on": "^7.2.0", "workbox-window": "^7.3.0"}, "build": {"appId": "com.iptv.player", "productName": "IPTV Player", "directories": {"output": "dist_electron"}, "files": ["dist/**/*", "electron/**/*"], "win": {"target": ["nsis"]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "IPTV Player"}, "mac": {"target": "dmg", "category": "public.app-category.video"}, "linux": {"target": ["AppImage", "deb"], "category": "Video"}}}