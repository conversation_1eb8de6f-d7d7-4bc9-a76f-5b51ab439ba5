import React from 'react'
import { NavLink } from 'react-router-dom'
import { useConnectionStore } from '../stores/connectionStore'
import { 
  FiHome, 
  FiTv, 
  FiSettings,
  FiPlus
} from 'react-icons/fi'

const Sidebar: React.FC = () => {
  const { openAuthModal } = useConnectionStore()

  const navItems = [
    { path: '/', icon: FiHome, label: 'Dashboard' },
    { path: '/channels', icon: FiTv, label: 'Live Channels' },
    { path: '/settings', icon: FiSettings, label: 'Settings' },
  ]

  return (
    <div className="w-64 bg-slate-800 border-r border-slate-700">
      <div className="p-6">
        <h1 className="text-xl font-bold text-white">
          IPTV FUCKING PLAYER PRO
        </h1>
      </div>
      
      <nav className="mt-6">
        {navItems.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                isActive
                  ? 'bg-sky-600 text-white border-r-2 border-sky-400'
                  : 'text-slate-300 hover:bg-slate-700 hover:text-white'
              }`
            }
          >
            <item.icon className="mr-3 h-5 w-5" />
            {item.label}
          </NavLink>
        ))}
        
        <button
          onClick={() => {
            console.log('🔘 Sidebar Add Connection button clicked')
            openAuthModal()
          }}
          className="flex items-center w-full px-6 py-3 text-sm font-medium text-slate-300 hover:bg-slate-700 hover:text-white transition-colors duration-200"
        >
          <FiPlus className="mr-3 h-5 w-5" />
          Add Connection
        </button>
      </nav>
    </div>
  )
}

export default Sidebar;