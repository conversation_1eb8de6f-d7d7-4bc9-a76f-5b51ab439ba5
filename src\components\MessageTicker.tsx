import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Box, Typography, Link, useTheme, Fade } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import LaunchIcon from '@mui/icons-material/Launch';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import { handleExternalLinkClick } from '../utils/openExternal';

// Animação de scroll horizontal otimizada
const scrollAnimation = keyframes`
  0% {
    transform: translate3d(100%, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
`;

// Animação de brilho otimizada
const shimmerAnimation = keyframes`
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
`;

// Animação de pulsação otimizada
const pulseAnimation = keyframes`
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
`;

// Container principal do ticker otimizado
const TickerContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '70px', // Aumentado para acomodar links maiores
  zIndex: 10,
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9))'
    : 'linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.9), rgba(226, 232, 240, 0.9))',
  backdropFilter: 'blur(12px)',
  borderTop: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)'}`,
  borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)'}`,
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 16px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2)'
    : '0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)',

  // Otimização para performance
  willChange: 'transform',
  contain: 'layout style paint',

  // Efeito de brilho sutil otimizado
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.2)'}, transparent)`,
    animation: `${shimmerAnimation} 4s infinite`,
    willChange: 'transform',
  },

  // Efeito de pulsação suave otimizado
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',
    animation: `${pulseAnimation} 6s infinite`,
    willChange: 'opacity',
  },

  [theme.breakpoints.down('sm')]: {
    height: '68px', // Ajustado para mobile
    padding: theme.spacing(0.5, 1),
  },
}));

// Área de conteúdo scrollável otimizada
const ScrollingContent = styled(Box)<{ duration: number }>(({ theme, duration }) => ({
  display: 'flex',
  alignItems: 'center',
  whiteSpace: 'nowrap',
  animation: `${scrollAnimation} ${duration}s linear infinite`,
  paddingLeft: '100%',
  paddingRight: theme.spacing(4), // Espaço adicional após o conteúdo
  willChange: 'transform',
  backfaceVisibility: 'hidden',
  perspective: 1000,

  '&:hover': {
    animationPlayState: 'paused',
  },
}));

// Animação do ícone otimizada
const iconPulseAnimation = keyframes`
  0%, 100% {
    opacity: 0.8;
    transform: scale3d(1, 1, 1);
  }
  50% {
    opacity: 1;
    transform: scale3d(1.05, 1.05, 1);
  }
`;

// Ícone de notificação sutil otimizado
const NotificationIcon = styled(NotificationsActiveIcon)(({ theme }) => ({
  color: theme.palette.mode === 'dark' ? '#ffffff' : '#1f2937',
  marginRight: theme.spacing(1.5),
  fontSize: '1.4rem',
  animation: `${iconPulseAnimation} 2s infinite`,
  filter: theme.palette.mode === 'dark'
    ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5))'
    : 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))',
  position: 'relative',
  zIndex: 2,
  willChange: 'transform, opacity',
}));



// Link estilizado otimizado para melhor clicabilidade
const StyledLink = styled(Link)(({ theme }) => ({
  zIndex: 1000, // Z-index muito alto para garantir clicabilidade
  position: 'relative',
  color: theme.palette.mode === 'dark' ? '#ffffff' : '#1f2937',
  textDecoration: 'none !important',
  fontWeight: 700,
  padding: theme.spacing(1.4, 2.5), // Área de clique ainda maior para mobile
  borderRadius: 8,
  background: theme.palette.mode === 'dark'
    ? 'rgba(59, 130, 246, 1)' // Totalmente opaco para melhor visibilidade
    : 'rgba(59, 130, 246, 0.9)',
  border: `2px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.5)'}`,
  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)', // Transição mais suave
  display: 'inline-flex',
  alignItems: 'center',
  gap: theme.spacing(0.8),
  fontSize: '1rem',
  textShadow: theme.palette.mode === 'dark'
    ? '0 1px 3px rgba(0, 0, 0, 0.7)'
    : '0 1px 3px rgba(255, 255, 255, 0.9)',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 12px rgba(0, 0, 0, 0.5)'
    : '0 4px 12px rgba(0, 0, 0, 0.2)',
  cursor: 'pointer !important',
  userSelect: 'none',

  // Garantir que o link seja sempre clicável
  pointerEvents: 'auto !important',
  touchAction: 'manipulation',

  // Melhor contraste visual
  minHeight: 36,
  minWidth: 100,
  justifyContent: 'center',

  '&:hover, &:focus': {
    background: theme.palette.mode === 'dark'
      ? 'rgba(59, 130, 246, 1)'
      : 'rgba(59, 130, 246, 0.9)',
    border: `2px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.4)'}`,
    transform: 'translateY(-2px) scale(1.05)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 6px 16px rgba(0, 0, 0, 0.5)'
      : '0 6px 16px rgba(0, 0, 0, 0.2)',
    textDecoration: 'none !important',
    outline: 'none',
  },

  '&:active': {
    transform: 'translateY(-1px) scale(1.02)',
    transition: 'all 0.1s ease',
  }
}))


// Interface para configuração de mensagens
export interface MessageConfig {
  id: string;
  text: string;
  link?: {
    text: string;
    url: string;
    target?: '_blank' | '_self';
  };
  suffix?: string;
  duration?: number;
  priority?: number;
  enabled?: boolean;
}

export interface TickerConfig {
  enabled: boolean;
  messages: MessageConfig[];
  defaultDuration?: number;
  allowClose?: boolean;
}

interface MessageTickerProps {
  config?: TickerConfig;
  className?: string;
}

const MessageTicker: React.FC<MessageTickerProps> = ({
  config,
  className
}) => {
  const theme = useTheme();
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Configuração padrão otimizada
  const defaultConfig: TickerConfig = useMemo(() => ({
    enabled: true,
    messages: [
      {
        id: 'default',
        text: '🎉 Bem-vindo ao Neko TV! Confira nosso ',
        link: {
          text: 'canal oficial',
          url: 'https://nekotv.vercel.app',
          target: '_blank'
        },
        suffix: ' para mais novidades!',
        duration: 15,
        priority: 1,
        enabled: true
      }
    ],
    defaultDuration: 15
  }), []);

  // Memoizar configuração ativa e mensagens habilitadas
  const activeConfig = useMemo(() => config || defaultConfig, [config, defaultConfig]);
  const enabledMessages = useMemo(() =>
    activeConfig.messages.filter(msg => msg.enabled !== false),
    [activeConfig.messages]
  );

  // Otimizar o efeito de rotação de mensagens
  useEffect(() => {
    if (enabledMessages.length > 1) {
      const currentMessage = enabledMessages[currentMessageIndex];
      const duration = (currentMessage?.duration || activeConfig.defaultDuration || 15) * 1000;

      intervalRef.current = setTimeout(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % enabledMessages.length);
      }, duration);
    }

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
    };
  }, [currentMessageIndex, enabledMessages, activeConfig.defaultDuration]);

  // Otimizar handler de clique com useCallback
  const handleLinkClick = useCallback((url: string, target: string = '_blank') => {
    try {
      handleExternalLinkClick(url, target);
    } catch (error) {
      console.error('Erro ao abrir link:', error);
      // Fallback para window.open
      window.open(url, target, 'noopener,noreferrer');
    }
  }, []);

  // Early return otimizado
  if (!activeConfig.enabled || !isVisible || enabledMessages.length === 0) {
    return null;
  }

  const currentMessage = enabledMessages[currentMessageIndex];
  const scrollDuration = currentMessage?.duration || activeConfig.defaultDuration || 15;

  // Memoizar URL limpa
  const cleanUrl = useMemo(() =>
    currentMessage.link?.url.replace(/[`\s]/g, '') || '',
    [currentMessage.link?.url]
  );

  return (
    <Fade in={isVisible} timeout={300}>
      <TickerContainer className={className}>
        <ScrollingContent duration={scrollDuration}>
          <NotificationIcon />
          <Typography
            variant="body1"
            component="span"
            sx={{
              color: theme => theme.palette.mode === 'dark' ? '#ffffff' : '#1f2937',
              fontWeight: 700,
              fontSize: { xs: '1.05rem', sm: '1.15rem' },
              display: 'flex',
              alignItems: 'center',
              gap: 0.8,
              padding: { xs: '0.5rem 0', sm: '0.5rem 0' },
              textShadow: theme => theme.palette.mode === 'dark'
                ? '0 1px 2px rgba(0, 0, 0, 0.5)'
                : '0 1px 2px rgba(255, 255, 255, 0.8)',
              position: 'relative',
              zIndex: 2,
            }}
          >
            {currentMessage.text}
            {currentMessage.link && (
              <StyledLink
                href={cleanUrl}
                target={currentMessage.link.target || '_blank'}
                rel="noopener noreferrer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleLinkClick(cleanUrl, currentMessage.link!.target);
                }}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                sx={{
                  mx: 1.5,
                  my: 0.5,
                  display: 'inline-flex !important',
                  alignItems: 'center',
                  fontWeight: 'bold',
                  cursor: 'pointer !important',
                  pointerEvents: 'auto !important',
                  zIndex: 1001,
                  position: 'relative',
                  fontSize: { xs: '1.05rem', sm: '1.15rem' },
                  '&:hover': {
                    textDecoration: 'none !important'
                  }
                }}
              >
                {currentMessage.link.text}
                <LaunchIcon sx={{ fontSize: { xs: 18, sm: 20 }, ml: 0.7 }} />
              </StyledLink>
            )}
            {currentMessage.suffix}
          </Typography>
        </ScrollingContent>
      </TickerContainer>
    </Fade>
  );
};

export default MessageTicker;
