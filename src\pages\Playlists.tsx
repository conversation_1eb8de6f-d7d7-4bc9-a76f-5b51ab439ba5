import React, { useState, useEffect } from 'react';
import { playlistService, Playlist } from '../services/playlistService';

const Playlists: React.FC = () => {
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPlaylists();
  }, []);

  const loadPlaylists = async () => {
    try {
      const data = await playlistService.getPlaylists();
      setPlaylists(data);
    } catch (err) {
      setError('Failed to load playlists');
      console.error('Error loading playlists:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await playlistService.deletePlaylist(id);
      await loadPlaylists();
    } catch (err) {
      setError('Failed to delete playlist');
      console.error('Error deleting playlist:', err);
    }
  };

  if (loading) {
    return <div className="loading">Loading playlists...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="playlists-container">
      <h1>My Playlists</h1>
      
      {playlists.length === 0 ? (
        <div className="no-playlists">
          <p>No playlists found. Add a playlist from the home page.</p>
        </div>
      ) : (
        <div className="playlists-grid">
          {playlists.map(playlist => (
            <div key={playlist.id} className="playlist-card">
              <div className="playlist-info">
                <h3>{playlist.name}</h3>
                <p>{playlist.items.length} items</p>
                <p className="last-updated">
                  Last updated: {new Date(playlist.lastUpdated).toLocaleDateString()}
                </p>
              </div>
              <div className="playlist-actions">
                <button 
                  className="delete-button"
                  onClick={() => playlist.id && handleDelete(playlist.id)}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      <style>{`
        .playlists-container {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }

        .playlists-container h1 {
          margin-bottom: 30px;
          color: #333;
        }

        .playlists-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 20px;
        }

        .playlist-card {
          background: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .playlist-info h3 {
          margin: 0 0 10px 0;
          color: #333;
        }

        .playlist-info p {
          margin: 5px 0;
          color: #666;
        }

        .last-updated {
          font-size: 0.9em;
          color: #999;
        }

        .playlist-actions {
          margin-top: 20px;
          display: flex;
          justify-content: flex-end;
        }

        .delete-button {
          padding: 8px 16px;
          background-color: #dc3545;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .delete-button:hover {
          background-color: #c82333;
        }

        .no-playlists {
          text-align: center;
          padding: 40px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .no-playlists p {
          color: #666;
          font-size: 1.1em;
        }

        .loading {
          text-align: center;
          padding: 40px;
          color: #666;
        }

        .error {
          text-align: center;
          padding: 20px;
          color: #dc3545;
          background-color: #f8d7da;
          border-radius: 4px;
          margin: 20px;
        }
      `}</style>
    </div>
  );
};

export default Playlists; 