# Design Document

## Overview

O IPTV FUCKING PLAYER PRO será um aplicativo Electron moderno que combina tecnologias web (React + TypeScript) com funcionalidades nativas do desktop. O design foca em uma arquitetura limpa, performance otimizada e experiência do usuário excepcional.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[React UI Layer] --> API[API Service Layer]
    UI --> DB[Database Layer]
    UI --> Player[Video Player Layer]
    
    API --> IPTV[IPTV Endpoints]
    API --> Cache[Cache Manager]
    
    DB --> SQLite[(SQLite Database)]
    Cache --> DB
    
    Player --> HLS[HLS.js]
    Player --> VideoJS[Video.js]
    
    IPTV --> Auth[Authentication]
    IPTV --> Categories[Categories API]
    IPTV --> Streams[Streams API]
    IPTV --> EPG[EPG XML API]
```

### Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + Framer Motion (animações)
- **Desktop**: Electron 28+
- **Database**: SQLite (via better-sqlite3)
- **Video Player**: Video.js + HLS.js
- **State Management**: React Context + Zustand
- **Internationalization**: react-i18next
- **Security**: crypto-js (para criptografia de senhas)
- **UI Components**: Headless UI + Heroicons

## Components and Interfaces

### Core Components

#### 1. Authentication Service
```typescript
interface AuthService {
  authenticate(url: string, username: string, password: string): Promise<AuthResult>
  validateConnection(connectionId: string): Promise<boolean>
  getUserInfo(connectionId: string): Promise<UserInfo>
  getServerInfo(connectionId: string): Promise<ServerInfo>
  testConnection(connection: Connection): Promise<ConnectionTestResult>
}

interface AuthResult {
  success: boolean
  userInfo?: UserInfo
  serverInfo?: ServerInfo
  error?: string
  connectionId?: string
}

interface ConnectionTestResult {
  success: boolean
  latency?: number
  error?: string
  serverInfo?: ServerInfo
}
```

#### 2. IPTV API Service
```typescript
interface IPTVService {
  // Categories
  getLiveCategories(): Promise<Category[]>
  getMovieCategories(): Promise<Category[]>
  getSeriesCategories(): Promise<Category[]>
  
  // Streams
  getLiveStreams(categoryId: string): Promise<Stream[]>
  getMovieStreams(categoryId: string): Promise<Stream[]>
  getSeriesStreams(categoryId: string): Promise<Stream[]>
  
  // Series specific
  getSeriesInfo(seriesId: string): Promise<SeriesInfo>
  getSeriesEpisodes(seriesId: string, season: number): Promise<Episode[]>
  
  // EPG
  getEPGData(): Promise<EPGChannel[]>
  
  // Stream URLs
  getStreamUrl(streamId: string, type: StreamType): string
}
```

#### 3. Database Service
```typescript
interface DatabaseService {
  // Connection management
  saveConnection(connection: Connection): Promise<string>
  getConnection(id: string): Promise<Connection | null>
  getAllConnections(): Promise<Connection[]>
  updateConnection(id: string, updates: Partial<Connection>): Promise<void>
  deleteConnection(id: string): Promise<void>
  setActiveConnection(id: string): Promise<void>
  getActiveConnection(): Promise<Connection | null>
  
  // Cache management
  setCache<T>(key: string, data: T, ttl?: number): Promise<void>
  getCache<T>(key: string): Promise<T | null>
  clearCache(pattern?: string): Promise<void>
  
  // User preferences
  setPreference(key: string, value: any): Promise<void>
  getPreference(key: string): Promise<any>
}
```

#### 4. Video Player Component
```typescript
interface VideoPlayerProps {
  src: string
  type: 'live' | 'movie' | 'series'
  title: string
  onProgress?: (progress: number) => void
  onEnded?: () => void
  autoplay?: boolean
}
```

### UI Components Architecture

#### Layout Structure
```
App
├── Authentication Modal (Conditional)
├── Connection Tabs Bar (When multiple connections)
├── Sidebar (Collapsible)
│   ├── Navigation Menu
│   └── Language Selector
├── Main Content Area
│   ├── Header (Search, User Info, Connection Status)
│   ├── Content Router
│   │   ├── Dashboard
│   │   ├── Live Channels
│   │   ├── Movies
│   │   ├── Series
│   │   ├── Settings
│   │   └── Connection Manager
│   └── Player Modal/Fullscreen
└── Toast Notifications
```

#### Sidebar Menu Items
- 🏠 Dashboard
- 📺 Canais ao Vivo
- 🎬 Filmes
- 📺 Séries
- 🔗 Gerenciar Conexões
- ⚙️ Configurações

#### New UI Components

##### Authentication Modal
```typescript
interface AuthModalProps {
  isOpen: boolean
  onClose?: () => void
  onSuccess: (connection: Connection) => void
  editConnection?: Connection
  isRequired?: boolean
}
```

##### Connection Tabs
```typescript
interface ConnectionTabsProps {
  connections: Connection[]
  activeConnectionId: string
  onTabChange: (connectionId: string) => void
  onTabClose: (connectionId: string) => void
  onAddConnection: () => void
}

interface ConnectionTab {
  id: string
  name: string
  isActive: boolean
  hasError?: boolean
}
```

##### Connection Manager
```typescript
interface ConnectionManagerProps {
  connections: Connection[]
  onEdit: (connection: Connection) => void
  onDelete: (connectionId: string) => void
  onTest: (connectionId: string) => void
  onToggleFavorite: (connectionId: string) => void
  onDuplicate: (connection: Connection) => void
  onImport: (file: File) => void
  onExport: (connectionIds: string[]) => void
}

interface ConnectionCard {
  connection: Connection
  status: 'testing' | 'online' | 'offline' | 'error'
  latency?: number
  lastTested?: number
}
```

## Authentication Flow Design

### Modal Authentication System

O sistema de autenticação será baseado em um modal obrigatório que aparece quando:
1. O aplicativo é iniciado pela primeira vez
2. Não há conexões salvas no banco de dados
3. O usuário clica em "Adicionar Nova Conexão"
4. Uma conexão existente precisa ser editada

#### Modal Design Specifications

```typescript
interface AuthModalState {
  step: 'credentials' | 'testing' | 'success' | 'error'
  formData: {
    name: string
    url: string
    username: string
    password: string
  }
  errors: Record<string, string>
  isLoading: boolean
  testResult?: ConnectionTestResult
}
```

#### Modal Visual Design
- **Background**: Semi-transparente com blur effect
- **Card**: Glassmorphism design com bordas arredondadas
- **Animações**: Fade in/out, scale, e slide transitions
- **Cores**: Gradiente sutil com accent colors
- **Responsivo**: Adaptável para diferentes tamanhos de tela

### Multi-Connection Tab System

#### Tab Management
```typescript
interface TabManager {
  activeTabs: ConnectionTab[]
  activeTabId: string
  maxTabs: number // Limite de 8 abas
  
  addTab(connection: Connection): void
  removeTab(tabId: string): void
  switchTab(tabId: string): void
  reorderTabs(fromIndex: number, toIndex: number): void
  closeAllTabs(): void
  duplicateTab(tabId: string): void
}

interface ConnectionTab {
  id: string
  connectionId: string
  name: string
  isActive: boolean
  hasError: boolean
  isLoading: boolean
  favicon?: string
  lastActivity: number
}
```

#### Tab Visual Design
- **Estilo Chrome**: Tabs trapezoidais com close button
- **Indicadores**: Status de conexão, loading, erro
- **Drag & Drop**: Reordenação de abas
- **Context Menu**: Opções de fechar, duplicar, renomear
- **Overflow**: Scroll horizontal quando muitas abas

## Connection Management System

### Database Schema Enhancements

```sql
-- Enhanced connections table
CREATE TABLE connections (
  id TEXT PRIMARY KEY,
  url TEXT NOT NULL,
  username TEXT NOT NULL,
  password TEXT NOT NULL, -- Encrypted
  name TEXT NOT NULL,
  is_favorite INTEGER DEFAULT 0,
  is_active INTEGER DEFAULT 0,
  server_info TEXT, -- JSON serialized ServerInfo
  connection_status TEXT DEFAULT 'unknown', -- 'online', 'offline', 'error'
  last_tested INTEGER DEFAULT 0,
  last_latency INTEGER DEFAULT 0,
  last_used INTEGER DEFAULT 0,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Connection groups for organization
CREATE TABLE connection_groups (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT DEFAULT '#0ea5e9',
  created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Many-to-many relationship
CREATE TABLE connection_group_members (
  connection_id TEXT NOT NULL,
  group_id TEXT NOT NULL,
  PRIMARY KEY (connection_id, group_id),
  FOREIGN KEY (connection_id) REFERENCES connections (id) ON DELETE CASCADE,
  FOREIGN KEY (group_id) REFERENCES connection_groups (id) ON DELETE CASCADE
);

-- Connection usage statistics
CREATE TABLE connection_stats (
  id TEXT PRIMARY KEY,
  connection_id TEXT NOT NULL,
  total_usage_time INTEGER DEFAULT 0,
  last_stream_watched TEXT,
  favorite_categories TEXT, -- JSON array
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  FOREIGN KEY (connection_id) REFERENCES connections (id) ON DELETE CASCADE
);
```

### Security Implementation

#### Password Encryption
```typescript
import CryptoJS from 'crypto-js'

class SecurityService {
  private static readonly SECRET_KEY = 'iptv-player-secret-key'
  
  static encryptPassword(password: string): string {
    return CryptoJS.AES.encrypt(password, this.SECRET_KEY).toString()
  }
  
  static decryptPassword(encryptedPassword: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedPassword, this.SECRET_KEY)
    return bytes.toString(CryptoJS.enc.Utf8)
  }
}
```

### Connection Testing System

#### Real-time Connection Monitoring
```typescript
interface ConnectionMonitor {
  testConnection(connection: Connection): Promise<ConnectionTestResult>
  monitorConnections(connectionIds: string[]): void
  stopMonitoring(): void
  getConnectionStatus(connectionId: string): ConnectionStatus
}

interface ConnectionStatus {
  status: 'online' | 'offline' | 'testing' | 'error'
  latency?: number
  lastTested: number
  error?: string
  serverInfo?: ServerInfo
}
```

## Data Models

### Core Entities

```typescript
interface Connection {
  id: string
  url: string
  username: string
  password: string
  name: string
  lastUsed: number
  isFavorite: boolean
  isActive: boolean
  serverInfo?: ServerInfo
  createdAt: number
  updatedAt: number
}

interface ServerInfo {
  serverProtocol: string
  serverPort: string
  httpsPort: string
  rtmpPort: string
  timezone: string
  timestampNow: number
  timeNow: string
}

interface Category {
  id: string
  name: string
  type: 'live' | 'movie' | 'series'
}

interface Stream {
  id: string
  name: string
  thumbnail?: string
  categoryId: string
  type: 'live' | 'movie' | 'series'
  url?: string
  description?: string
  rating?: number
  year?: string
  genres?: string[]
  duration?: number
}

interface SeriesInfo extends Stream {
  seasons: Season[]
  episodeCount: number
}

interface Season {
  id: number
  name: string
  episodes: Episode[]
}

interface Episode {
  id: string
  title: string
  season: number
  episode: number
  thumbnail?: string
  description?: string
  duration?: number
  url: string
}

interface EPGChannel {
  id: string
  name: string
  programs: EPGProgram[]
}

interface EPGProgram {
  title: string
  description: string
  startTime: string
  endTime: string
  isLive?: boolean
}
```

### Database Schema

```sql
-- Connections table
CREATE TABLE connections (
  id TEXT PRIMARY KEY,
  url TEXT NOT NULL,
  username TEXT NOT NULL,
  password TEXT NOT NULL,
  name TEXT NOT NULL,
  is_favorite INTEGER DEFAULT 0,
  is_active INTEGER DEFAULT 0,
  server_info TEXT,
  last_used INTEGER DEFAULT 0,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Active sessions table (for tab management)
CREATE TABLE active_sessions (
  id TEXT PRIMARY KEY,
  connection_id TEXT NOT NULL,
  tab_order INTEGER DEFAULT 0,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  FOREIGN KEY (connection_id) REFERENCES connections (id) ON DELETE CASCADE
);

-- Cache table
CREATE TABLE cache (
  key TEXT PRIMARY KEY,
  data TEXT NOT NULL,
  expires_at INTEGER,
  created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- User preferences
CREATE TABLE preferences (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Watch history
CREATE TABLE watch_history (
  id TEXT PRIMARY KEY,
  stream_id TEXT NOT NULL,
  stream_type TEXT NOT NULL,
  progress REAL DEFAULT 0,
  duration REAL DEFAULT 0,
  last_watched INTEGER DEFAULT (strftime('%s', 'now'))
);
```

## Error Handling

### Error Types
```typescript
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  STREAM_ERROR = 'STREAM_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

interface AppError {
  type: ErrorType
  message: string
  details?: any
  timestamp: number
}
```

### Error Handling Strategy
1. **Network Errors**: Retry com backoff exponencial
2. **Authentication Errors**: Redirect para login
3. **Stream Errors**: Fallback para qualidade inferior
4. **Database Errors**: Graceful degradation
5. **Validation Errors**: User feedback imediato

## Testing Strategy

### Test Pyramid

#### Unit Tests (70%)
- Services (AuthService, IPTVService, DatabaseService)
- Utilities e helpers
- React hooks customizados
- Data transformations

#### Integration Tests (20%)
- API endpoints integration
- Database operations
- Component integration
- Player functionality

#### E2E Tests (10%)
- Login flow completo
- Navigation entre seções
- Video playback
- Settings management

### Test Tools
- **Unit**: Jest + React Testing Library
- **Integration**: Jest + Supertest
- **E2E**: Playwright
- **Coverage**: Istanbul

### Test Scenarios
1. **Authentication Flow**
   - Valid credentials → Success
   - Invalid credentials → Error message
   - Network failure → Retry mechanism

2. **Content Loading**
   - Categories load correctly
   - Streams load with proper data
   - EPG data parsing

3. **Video Playback**
   - HLS streams play correctly
   - Player controls work
   - Fullscreen functionality

4. **Database Operations**
   - Connection persistence
   - Cache management
   - Preferences storage

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**
   - Route-based code splitting
   - Image lazy loading
   - Virtual scrolling para listas grandes

2. **Caching Strategy**
   - Categories: 24h TTL
   - Streams: 6h TTL
   - EPG: 1h TTL
   - Images: Persistent cache

3. **Memory Management**
   - Cleanup de event listeners
   - Dispose de video players
   - Cache size limits

4. **Network Optimization**
   - Request debouncing
   - Concurrent request limits
   - Retry with exponential backoff

## Security Considerations

### Data Protection
1. **Credentials Storage**: Encrypted no SQLite
2. **API Calls**: HTTPS only
3. **Input Validation**: Sanitização de todas as entradas
4. **XSS Prevention**: Content Security Policy

### Electron Security
1. **Context Isolation**: Enabled
2. **Node Integration**: Disabled no renderer
3. **Remote Module**: Disabled
4. **Secure Defaults**: All security features enabled

## Internationalization

### Supported Languages
- Português (pt-BR)
- English (en-US)
- Español (es-ES)

### Implementation
```typescript
// Language files structure
/src/locales/
  ├── pt-BR/
  │   ├── common.json
  │   ├── navigation.json
  │   └── player.json
  ├── en-US/
  │   ├── common.json
  │   ├── navigation.json
  │   └── player.json
  └── es-ES/
      ├── common.json
      ├── navigation.json
      └── player.json
```

### Key Translation Areas
- Navigation menu
- Error messages
- Player controls
- Settings interface
- Content metadata

## State Management Architecture

### Global State Structure
```typescript
interface AppState {
  // Authentication & Connections
  connections: Connection[]
  activeConnectionId: string | null
  activeTabs: ConnectionTab[]
  isAuthModalOpen: boolean
  
  // UI State
  sidebarCollapsed: boolean
  currentRoute: string
  theme: 'dark' | 'light'
  language: 'pt-BR' | 'en-US' | 'es-ES'
  
  // Content State
  categories: Record<string, Category[]> // keyed by connectionId
  streams: Record<string, Stream[]> // keyed by categoryId
  currentStream: Stream | null
  
  // Loading States
  isLoading: Record<string, boolean>
  errors: Record<string, string>
}
```

### State Management Flow
```mermaid
graph TB
    UI[UI Components] --> Actions[Actions]
    Actions --> Store[Zustand Store]
    Store --> Services[Services Layer]
    Services --> API[IPTV API]
    Services --> DB[(Database)]
    Store --> UI
    
    subgraph "Connection Management"
        CM[Connection Manager] --> CS[Connection Service]
        CS --> DB
        CS --> Monitor[Connection Monitor]
    end
    
    subgraph "Tab Management"
        TM[Tab Manager] --> TS[Tab Service]
        TS --> Store
    end
```

## UI/UX Design Principles

### Visual Design
- **Dark Theme**: Primary com opção light
- **Color Palette**: 
  - Primary: #0ea5e9 (Sky Blue)
  - Secondary: #38bdf8 (Light Blue)
  - Accent: #22c55e (Green)
  - Success: #10b981 (Emerald)
  - Warning: #f59e0b (Amber)
  - Error: #ef4444 (Red)
  - Background: #0f172a (Dark Slate)
  - Surface: #1e293b (Slate 800)
- **Typography**: Inter font family
- **Spacing**: 8px grid system
- **Shadows**: Layered shadows para depth
- **Borders**: Subtle borders com opacity

### Component Design System

#### Authentication Modal
- **Size**: 480px width, auto height
- **Position**: Center screen com backdrop blur
- **Animation**: Scale + fade in (300ms ease-out)
- **Steps**: Progress indicator para multi-step flow
- **Validation**: Real-time com feedback visual

#### Connection Tabs
- **Height**: 40px
- **Max Width**: 200px per tab
- **Min Width**: 120px per tab
- **Shape**: Trapezoidal (Chrome-style)
- **Close Button**: Hover-activated X button
- **Drag Handle**: Entire tab area
- **Overflow**: Horizontal scroll com fade edges

#### Connection Manager
- **Layout**: Grid responsivo (1-3 colunas)
- **Card Design**: Elevated cards com hover effects
- **Status Indicators**: Color-coded dots
- **Actions**: Dropdown menu com ícones
- **Search**: Real-time filtering
- **Sorting**: Por nome, data, status, latência

### Animations & Transitions
- **Page Transitions**: Slide animations (200ms)
- **Loading States**: Skeleton screens com shimmer
- **Hover Effects**: Subtle scale (1.02x) + glow
- **Modal Animations**: Fade + scale (300ms)
- **Tab Switching**: Slide content (150ms)
- **Connection Status**: Pulse animation para testing

### Responsive Design
- **Breakpoints**: 
  - Mobile: 640px (single column)
  - Tablet: 768px (sidebar overlay)
  - Desktop: 1024px (sidebar fixed)
  - Large: 1280px (expanded content)
- **Tab Behavior**: Stack em mobile, scroll em tablet
- **Modal Behavior**: Full screen em mobile

## API Endpoints Reference

### Authentication
- `GET /player_api.php?username={user}&password={pass}` - User info

### Categories
- `GET /player_api.php?action=get_live_categories` - Live categories
- `GET /player_api.php?action=get_vod_categories` - Movie categories  
- `GET /player_api.php?action=get_series_categories` - Series categories

### Streams
- `GET /player_api.php?action=get_live_streams&category_id={id}` - Live streams
- `GET /player_api.php?action=get_vod_streams&category_id={id}` - Movie streams
- `GET /player_api.php?action=get_series&category_id={id}` - Series streams

### Series Details
- `GET /player_api.php?action=get_series_info&series_id={id}` - Series info
- `GET /player_api.php?action=get_series_episodes&series_id={id}&season={num}` - Episodes

### EPG
- `GET /xmltv.php?username={user}&password={pass}` - EPG XML data

### Stream URLs
- Live: `{baseUrl}/live/{username}/{password}/{streamId}.m3u8`
- Movie: `{baseUrl}/movie/{username}/{password}/{streamId}.mp4`
- Series: `{baseUrl}/series/{username}/{password}/{streamId}.mp4`