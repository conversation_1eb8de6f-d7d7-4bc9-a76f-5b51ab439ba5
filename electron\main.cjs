const { app, BrowserWindow, Menu, shell, ipcMain } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const store = require('./store.cjs');

function createWindow() {
  // Criar a janela do navegador
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      // Habilitamos webSecurity para evitar avisos de segurança
      // Como não usamos mais proxy no Electron, podemos habilitar a segurança web
      webSecurity: true,
      // Desabilitamos conteúdo inseguro
      allowRunningInsecureContent: false,
      // Adicionamos o preload script
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, '../public/icons/icon-512.png'),
    show: false,
    titleBarStyle: 'default'
  });

  // Configurar Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; connect-src 'self' https: http:; img-src 'self' https: http: data:; media-src 'self' https: http: blob:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com"]
      }
    });
  });

  // Carregar a aplicação
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    // Abrir DevTools em desenvolvimento
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // Mostrar janela quando estiver pronta
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Interceptar links externos e abrir no navegador padrão
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // Lista de domínios que devem abrir no navegador externo
    const externalDomains = [
      'nekotv.vercel.app',
      'nekotv.top',
      'github.com',
      'discord.gg',
      'wa.me',
      'whatsapp.com'
    ];

    // Verificar se a URL deve ser aberta externamente
    const shouldOpenExternally = externalDomains.some(domain => url.includes(domain));

    if (shouldOpenExternally) {
      shell.openExternal(url);
      return { action: 'deny' }; // Impede a abertura no Electron
    }

    return { action: 'allow' }; // Permite abertura no Electron para outros links
  });

  // Interceptar navegação para links externos
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    // Lista de domínios que devem abrir no navegador externo
    const externalDomains = [
      'nekotv.vercel.app',
      'nekotv.top',
      'github.com',
      'discord.gg',
      'wa.me',
      'whatsapp.com'
    ];

    // Se for um domínio externo, abrir no navegador padrão
    if (externalDomains.some(domain => parsedUrl.hostname.includes(domain))) {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });

  // Remover menu padrão
  Menu.setApplicationMenu(null);
}

// Handler IPC para abrir links externos
ipcMain.on('open-external', (event, url) => {
  shell.openExternal(url);
});

// Handlers IPC para electron-store
ipcMain.handle('electron-store-get', async (event, key) => {
  return store.get(key);
});

ipcMain.handle('electron-store-set', async (event, key, value) => {
  store.set(key, value);
  return true;
});

ipcMain.handle('electron-store-delete', async (event, key) => {
  store.delete(key);
  return true;
});

ipcMain.handle('electron-store-clear', async (event) => {
  store.clear();
  return true;
});

// Este método será chamado quando o Electron tiver terminado
// a inicialização e estiver pronto para criar janelas do navegador.
app.whenReady().then(createWindow);

// Sair quando todas as janelas estiverem fechadas, exceto no macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // No macOS, é comum recriar uma janela na aplicação quando o
  // ícone do dock é clicado e não há outras janelas abertas.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Neste arquivo você pode incluir o resto do código específico do processo principal
// da sua aplicação. Você também pode colocá-los em arquivos separados e requerê-los aqui.