/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_SUPABASE_URL: string
  readonly VITE_SUPABASE_ANON_KEY: string
  readonly VITE_SERVICE_TOKEN: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

interface Window {
  isElectronApp?: boolean;
  process?: {
    versions?: {
      electron?: string;
    }
  }
  electronAPI?: {
    openExternal: (url: string) => boolean;
    getAppVersion: () => string;
    getPlatform: () => string;
    store: {
      get: (key: string) => Promise<any>;
      set: (key: string, value: any) => Promise<boolean>;
      delete: (key: string) => Promise<boolean>;
      clear: () => Promise<boolean>;
    }
  }
}
