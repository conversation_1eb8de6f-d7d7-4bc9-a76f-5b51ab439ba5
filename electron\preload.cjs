// Preload script para o Electron
const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Expor a variável isElectronApp para o processo de renderização
contextBridge.exposeInMainWorld('isElectronApp', true);

// Adicionar outras APIs seguras que o aplicativo possa precisar
contextBridge.exposeInMainWorld('electronAPI', {
  // Você pode adicionar funções seguras aqui para comunicação com o processo principal
  getAppVersion: () => process.env.npm_package_version || 'dev',
  getPlatform: () => process.platform,
  // Função para abrir links externos no navegador padrão
  openExternal: (url) => {
    // Lista de domínios permitidos para segurança
    const allowedDomains = [
      'nekotv.vercel.app',
      'nekotv.top',
      'github.com',
      'discord.gg',
      'wa.me',
      'whatsapp.com'
    ];

    try {
      const urlObj = new URL(url);
      const isAllowed = allowedDomains.some(domain => urlObj.hostname.includes(domain));

      if (isAllowed) {
        ipcRenderer.send('open-external', url);
        return true;
      } else {
        console.warn('Domínio não permitido para abertura externa:', urlObj.hostname);
        return false;
      }
    } catch (error) {
      console.error('URL inválida:', url);
      return false;
    }
  },
  // Funções para acessar o electron-store
  store: {
    get: (key) => ipcRenderer.invoke('electron-store-get', key),
    set: (key, value) => ipcRenderer.invoke('electron-store-set', key, value),
    delete: (key) => ipcRenderer.invoke('electron-store-delete', key),
    clear: () => ipcRenderer.invoke('electron-store-clear')
  }
});

// Isso ajuda a resolver problemas com o React Refresh
window.addEventListener('DOMContentLoaded', () => {
  // Qualquer inicialização específica do Electron pode ser feita aqui
  console.log('Electron preload script carregado');
});