import { useState, useEffect, useCallback } from 'react';
import { supabaseAdminService } from '../services/supabaseAdminService';
import { testMCPConnection } from '../integrations/supabase/mcp-client';
import type { License, LicenseInsert, LicenseUpdate } from '../integrations/supabase/types';

export interface MCPStatus {
  isConnected: boolean;
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  lastCheck: Date | null;
}

export interface MCPOperations {
  // Status
  status: MCPStatus;
  
  // Operações de licenças
  createLicense: (data: Partial<LicenseInsert>) => Promise<License | null>;
  updateLicense: (id: string, data: Partial<LicenseUpdate>) => Promise<License | null>;
  deleteLicense: (id: string) => Promise<boolean>;
  getAllLicenses: () => Promise<License[]>;
  
  // Operações de usuários
  createUser: (email: string, password: string) => Promise<any>;
  getAllUsers: () => Promise<any[]>;
  deleteUser: (userId: string) => Promise<boolean>;
  
  // Estatísticas
  getStats: () => Promise<any>;
  
  // Utilitários
  healthCheck: () => Promise<any>;
  createBackup: () => Promise<any>;
  refreshConnection: () => Promise<void>;
}

/**
 * Hook para usar o MCP do Supabase com permissões completas
 */
export function useSupabaseMCP(): MCPOperations {
  const [status, setStatus] = useState<MCPStatus>({
    isConnected: false,
    isInitialized: false,
    isLoading: true,
    error: null,
    lastCheck: null
  });

  // Inicializar conexão MCP
  const initializeMCP = useCallback(async () => {
    setStatus(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      console.log('🔄 Inicializando MCP do Supabase...');
      
      // Testar conexão
      const isConnected = await testMCPConnection();
      
      if (!isConnected) {
        throw new Error('Falha na conexão MCP');
      }
      
      // Inicializar serviço admin
      const isInitialized = await supabaseAdminService.initialize();
      
      if (!isInitialized) {
        throw new Error('Falha na inicialização do serviço admin');
      }
      
      setStatus({
        isConnected: true,
        isInitialized: true,
        isLoading: false,
        error: null,
        lastCheck: new Date()
      });
      
      console.log('✅ MCP do Supabase inicializado com sucesso!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('❌ Erro ao inicializar MCP:', errorMessage);
      
      setStatus({
        isConnected: false,
        isInitialized: false,
        isLoading: false,
        error: errorMessage,
        lastCheck: new Date()
      });
    }
  }, []);

  // Atualizar status da conexão
  const refreshConnection = useCallback(async () => {
    await initializeMCP();
  }, [initializeMCP]);

  // Operações de licenças
  const createLicense = useCallback(async (data: Partial<LicenseInsert>): Promise<License | null> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.createLicense(data);
    } catch (error) {
      console.error('Erro ao criar licença via MCP:', error);
      return null;
    }
  }, [status.isInitialized]);

  const updateLicense = useCallback(async (id: string, data: Partial<LicenseUpdate>): Promise<License | null> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.updateLicense(id, data);
    } catch (error) {
      console.error('Erro ao atualizar licença via MCP:', error);
      return null;
    }
  }, [status.isInitialized]);

  const deleteLicense = useCallback(async (id: string): Promise<boolean> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.deleteLicense(id);
    } catch (error) {
      console.error('Erro ao deletar licença via MCP:', error);
      return false;
    }
  }, [status.isInitialized]);

  const getAllLicenses = useCallback(async (): Promise<License[]> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.getAllLicenses();
    } catch (error) {
      console.error('Erro ao obter licenças via MCP:', error);
      return [];
    }
  }, [status.isInitialized]);

  // Operações de usuários
  const createUser = useCallback(async (email: string, password: string): Promise<any> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.createAdminUser(email, password);
    } catch (error) {
      console.error('Erro ao criar usuário via MCP:', error);
      throw error;
    }
  }, [status.isInitialized]);

  const getAllUsers = useCallback(async (): Promise<any[]> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.getAllUsers();
    } catch (error) {
      console.error('Erro ao obter usuários via MCP:', error);
      return [];
    }
  }, [status.isInitialized]);

  const deleteUser = useCallback(async (userId: string): Promise<boolean> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.deleteUser(userId);
    } catch (error) {
      console.error('Erro ao deletar usuário via MCP:', error);
      return false;
    }
  }, [status.isInitialized]);

  // Estatísticas
  const getStats = useCallback(async (): Promise<any> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.getAdminStats();
    } catch (error) {
      console.error('Erro ao obter estatísticas via MCP:', error);
      return null;
    }
  }, [status.isInitialized]);

  // Utilitários
  const healthCheck = useCallback(async (): Promise<any> => {
    try {
      return await supabaseAdminService.healthCheck();
    } catch (error) {
      console.error('Erro no health check via MCP:', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, []);

  const createBackup = useCallback(async (): Promise<any> => {
    try {
      if (!status.isInitialized) {
        throw new Error('MCP não inicializado');
      }
      
      return await supabaseAdminService.createBackup();
    } catch (error) {
      console.error('Erro ao criar backup via MCP:', error);
      throw error;
    }
  }, [status.isInitialized]);

  // Inicializar ao montar o hook
  useEffect(() => {
    initializeMCP();
  }, [initializeMCP]);

  return {
    status,
    createLicense,
    updateLicense,
    deleteLicense,
    getAllLicenses,
    createUser,
    getAllUsers,
    deleteUser,
    getStats,
    healthCheck,
    createBackup,
    refreshConnection
  };
}

/**
 * Hook simplificado para verificar status do MCP
 */
export function useMCPStatus() {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkStatus = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const connected = await testMCPConnection();
      setIsConnected(connected);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    isConnected,
    isLoading,
    error,
    refresh: checkStatus
  };
}
