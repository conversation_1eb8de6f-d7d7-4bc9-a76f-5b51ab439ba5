import { createClient } from '@supabase/supabase-js';
import storageService from './storageService';

// Configuração do Supabase
const supabaseUrl = 'https://kzvhuqfkqoncgsyezaaq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.L8agsQM1GDdkVt-ROWFfuHMsk7zGzjHzkW-JJQChXgs';
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false
  },
  global: {
    headers: {
      'Content-Type': 'application/json',
      'Prefer': 'return=representation',
      'apikey': supabaseKey
    }
  }
});

// Interface para licença de dispositivo
export interface DeviceLicense {
  deviceId: string;
  licenseKey: string;
  active: boolean;
  iptv_url?: string;
  iptvUrl?: string;
  message?: string;
  valid?: boolean;
  name?: string;
  createdAt?: number;
  activatedAt?: number;
  configHash?: string;
  lastConfigUpdate?: string;
  lastCheckTime?: string;
}

// Função para exportar para outros módulos
export function getSupabaseClient() {
  return supabase;
}

// Classe para gerenciar licenças de dispositivos
export class DeviceService {
  private deviceId: string;
  private licenseData: DeviceLicense | null = null;
  private callbacks: ((data: DeviceLicense | null) => void)[] = [];
  private checkInterval: any = null;
  private licenseCheckInterval: any = null;
  private lastIptvUrl: string | null = null;
  private initializePromise: Promise<void>;
  
  // Singleton instance
  private static instance: DeviceService;
  
  // Método estático para obter a instância única
  static getInstance(): DeviceService {
    if (!DeviceService.instance) {
      DeviceService.instance = new DeviceService();
    }
    return DeviceService.instance;
  }
  
  constructor() {
    // Inicializar com um ID temporário
    this.deviceId = 'temp-' + Date.now();

    // Inicializar de forma assíncrona
    this.initializePromise = this.initialize();
  }
  
  // Método de inicialização assíncrona
  private async initialize() {
    // Obter o ID do dispositivo
    this.deviceId = await this.generateDeviceId();
    
    // Verificar se há licença salva antes de iniciar verificações
    const savedLicense = await this.loadLicense();
    if (savedLicense && savedLicense.licenseKey && savedLicense.active) {
      // Registrar o login do usuário (inicialização do app)
      await this.trackAppLogin();
      
      // Iniciar verificações periódicas apenas se houver licença válida
      this.startUpdateCheckInterval();
      this.startPeriodicLicenseCheck();
    }
  }
  
  // Registra no Supabase que o usuário iniciou o app
  private async trackAppLogin(): Promise<void> {
    try {
      // Obter licença salva localmente
      const licenseData = await this.loadLicense();
      if (!licenseData || !licenseData.licenseKey) {
        // Se não tiver licença salva ainda, não há o que registrar
        return;
      }
      
      console.log('Registrando login do app para:', licenseData.licenseKey, this.deviceId);
      
      // Atualizar o campo last_active no Supabase
      const { data, error } = await supabase
        .rpc('track_app_login', {
          key: licenseData.licenseKey,
          device: this.deviceId
        });
      
      if (error) {
        console.error('Erro ao registrar login do app:', error);
      } else {
        console.log('Login do app registrado com sucesso');
      }
    } catch (err) {
      console.error('Erro ao tentar registrar login do app:', err);
    }
  }

  // Gera um ID único para o dispositivo
  private async generateDeviceId(): Promise<string> {
    try {
      // Tentar usar ID salvo anteriormente
      const savedId = await storageService.get<string>('iptv_device_id');
      if (savedId) {
        return savedId;
      }
    } catch (error) {
      console.error('Erro ao ler ID do dispositivo:', error);
    }

    // Gerar um código curto e sequencial
    const generateShortCode = () => {
      // Caracteres que serão usados (removendo caracteres confusos como 0/O, 1/I, etc)
      const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
      
      // Timestamp em base36 (compacto) - últimos 5 caracteres
      const timestamp = Date.now().toString(36).slice(-5).toUpperCase();
      
      // 4 caracteres aleatórios
      let randomStr = '';
      for (let i = 0; i < 4; i++) {
        randomStr += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      
      // Combinar para ter um código único de 9 caracteres
      return `${timestamp}-${randomStr}`;
    };
    
    // Criar um ID único e persistente
    const uniqueId = generateShortCode();
    
    // Armazenar para uso futuro
    try {
      await storageService.set('iptv_device_id', uniqueId);
    } catch (e) {
      console.error('Erro ao salvar ID do dispositivo:', e);
    }
    
    return uniqueId;
  }

  // Retorna o ID do dispositivo (síncrono - pode retornar ID temporário)
  static getDeviceId(): string {
    return DeviceService.getInstance().getDeviceId();
  }

  // Retorna o ID do dispositivo após aguardar a inicialização (assíncrono)
  static async getDeviceIdAsync(): Promise<string> {
    const instance = DeviceService.getInstance();
    await instance.initializePromise;
    return instance.getDeviceId();
  }

  // Método de instância para retornar o ID do dispositivo
  getDeviceId(): string {
    return this.deviceId;
  }

  // Formata o ID do dispositivo para exibição
  static formatDeviceId(deviceId: string): string {
    return DeviceService.getInstance().formatDeviceId(deviceId);
  }

  // Método de instância para formatar o ID do dispositivo
  formatDeviceId(deviceId: string): string {
    if (!deviceId) return '';
    
    // Exibe apenas primeiros e últimos caracteres para facilitar leitura
    if (deviceId.length > 8) {
      return `${deviceId.substring(0, 4)}...${deviceId.substring(deviceId.length - 4)}`;
    }
    
    return deviceId;
  }
  
  // Método de instância para verificar se existe uma licença válida
  async hasValidLicense(): Promise<boolean> {
    try {
      const result = await this.checkActivation();
      return result.valid === true;
    } catch (error) {
      console.error('Error checking valid license:', error);
      return false;
    }
  }
  
  // Verifica se existe uma licença válida (método estático)
  static async hasValidLicense(): Promise<boolean> {
    return DeviceService.getInstance().hasValidLicense();
  }

  // Gera uma versão curta da chave de licença para exibição
  generateShortKey(licenseKey: string): string {
    if (!licenseKey) return '';
    
    // Se a licença já estiver no formato XXXX-XXXX-XXXX-XXXX
    if (licenseKey.includes('-')) {
      const parts = licenseKey.split('-');
      if (parts.length >= 4) {
        return `${parts[0]}-XXX-XXX-${parts[3]}`;
      }
    }
    
    // Fallback para outros formatos
    if (licenseKey.length > 8) {
      return `${licenseKey.substring(0, 4)}...${licenseKey.substring(licenseKey.length - 4)}`;
    }
    
    return licenseKey;
  }

  // Verifica a licença no servidor (método estático)
  static async checkLicense(licenseKey: string): Promise<DeviceLicense> {
    return DeviceService.getInstance().checkLicense(licenseKey);
  }

  // Método de instância para verificar a licença no servidor
  async checkLicense(licenseKey: string): Promise<DeviceLicense> {
    try {
      const deviceId = this.getDeviceId();
      
      // Validar o formato da licença antes de tentar verificá-la
      if (!licenseKey || licenseKey.length < 8) { // Assumindo que um código válido tem pelo menos 8 caracteres
        return {
          deviceId,
          licenseKey,
          valid: false,
          active: false,
          message: 'O código de licença é inválido ou muito curto. Por favor, verifique e tente novamente.',
          createdAt: Date.now()
        };
      }
      
      // Log para depuração
      console.log(`Verificando licença com chave exata: "${licenseKey}"`);
      
      // Verificar na API do Supabase - use RPC para garantir verificação exata
      const { data, error } = await supabase
        .rpc('check_license', {
          p_license_key: licenseKey.trim(), // Remover espaços em branco
          p_device_id: deviceId
        });
      
      console.log('Resultado da verificação de licença:', data);
      
      if (error) {
        console.error('Erro na verificação de licença:', error);
        throw error;
      }
      
      if (!data || !data.valid) {
        return {
          deviceId,
          licenseKey,
          valid: false,
          active: false,
          message: data?.message || 'Licença inválida ou não encontrada.',
          createdAt: Date.now()
        };
      }
      
      const licenseData: DeviceLicense = {
        deviceId,
        licenseKey,
        valid: data.valid,
        active: data.valid,
        message: data.message,
        iptvUrl: data.iptv_url,
        iptv_url: data.iptv_url,
        createdAt: Date.now(),
        activatedAt: Date.now()
      };
      
      // Atualizar dados da licença
      this.licenseData = licenseData;
      this.notifyCallbacks();
      
      // Se a licença for válida, iniciar verificações periódicas
      if (licenseData.valid && licenseData.active) {
        this.startPeriodicChecksAfterActivation();
      }
      
      return licenseData;
    } catch (error) {
      console.error('Erro ao verificar licença:', error);
      return {
        deviceId: this.getDeviceId(),
        licenseKey,
        valid: false,
        active: false,
        message: 'Erro ao verificar licença. Verifique sua conexão.',
        createdAt: Date.now()
      };
    }
  }

  // Salva a licença localmente (método estático)
  static async saveLicenseLocally(license: DeviceLicense): Promise<void> {
    return DeviceService.getInstance().saveLicenseLocally(license);
  }

  // Método de instância para salvar a licença localmente
  async saveLicenseLocally(license: DeviceLicense): Promise<void> {
    try {
      await storageService.set('iptv_license', license);
    } catch (error) {
      console.error('Erro ao salvar licença:', error);
    }
  }

  // Carrega a licença salva localmente (método estático)
  static async loadLicense(): Promise<DeviceLicense | null> {
    return DeviceService.getInstance().loadLicense();
  }

  // Método de instância para carregar a licença salva localmente
  async loadLicense(): Promise<DeviceLicense | null> {
    try {
      const savedLicense = await storageService.get<DeviceLicense>('iptv_license');
      return savedLicense;
    } catch (error) {
      console.error('Erro ao carregar licença:', error);
      return null;
    }
  }

  // Remove a licença salva localmente
  async removeLicense(): Promise<void> {
    try {
      await storageService.remove('iptv_license');
    } catch (error) {
      console.error('Erro ao remover licença:', error);
    }
  }

  // Verifica a ativação atual (para compatibilidade)
  async checkActivation(): Promise<{
    valid: boolean;
    message?: string;
    iptvUrl?: string;
    name?: string;
    activatedAt?: string;
  }> {
    console.log('Starting checkActivation...');
    try {
      // Verificar primeiro no servidor, ignorando o cache
      console.log('Checking server first, ignoring local cache...');
      console.log('Using device ID:', this.getDeviceId());
      
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('device_id', this.getDeviceId())
        .eq('active', true)
        .single();
      
      console.log('Server response - data:', data);
      console.log('Server response - error:', error);
      
      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          console.log('No license found for this device ID');
          
          // Limpar qualquer licença salva
          await this.removeLicense();
          
          return {
            valid: false,
            message: 'Nenhuma licença encontrada para este dispositivo.'
          };
        }
        
        console.error('Erro ao verificar licença:', error);
        return {
          valid: false,
          message: 'Erro ao verificar licença.'
        };
      }
      
      if (data && data.active) {
        console.log('Found active license on server:', data);
        // Save the license to persistent storage
        const license: DeviceLicense = {
          deviceId: this.getDeviceId(),
          licenseKey: data.license_key || '',
          active: !!data.active,
          valid: !!data.active,
          iptv_url: data.iptv_url,
          iptvUrl: data.iptv_url,
          name: data.client_name,
          message: 'Licença ativada com sucesso.',
          createdAt: new Date(data.created_at).getTime(),
          activatedAt: new Date(data.updated_at).getTime()
        };
        
        await this.saveLicenseLocally(license);
        this.licenseData = license;
        this.notifyCallbacks();
        
        // Iniciar verificações periódicas após encontrar licença válida
        this.startPeriodicChecksAfterActivation();
        
        return {
          valid: true,
          message: 'Licença ativada com sucesso.',
          iptvUrl: data.iptv_url,
          name: data.client_name,
          activatedAt: data.updated_at
        };
      } else if (data) {
        // License exists but is not active
        console.log('License found but inactive');
        
        // Limpar qualquer licença salva
        await this.removeLicense();
        
        return {
          valid: false,
          message: 'A licença para este dispositivo está desativada.'
        };
      }
      
      // Somente agora verificamos o armazenamento persistente como último recurso
      const savedLicense = await this.loadLicense();
      console.log('No active license in server, checking persistent storage:', savedLicense);
      
      if (savedLicense && savedLicense.active && savedLicense.licenseKey) {
        console.log('Found license in persistent storage, verifying with server...');
        
        // Verificar com o servidor se a licença ainda é válida
        const verifiedLicense = await this.fetchLicenseByKey(savedLicense.licenseKey);
        
        if (verifiedLicense && verifiedLicense.active) {
          console.log('License from persistent storage verified with server');
          return {
            valid: true,
            message: 'Licença ativada com sucesso.',
            iptvUrl: verifiedLicense.iptv_url || verifiedLicense.iptvUrl,
            name: verifiedLicense.name,
            activatedAt: verifiedLicense.activatedAt ? new Date(verifiedLicense.activatedAt).toISOString() : undefined
          };
        } else {
          console.log('License from persistent storage is no longer valid');
          
          // Limpar a licença inválida
          await this.removeLicense();
          
          return {
            valid: false,
            message: 'A licença salva não é mais válida.'
          };
        }
      }
      
      return {
        valid: false,
        message: 'Nenhuma licença encontrada para este dispositivo.'
      };
    } catch (error) {
      console.error('Error in checkActivation:', error);
      return {
        valid: false,
        message: 'Erro ao verificar licença.'
      };
    }
  }

  // Método privado para verificar ativação
  private async hasValidLicenseUsingActivation(): Promise<boolean> {
    try {
      const result = await this.checkActivation();
      return result.valid === true;
    } catch (error) {
      console.error('Error checking valid license:', error);
      return false;
    }
  }

  // Intervalo para verificação de atualizações
  private updateCheckInterval: NodeJS.Timeout | null = null;
  
  // Iniciar verificação periódica de atualizações a cada 30 segundos
  async startUpdateCheckInterval() {
    if (this.updateCheckInterval) {
      clearInterval(this.updateCheckInterval);
    }
    
    // Verificar imediatamente ao iniciar
    await this.checkForUpdatesAndNotify();
    
    // Configurar verificação a cada 30 segundos
    this.updateCheckInterval = setInterval(async () => {
      await this.checkForUpdatesAndNotify();
    }, 30 * 1000); // 30 segundos
    
    console.log('Verificação de atualizações iniciada - intervalo: 30 segundos');
  }
  
  // Para a verificação periódica de atualizações
  stopUpdateCheckInterval() {
    if (this.updateCheckInterval) {
      clearInterval(this.updateCheckInterval);
      this.updateCheckInterval = null;
      console.log('Verificação de atualizações interrompida');
    }
  }
  
  // Verificar atualizações e notificar o usuário se houver
  private async checkForUpdatesAndNotify() {
    try {
      if (!this.licenseData || !this.licenseData.licenseKey) {
        return; // Não temos licença para verificar
      }
      
      const result = await this.checkForUpdates();
      
      if (result.hasUpdates) {
        console.log('Novas configurações disponíveis:', result);
        
        // Atualizar a URL IPTV e outros dados
        if (result.newIptvUrl) {
          // Notificar o usuário de forma não intrusiva
          this.showUpdateNotification(result);
        }
      }
    } catch (error) {
      console.error('Erro ao verificar e notificar atualizações:', error);
    }
  }
  
  // Exibe uma notificação ultra-sutil sobre a atualização
  private showUpdateNotification(updateInfo: {newIptvUrl?: string; isActive?: boolean; message?: string}) {
    try {
      // Log no console antes de qualquer interação com o DOM
      console.log('Atualizando lista IPTV silenciosamente:', updateInfo);
      
      // Criar um overlay sutil no topo da tela como uma pequena barra de notificação
      const notificationId = 'neko-update-indicator';
      
      // Se já existe, remover
      const existingNotification = document.getElementById(notificationId);
      if (existingNotification) {
        existingNotification.remove();
      }
      
      // Criar um ícone pulsante sutil no canto da tela
      const indicatorEl = document.createElement('div');
      indicatorEl.id = notificationId;
      indicatorEl.className = 'neko-update-indicator';
      indicatorEl.innerHTML = `
        <div class="indicator-dot"></div>
        <div class="indicator-tooltip">Lista IPTV atualizada</div>
      `;
      
      // Estilo muito mais sutil - apenas um pequeno indicador visual
      const style = document.createElement('style');
      style.textContent = `
        .neko-update-indicator {
          position: fixed;
          top: 15px;
          right: 15px;
          width: 12px;
          height: 12px;
          z-index: 999;
          pointer-events: none; /* Não interfere com clicks/digitação */
        }
        
        .indicator-dot {
          width: 100%;
          height: 100%;
          background-color: #0ea5e9;
          border-radius: 50%;
          box-shadow: 0 0 5px rgba(14, 165, 233, 0.6);
          animation: pulse 2s infinite;
          opacity: 0.9;
        }
        
        .indicator-tooltip {
          position: absolute;
          top: -5px;
          right: 20px;
          background-color: rgba(15, 23, 42, 0.75);
          color: #f8fafc;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          white-space: nowrap;
          opacity: 0;
          transform: translateX(10px);
          transition: opacity 0.3s, transform 0.3s;
          pointer-events: none;
        }
        
        .neko-update-indicator:hover .indicator-tooltip {
          opacity: 1;
          transform: translateX(0);
        }
        
        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 0.9;
          }
          50% {
            transform: scale(1.2);
            opacity: 1;
          }
          100% {
            transform: scale(1);
            opacity: 0.9;
          }
        }
      `;
      
      // Adicionar à página sem interferir em nada
      document.head.appendChild(style);
      document.body.appendChild(indicatorEl);
      
      // Silenciosamente aplicar a atualização da lista IPTV
      // sem interferir na interação do usuário
      this.silentlyApplyUpdate(updateInfo);
      
      // Remover o indicador após 10 segundos
      setTimeout(() => {
        if (document.body.contains(indicatorEl)) {
          // Fade out suave
          indicatorEl.style.opacity = '0';
          indicatorEl.style.transition = 'opacity 0.5s';
          
          // Remover após a transição
          setTimeout(() => {
            if (document.body.contains(indicatorEl)) {
              indicatorEl.remove();
            }
          }, 500);
        }
      }, 10000);
    } catch (error) {
      // Simplesmente logar o erro sem interferir na experiência do usuário
      console.error('Erro ao processar atualização silenciosa:', error);
    }
  }
  
  // Aplica a atualização silenciosamente, sem interromper o fluxo do usuário
  private async silentlyApplyUpdate(updateInfo: {newIptvUrl?: string; isActive?: boolean; message?: string}) {
    // Lógica para atualizar a URL IPTV sem interrupção do usuário
    if (updateInfo.newIptvUrl && this.licenseData) {
      // Atualizar os dados internos
      this.licenseData.iptvUrl = updateInfo.newIptvUrl;
      this.licenseData.iptv_url = updateInfo.newIptvUrl;
      
      // Salvar localmente
      await this.saveLicenseLocally(this.licenseData);
      
      // Disparar evento customizado para componentes da UI que precisam saber
      // sobre a mudança, mas sem exigir interação do usuário
      const event = new CustomEvent('iptv-update-available', { 
        detail: { 
          url: updateInfo.newIptvUrl,
          silent: true 
        } 
      });
      document.dispatchEvent(event);
      
      // Atualizar qualquer cache em background
      setTimeout(async () => {
        try {
          // Tentar atualizar a lista em background
          const cacheKey = 'iptv-playlist-cache';
          await storageService.remove(cacheKey);
          console.log('Cache da playlist limpo com sucesso');
        } catch (e) {
          console.error('Erro ao limpar cache:', e);
        }
      }, 100);
    }
  }

  // Verifica se há atualizações disponíveis para a licença
  async checkForUpdates(): Promise<{
    hasUpdates: boolean;
    newIptvUrl?: string;
    isActive?: boolean;
    message?: string;
  }> {
    try {
      // Se não tivermos dados de licença, não podemos verificar atualizações
      if (!this.licenseData || !this.licenseData.licenseKey) {
        return { hasUpdates: false, message: 'Sem licença ativa para verificar.' };
      }
      
      // Obter o último timestamp de verificação
      const lastCheckTime = this.licenseData.lastCheckTime || null;
      
      // Chamar a função RPC que criamos para verificar atualizações
      const { data, error } = await supabase.rpc('neko_tv_check_updates', {
        key: this.licenseData.licenseKey,
        device_id: this.deviceId,
        last_check: lastCheckTime
      });
      
      if (error) {
        console.error('Erro ao verificar atualizações:', error);
        return { hasUpdates: false, message: 'Erro ao verificar atualizações.' };
      }
      
      if (!data) {
        return { hasUpdates: false, message: 'Sem resposta do servidor.' };
      }
      
      console.log('Resultado da verificação de atualizações:', data);
      
      // Atualizar o timestamp de última verificação
      if (this.licenseData) {
        this.licenseData.lastCheckTime = new Date().toISOString();
        if (data.last_config_update) {
          this.licenseData.lastConfigUpdate = data.last_config_update;
        }
        await this.saveLicenseLocally(this.licenseData);
      }
      
      // Se houver atualizações, notificar o usuário
      if (data.has_updates) {
        // Atualizar os dados da licença
        if (this.licenseData) {
          this.licenseData.iptvUrl = data.iptv_url;
          this.licenseData.iptv_url = data.iptv_url;
          this.licenseData.active = data.is_active;
          await this.saveLicenseLocally(this.licenseData);
          this.notifyCallbacks();
        }
        
        return {
          hasUpdates: true,
          newIptvUrl: data.iptv_url,
          isActive: data.is_active,
          message: 'Novas configurações disponíveis.'
        };
      }
      
      return { 
        hasUpdates: false, 
        message: 'Nenhuma atualização disponível.'
      };
    } catch (error) {
      console.error('Erro ao verificar atualizações:', error);
      return { hasUpdates: false, message: 'Erro ao verificar atualizações.' };
    }
  }
  
  // Para a verificação periódica
  private stopPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }
  
  // Notifica todos os callbacks registrados
  private notifyCallbacks() {
    this.callbacks.forEach(callback => {
      callback(this.licenseData);
    });
  }

  // Ativa uma licença com uma chave específica
  async activateLicense(licenseKey: string): Promise<DeviceLicense> {
    return await this.checkLicense(licenseKey);
  }

  // Busca uma licença específica pelo código da licença para propósitos administrativos
  async fetchLicenseByKey(licenseKey: string): Promise<DeviceLicense | null> {
    try {
      console.log('Fetching license with exact key match:', licenseKey);
      
      // Verificar primeiro no cache local
      const cachedLicense = await storageService.get<DeviceLicense>('iptv_license');
      if (cachedLicense && cachedLicense.licenseKey === licenseKey && cachedLicense.active) {
        console.log('Found cached license with key:', licenseKey);
        return cachedLicense;
      }
      
      // Usar eq para correspondência exata - não aceitar chaves parciais
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey) // Correspondência exata
        .eq('active', true) // Garantir que só busque licenças ativas
        .limit(1);
      
      console.log('License fetch result:', data, error);
      
      if (error || !data || data.length === 0) {
        console.error('License not found or error:', error);
        return null;
      }
      
      // Convert to license format
      const licenseData = data[0];
      const license: DeviceLicense = {
        deviceId: licenseData.device_id || this.getDeviceId(),
        licenseKey: licenseData.license_key,
        active: !!licenseData.active,
        valid: !!licenseData.active,
        iptv_url: licenseData.iptv_url,
        iptvUrl: licenseData.iptv_url,
        name: licenseData.client_name,
        message: 'Licença encontrada com sucesso.',
        createdAt: new Date(licenseData.created_at).getTime(),
        activatedAt: new Date(licenseData.updated_at).getTime()
      };
      
      // Save the license locally
      this.licenseData = license;
      await this.saveLicenseLocally(license);
      this.notifyCallbacks();
      
      return license;
    } catch (error) {
      console.error('Error fetching license:', error);
      return null;
    }
  }

  // Busca uma licença pelo deviceId
  async fetchLicenseByDeviceId(deviceId: string): Promise<DeviceLicense | null> {
    try {
      console.log('Fetching license by device ID:', deviceId);
      
      // Verificar primeiro no cache local
      const cachedLicense = await storageService.get<DeviceLicense>('iptv_license');
      if (cachedLicense && cachedLicense.deviceId === deviceId && cachedLicense.active) {
        console.log('Found cached license for device ID:', deviceId);
        return cachedLicense;
      }
      
      // Se não encontrou no cache, buscar no servidor
      // Usar comparação exata para validar o deviceId
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('device_id', deviceId)
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (error) {
        console.error('Error fetching license by device ID:', error);
        return null;
      }
      
      console.log('License fetch by device ID result:', data, error);
      
      if (!data || data.length === 0) {
        console.error('Error fetching license by device ID: No results');

        return null;
      }
      
      // Mapear campos da API para nossa interface DeviceLicense
      const license = data[0];
      return {
        deviceId: license.device_id,
        licenseKey: license.license_key,
        active: license.active === true,
        iptvUrl: license.iptv_url,
        iptv_url: license.iptv_url,
        name: license.client_name,
        valid: license.active === true,
        createdAt: new Date(license.created_at).getTime(),
        activatedAt: license.activated_at ? new Date(license.activated_at).getTime() : Date.now()
      };
    } catch (error) {
      console.error('Error fetching license by device ID:', error);
      return null;
    }
  }

  // Busca uma licença pelo nome do cliente (ex: "giga")
  async fetchLicenseByClientName(clientName: string): Promise<DeviceLicense | null> {
    try {
      console.log('Fetching license for client:', clientName);
      
      // Para o nome do cliente, mantemos a correspondência parcial usando ilike,
      // pois é útil para pesquisas por nome
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .ilike('client_name', `%${clientName}%`)
        .eq('active', true) // Adicionar filtro para licenças ativas apenas
        .order('created_at', { ascending: false })
        .limit(1);
      
      console.log('License fetch by client result:', data, error);
      
      if (error || !data || data.length === 0) {
        console.error('Error fetching license by client name or no results:', error);
        return null;
      }
      
      const licenseData = data[0];
      
      // Converter para o formato da licença
      const license: DeviceLicense = {
        deviceId: licenseData.device_id || this.getDeviceId(),
        licenseKey: licenseData.license_key,
        active: !!licenseData.active,
        valid: !!licenseData.active,
        iptv_url: licenseData.iptv_url,
        iptvUrl: licenseData.iptv_url,
        name: licenseData.client_name,
        message: 'Licença encontrada com sucesso.',
        createdAt: new Date(licenseData.created_at).getTime(),
        activatedAt: new Date(licenseData.updated_at).getTime()
      };
      
      // Salvar a licença localmente e definir como licença atual
      this.licenseData = license;
      await this.saveLicenseLocally(license);
      this.notifyCallbacks();
        
      return license;
    } catch (error) {
      console.error('Error fetching license by client name:', error);
      return null;
    }
  }

  // Registra callback para mudanças de status de licença
  onActivationChange(callback: (data: DeviceLicense | null) => void): () => void {
    this.callbacks.push(callback);
    
    // Chamar callback imediatamente com status atual
    if (this.licenseData) {
      callback(this.licenseData);
    } else {
      // Verificar licença se ainda não tiver dados
      this.checkActivation().then(() => {
        callback(this.licenseData);
      });
    }
    
    // Retorna função para cancelar a inscrição
    return () => {
      this.callbacks = this.callbacks.filter(cb => cb !== callback);
    };
  }
  
  // Verificar periodicamente se a licença foi alterada no Supabase
  async startPeriodicLicenseCheck() {
    // Limpar qualquer intervalo existente
    if (this.licenseCheckInterval) {
      clearInterval(this.licenseCheckInterval);
    }

    // Armazenar a URL atual do IPTV
    const savedLicense = await this.loadLicense();
    
    // Só iniciar verificações se houver uma licença válida salva
    if (!savedLicense || !savedLicense.licenseKey || !savedLicense.active) {
      console.log('Nenhuma licença válida encontrada. Verificação periódica não iniciada.');
      return;
    }
    
    this.lastIptvUrl = savedLicense?.iptvUrl || null;
    console.log('Iniciando verificação periódica de licença. URL atual:', this.lastIptvUrl);

    // Verificar a cada 2 minutos
    this.licenseCheckInterval = setInterval(async () => {
      try {
        console.log('Verificando atualizações de licença no Supabase...');
        const deviceId = this.getDeviceId();
        
        // Buscar a licença mais recente do Supabase
        const license = await this.fetchLicenseByDeviceId(deviceId);
        
        if (license && license.active) {
          const currentIptvUrl = license.iptvUrl || '';
          
          // Se a URL do IPTV mudou desde a última verificação
          if (this.lastIptvUrl && currentIptvUrl && this.lastIptvUrl !== currentIptvUrl) {
            console.log('Mudança na URL do IPTV detectada!');
            console.log('URL anterior:', this.lastIptvUrl);
            console.log('Nova URL:', currentIptvUrl);
            
            // Salvar localmente a licença atualizada
            const updatedLicense: DeviceLicense = {
              deviceId: license.deviceId,
              licenseKey: license.licenseKey,
              active: true,
              iptvUrl: currentIptvUrl,
              message: 'Licença atualizada automaticamente',
              name: license.name || 'Neko TV',
              valid: true,
              createdAt: Date.now(),
              activatedAt: Date.now()
            };
            
            await this.saveLicenseLocally(updatedLicense);
            
            // Atualizar a última URL conhecida
            this.lastIptvUrl = currentIptvUrl;
            
            // Exibir notificação estilosa em vez de alert
            this.showStylishUpdateNotification();
            
            // Recarregar a aplicação após um breve atraso para dar tempo de ver a notificação
            setTimeout(() => {
              window.location.href = '/access?refresh=' + Date.now();
            }, 3000);
          } else if (!this.lastIptvUrl && currentIptvUrl) {
            // Primeira vez que detectamos uma URL
            this.lastIptvUrl = currentIptvUrl;
          }
        }
      } catch (error) {
        console.error('Erro ao verificar atualizações de licença:', error);
      }
    }, 120000); // 120000 ms = 2 minutos
  }

  // Parar a verificação periódica de licença
  stopPeriodicLicenseCheck() {
    if (this.licenseCheckInterval) {
      clearInterval(this.licenseCheckInterval);
      this.licenseCheckInterval = null;
    }
  }
  
  // Iniciar verificações após ativação de licença
  async startPeriodicChecksAfterActivation() {
    // Registrar o login do usuário
    await this.trackAppLogin();
    
    // Iniciar verificações periódicas
    this.startUpdateCheckInterval();
    this.startPeriodicLicenseCheck();
  }
  
  // Limpeza
  cleanup() {
    this.stopPeriodicCheck();
    this.stopPeriodicLicenseCheck();
  }

  // Exibe uma notificação moderna e estilosa para atualizações importantes
  private showStylishUpdateNotification() {
    // Criar overlay completo para a notificação importante
    const overlayId = 'neko-update-overlay';
    
    // Remover se já existir
    const existingOverlay = document.getElementById(overlayId);
    if (existingOverlay) {
      existingOverlay.remove();
    }
    
    // Criar overlay com efeito de blur
    const overlayEl = document.createElement('div');
    overlayEl.id = overlayId;
    overlayEl.className = 'neko-update-overlay';
    
    // HTML com animação e design moderno
    overlayEl.innerHTML = `
      <div class="modern-update-modal">
        <div class="modal-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
        </div>
        <div class="modal-content">
          <h2>Atualização Disponível</h2>
          <p>As configurações da sua TV foram atualizadas.<br>Aplicando mudanças automaticamente.</p>
          <div class="loading-bar">
            <div class="loading-progress"></div>
          </div>
        </div>
      </div>
    `;
    
    // Adicionar estilos modernos e animados
    const style = document.createElement('style');
    style.textContent = `
      .neko-update-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 9999;
        animation: fadeIn 0.3s ease-out;
      }
      
      .modern-update-modal {
        background: linear-gradient(135deg, #1e293b, #0f172a);
        border-radius: 16px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        padding: 32px;
        max-width: 400px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        overflow: hidden;
        position: relative;
        animation: slideUp 0.5s cubic-bezier(0.16, 1, 0.3, 1);
      }
      
      .modal-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        border-radius: 50%;
        margin-bottom: 24px;
        color: white;
        box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.3);
        animation: pulse 2s infinite;
      }
      
      .modal-content h2 {
        color: #f8fafc;
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 12px;
      }
      
      .modal-content p {
        color: #cbd5e1;
        font-size: 16px;
        line-height: 1.6;
        margin: 0 0 24px;
      }
      
      .loading-bar {
        width: 100%;
        height: 6px;
        background-color: #334155;
        border-radius: 10px;
        overflow: hidden;
        margin-top: 8px;
      }
      
      .loading-progress {
        height: 100%;
        background: linear-gradient(90deg, #0ea5e9, #38bdf8);
        border-radius: 10px;
        width: 0%;
        animation: loadProgress 2.5s ease-in-out forwards;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      
      @keyframes pulse {
        0% { transform: scale(1); box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.3); }
        50% { transform: scale(1.05); box-shadow: 0 15px 20px -3px rgba(14, 165, 233, 0.4); }
        100% { transform: scale(1); box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.3); }
      }
      
      @keyframes loadProgress {
        0% { width: 0%; }
        20% { width: 30%; }
        50% { width: 60%; }
        80% { width: 80%; }
        100% { width: 100%; }
      }
    `;
    
    // Adicionar à página
    document.head.appendChild(style);
    document.body.appendChild(overlayEl);
    
    // Não precisamos de botões de ação - a notificação é puramente visual
    // e será removida automaticamente quando a página for recarregada
  }
}

// Criar e exportar uma instância para manter compatibilidade com código existente
export const deviceService = DeviceService.getInstance();

// Exportar a classe como default
export default DeviceService;