import React from 'react';
import { Link } from 'react-router-dom';

const NotFound: React.FC = () => {
  return (
    <div className="not-found">
      <h1>404 - Page Not Found</h1>
      <p>The page you are looking for does not exist.</p>
      <Link to="/" className="back-home">
        Back to Home
      </Link>
      <style>{`
        .not-found {
          text-align: center;
          padding: 50px 20px;
          max-width: 600px;
          margin: 0 auto;
        }

        .not-found h1 {
          font-size: 36px;
          margin-bottom: 20px;
          color: #333;
        }

        .not-found p {
          font-size: 18px;
          color: #666;
          margin-bottom: 30px;
        }

        .back-home {
          display: inline-block;
          padding: 10px 20px;
          background-color: #007bff;
          color: white;
          text-decoration: none;
          border-radius: 4px;
          transition: background-color 0.2s;
        }

        .back-home:hover {
          background-color: #0056b3;
        }
      `}</style>
    </div>
  );
};

export default NotFound; 