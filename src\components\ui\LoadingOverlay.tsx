import React from 'react';
import { Box, CircularProgress, Typography, Fade } from '@mui/material';
import { styled } from '@mui/material/styles';

interface LoadingOverlayProps {
  show: boolean;
  message?: string;
  variant?: 'player' | 'page' | 'inline';
  size?: 'small' | 'medium' | 'large';
}

// Base overlay container
const OverlayContainer = styled(Box)<{ variant: string }>(({ theme, variant }) => ({
  position: variant === 'inline' ? 'relative' : 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: variant === 'player' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.5)',
  backdropFilter: 'blur(4px)',
  zIndex: 1000,
  gap: theme.spacing(2),
  
  ...(variant === 'inline' && {
    minHeight: '200px',
    backgroundColor: 'transparent',
    backdropFilter: 'none',
  }),
  
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(1.5),
  }
}));

// Loading spinner with responsive sizing
const StyledProgress = styled(CircularProgress)<{ size: string }>(({ theme, size }) => {
  const sizeMap = {
    small: 32,
    medium: 48,
    large: 64,
  };
  
  const mobileSize = {
    small: 28,
    medium: 40,
    large: 56,
  };
  
  return {
    width: `${sizeMap[size as keyof typeof sizeMap]}px !important`,
    height: `${sizeMap[size as keyof typeof sizeMap]}px !important`,
    color: theme.palette.primary.main,
    
    [theme.breakpoints.down('sm')]: {
      width: `${mobileSize[size as keyof typeof mobileSize]}px !important`,
      height: `${mobileSize[size as keyof typeof mobileSize]}px !important`,
    },
    
    // Smooth animation
    '& .MuiCircularProgress-circle': {
      strokeLinecap: 'round',
    }
  };
});

// Loading message with responsive typography
const LoadingMessage = styled(Typography)(({ theme }) => ({
  color: 'white',
  textAlign: 'center',
  fontWeight: 500,
  fontSize: '1rem',
  maxWidth: '80%',
  
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.875rem',
    maxWidth: '90%',
  },
  
  [theme.breakpoints.down('xs')]: {
    fontSize: '0.8rem',
  }
}));

// Pulsing dots animation
const PulsingDots = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(0.5),
  
  '& .dot': {
    width: 6,
    height: 6,
    borderRadius: '50%',
    backgroundColor: theme.palette.primary.main,
    animation: 'pulse 1.4s ease-in-out infinite both',
    
    '&:nth-of-type(1)': { animationDelay: '-0.32s' },
    '&:nth-of-type(2)': { animationDelay: '-0.16s' },
    '&:nth-of-type(3)': { animationDelay: '0s' },
  },
  
  '@keyframes pulse': {
    '0%, 80%, 100%': {
      transform: 'scale(0)',
      opacity: 0.5,
    },
    '40%': {
      transform: 'scale(1)',
      opacity: 1,
    },
  },
  
  [theme.breakpoints.down('sm')]: {
    '& .dot': {
      width: 5,
      height: 5,
    }
  }
}));

/**
 * Componente de overlay de loading responsivo
 * Suporta diferentes variantes e tamanhos para diferentes contextos
 */
const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  show,
  message = 'Carregando...',
  variant = 'page',
  size = 'medium'
}) => {
  if (!show) return null;

  return (
    <Fade in={show} timeout={300}>
      <OverlayContainer variant={variant}>
        <StyledProgress size={size} thickness={4} />
        
        {message && (
          <LoadingMessage variant="body1">
            {message}
          </LoadingMessage>
        )}
        
        <PulsingDots>
          <Box className="dot" />
          <Box className="dot" />
          <Box className="dot" />
        </PulsingDots>
      </OverlayContainer>
    </Fade>
  );
};

/**
 * Componente de loading inline mais simples
 */
export const InlineLoading: React.FC<{
  message?: string;
  size?: 'small' | 'medium' | 'large';
}> = ({ message = 'Carregando...', size = 'small' }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 1,
        py: 3,
      }}
    >
      <StyledProgress size={size} thickness={4} />
      {message && (
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            fontSize: { xs: '0.75rem', sm: '0.875rem' },
            textAlign: 'center',
          }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );
};

/**
 * Hook para controlar estados de loading
 */
export const useLoading = (initialState = false) => {
  const [isLoading, setIsLoading] = React.useState(initialState);
  const [loadingMessage, setLoadingMessage] = React.useState<string>('');

  const startLoading = (message?: string) => {
    setIsLoading(true);
    if (message) setLoadingMessage(message);
  };

  const stopLoading = () => {
    setIsLoading(false);
    setLoadingMessage('');
  };

  const updateMessage = (message: string) => {
    setLoadingMessage(message);
  };

  return {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading,
    updateMessage,
  };
};

export default LoadingOverlay;
