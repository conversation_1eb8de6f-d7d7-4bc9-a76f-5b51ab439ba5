import { dbService } from '../utils/storage';
import { Playlist, PlaylistInput } from '../types/Channel';

export const playlistService = {
  async addPlaylist(playlist: Omit<Playlist, 'id' | 'channelCount' | 'lastUpdated'>) {
    try {
      const playlistInput: PlaylistInput = {
        name: playlist.name,
        url: playlist.url,
        type: playlist.type || 'm3u',
        description: playlist.description,
        categories: playlist.categories,
        groups: playlist.groups,
        thumbnail: playlist.thumbnail
      };
      
      const newPlaylist = await dbService.savePlaylist(playlistInput);
      return newPlaylist.id;
    } catch (error) {
      console.error('Error adding playlist:', error);
      throw error;
    }
  },

  async getPlaylists() {
    try {
      return await dbService.getPlaylists();
    } catch (error) {
      console.error('Error getting playlists:', error);
      throw error;
    }
  },

  async deletePlaylist(id: string) {
    try {
      await dbService.deletePlaylist(id);
    } catch (error) {
      console.error('Error deleting playlist:', error);
      throw error;
    }
  },

  async updatePlaylist(id: string, updates: Partial<Playlist>) {
    try {
      const existingPlaylist = await dbService.getPlaylist(id);
      if (!existingPlaylist) {
        throw new Error(`Playlist with id ${id} not found`);
      }
      
      const playlistInput: PlaylistInput = {
        name: updates.name || existingPlaylist.name,
        url: updates.url || existingPlaylist.url,
        type: updates.type || existingPlaylist.type || 'm3u',
        description: updates.description || existingPlaylist.description,
        categories: updates.categories || existingPlaylist.categories,
        groups: updates.groups || existingPlaylist.groups,
        thumbnail: updates.thumbnail || existingPlaylist.thumbnail
      };
      
      await dbService.savePlaylist(playlistInput);
    } catch (error) {
      console.error('Error updating playlist:', error);
      throw error;
    }
  },

  async searchPlaylists(searchTerm: string) {
    try {
      const allPlaylists = await dbService.getPlaylists();
      const searchTermLower = searchTerm.toLowerCase();
      
      return allPlaylists.filter(playlist => 
        playlist.name.toLowerCase().includes(searchTermLower)
      );
    } catch (error) {
      console.error('Error searching playlists:', error);
      throw error;
    }
  }
}; 