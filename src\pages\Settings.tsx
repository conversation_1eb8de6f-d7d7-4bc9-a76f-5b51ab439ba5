import React from 'react';
import InstallPWA from '../components/InstallPWA';
import { Container, Paper, Typography } from '@mui/material';

const Settings: React.FC = () => {
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Configurações
      </Typography>

      {/* Install PWA Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Instalar Aplicativo
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Instale o IPTV Player como um aplicativo nativo no seu dispositivo para uma melhor experiência.
        </Typography>
        <InstallPWA />
      </Paper>

      <div className="settings-container">
        <header className="settings-header">
          <h1>Settings</h1>
          <p>Customize your IPTV player experience</p>
        </header>

        <div className="settings-grid">
          <section className="settings-section">
            <div className="section-header">
              <h2>Player Settings</h2>
              <p>Configure how your content plays</p>
            </div>
            
            <div className="settings-list">
              <div className="setting-item">
                <div className="setting-info">
                  <label>Default Quality</label>
                  <span className="setting-description">
                    Choose the default video quality for playback
                  </span>
                </div>
                <select defaultValue="auto" className="setting-control">
                  <option value="auto">Auto</option>
                  <option value="1080p">1080p</option>
                  <option value="720p">720p</option>
                  <option value="480p">480p</option>
                </select>
              </div>

              <div className="setting-item">
                <div className="setting-info">
                  <label>Autoplay</label>
                  <span className="setting-description">
                    Automatically start playing when content is loaded
                  </span>
                </div>
                <label className="toggle">
                  <input type="checkbox" defaultChecked />
                  <span className="toggle-slider"></span>
                </label>
              </div>

              <div className="setting-item">
                <div className="setting-info">
                  <label>Hardware Acceleration</label>
                  <span className="setting-description">
                    Use GPU for better performance
                  </span>
                </div>
                <label className="toggle">
                  <input type="checkbox" defaultChecked />
                  <span className="toggle-slider"></span>
                </label>
              </div>
            </div>
          </section>

          <section className="settings-section">
            <div className="section-header">
              <h2>Interface Settings</h2>
              <p>Customize the look and feel</p>
            </div>
            
            <div className="settings-list">
              <div className="setting-item">
                <div className="setting-info">
                  <label>Theme</label>
                  <span className="setting-description">
                    Choose your preferred color theme
                  </span>
                </div>
                <select defaultValue="system" className="setting-control">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="system">System</option>
                </select>
              </div>

              <div className="setting-item">
                <div className="setting-info">
                  <label>Thumbnail Preview</label>
                  <span className="setting-description">
                    Show preview thumbnails in playlists
                  </span>
                </div>
                <label className="toggle">
                  <input type="checkbox" defaultChecked />
                  <span className="toggle-slider"></span>
                </label>
              </div>
            </div>
          </section>
        </div>
      </div>

      <style>{`
        .settings-container {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .settings-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .settings-header h1 {
          font-size: 2.5rem;
          font-weight: 700;
          color: #1a202c;
          margin-bottom: 0.5rem;
        }

        .settings-header p {
          font-size: 1.1rem;
          color: #718096;
        }

        .settings-grid {
          display: grid;
          gap: 2rem;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        }

        .settings-section {
          background: white;
          border-radius: 12px;
          padding: 2rem;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          border: 1px solid #e2e8f0;
        }

        .section-header {
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e2e8f0;
        }

        .section-header h2 {
          font-size: 1.5rem;
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 0.25rem;
        }

        .section-header p {
          color: #718096;
          font-size: 0.9rem;
        }

        .settings-list {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background: #f7fafc;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .setting-info {
          flex: 1;
        }

        .setting-info label {
          display: block;
          font-weight: 500;
          color: #2d3748;
          margin-bottom: 0.25rem;
        }

        .setting-description {
          font-size: 0.85rem;
          color: #718096;
        }

        .setting-control {
          padding: 0.5rem 0.75rem;
          border: 1px solid #cbd5e0;
          border-radius: 6px;
          background: white;
          color: #2d3748;
          font-size: 0.9rem;
          min-width: 120px;
        }

        .setting-control:focus {
          outline: none;
          border-color: #3182ce;
          box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .toggle {
          position: relative;
          display: inline-block;
          width: 48px;
          height: 24px;
        }

        .toggle input {
          opacity: 0;
          width: 0;
          height: 0;
        }

        .toggle-slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #cbd5e0;
          transition: 0.3s;
          border-radius: 24px;
        }

        .toggle-slider:before {
          position: absolute;
          content: "";
          height: 18px;
          width: 18px;
          left: 3px;
          bottom: 3px;
          background-color: white;
          transition: 0.3s;
          border-radius: 50%;
        }

        .toggle input:checked + .toggle-slider {
          background-color: #3182ce;
        }

        .toggle input:checked + .toggle-slider:before {
          transform: translateX(24px);
        }

        @media (max-width: 768px) {
          .settings-container {
            padding: 1rem;
          }
          
          .settings-grid {
            grid-template-columns: 1fr;
          }
          
          .setting-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }
          
          .setting-control {
            width: 100%;
          }
        }
      `}</style>
    </Container>
  );
};

export default Settings; 