// Este script é executado pelo electron-builder após o empacotamento
const fs = require('fs');
const path = require('path');

module.exports = async function(context) {
  console.log('Executando script after-pack...');
  
  try {
    // Verificar se o contexto é válido
    if (!context || !context.appOutDir || !context.electronPlatformName) {
      console.log('Contexto inválido ou incompleto. Abortando verificações.');
      return;
    }
    
    const { appOutDir, packager, electronPlatformName } = context;
    
    console.log(`Aplicação empacotada em: ${appOutDir}`);
    console.log(`Plataforma: ${electronPlatformName}`);
    
    // Verificar possíveis locais onde os recursos podem estar
    const possibleResourcePaths = [
      // Caminho padrão
      path.join(appOutDir, electronPlatformName === 'darwin' ? 'Neko TV.app/Contents/Resources/app' : 'resources/app'),
      // Verificar diretamente no diretório app.asar
      path.join(appOutDir, electronPlatformName === 'darwin' ? 'Neko TV.app/Contents/Resources/app.asar' : 'resources/app.asar'),
      // Verificar no diretório de saída
      appOutDir,
      // Verificar no app
      path.join(appOutDir, 'app'),
      // Verificar no diretório resources
      path.join(appOutDir, 'resources')
    ];
    
    let foundFiles = false;
    
    for (const resourcesPath of possibleResourcePaths) {
      console.log(`Verificando em: ${resourcesPath}`);
      
      if (!fs.existsSync(resourcesPath)) {
        console.log(`Diretório ${resourcesPath} não existe.`);
        continue;
      }
      
      // Verificar conteúdo do diretório
      try {
        const files = fs.readdirSync(resourcesPath);
        console.log(`Conteúdo de ${resourcesPath}:`, files);
      } catch (err) {
        console.log(`Não foi possível ler o diretório ${resourcesPath}: ${err.message}`);
      }
      
      // Locais para verificar node_modules
      const possibleNodeModulesPaths = [
        path.join(resourcesPath, 'node_modules'),
        path.join(resourcesPath, '../node_modules')
      ];
      
      for (const nodeModulesPath of possibleNodeModulesPaths) {
        if (fs.existsSync(nodeModulesPath)) {
          console.log(`node_modules encontrado em: ${nodeModulesPath}`);
          
          // Verificar node-machine-id
          const machinIdPath = path.join(nodeModulesPath, 'node-machine-id');
          if (fs.existsSync(machinIdPath)) {
            console.log('✅ node-machine-id foi empacotado corretamente');
            foundFiles = true;
          } else {
            console.log('❌ node-machine-id não foi encontrado em', nodeModulesPath);
          }
          
          // Verificar electron-store
          const electronStorePath = path.join(nodeModulesPath, 'electron-store');
          if (fs.existsSync(electronStorePath)) {
            console.log('✅ electron-store foi empacotado corretamente');
            foundFiles = true;
          } else {
            console.log('❌ electron-store não foi encontrado em', nodeModulesPath);
          }
        }
      }
      
      // Verificar o main.cjs em diferentes locais
      const possibleMainPaths = [
        path.join(resourcesPath, 'electron/main.cjs'),
        path.join(resourcesPath, 'main.cjs'),
        path.join(appOutDir, 'electron/main.cjs'),
        path.join(appOutDir, 'main.cjs')
      ];
      
      for (const mainPath of possibleMainPaths) {
        if (fs.existsSync(mainPath)) {
          console.log(`✅ main.cjs foi encontrado em: ${mainPath}`);
          foundFiles = true;
        }
      }
    }
    
    if (!foundFiles) {
      console.log('⚠️ Não foi possível encontrar os arquivos necessários no pacote. Verifique a configuração do electron-builder.');
    }
    
    console.log('Script after-pack concluído com sucesso!');
  } catch (error) {
    console.error('Erro no script after-pack:', error);
  }
}; 