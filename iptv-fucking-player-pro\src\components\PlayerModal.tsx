import React, { useState, useEffect } from 'react'
import { Fi<PERSON>lock, FiPlay } from 'react-icons/fi'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { EPGProgram } from '../services/epgService'
import useEPG from '../hooks/useEPG'
import VideoPlayer from './VideoPlayer'

interface PlayerModalProps {
  isOpen: boolean
  onClose: () => void
  streamUrl: string
  channelName: string
  channelId: string
}

const PlayerModal: React.FC<PlayerModalProps> = ({
  isOpen,
  onClose,
  streamUrl,
  channelName,
  channelId
}) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [playerError, setPlayerError] = useState<string | null>(null)
  
  const { 
    getAllPrograms, 
    getCurrentProgram, 
    formatTime, 
    formatDuration,
    isLoadingEPG 
  } = useEPG()

  const allPrograms = getAllPrograms(channelId)
  const currentProgram = getCurrentProgram(channelId)

  // Filter programs by selected date
  const dayPrograms = allPrograms.filter(program => {
    const programDate = new Date(program.startTime)
    return programDate.toDateString() === selectedDate.toDateString()
  })

  // Generate date options (today and past 7 days)
  const dateOptions = []
  for (let i = 0; i < 8; i++) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dateOptions.push(date)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative w-full h-full flex bg-slate-900">
        {/* Video Player Section */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-slate-800 border-b border-slate-700">
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <h2 className="text-xl font-semibold text-white">{channelName}</h2>
                
                {/* Stream info badges */}
                <div className="flex items-center space-x-2">
                  {streamUrl.includes('.m3u8') && (
                    <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">HLS</span>
                  )}
                  {streamUrl.includes('4k') || streamUrl.includes('2160p') && (
                    <span className="px-2 py-1 bg-purple-600 text-white text-xs rounded">4K</span>
                  )}
                  {streamUrl.includes('1080p') && (
                    <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">HD</span>
                  )}
                  {(streamUrl.includes('h265') || streamUrl.includes('hevc')) && (
                    <span className="px-2 py-1 bg-orange-600 text-white text-xs rounded">H.265</span>
                  )}
                  {isPlaying && (
                    <div className="flex items-center text-green-400 text-xs">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                      LIVE
                    </div>
                  )}
                </div>
              </div>
              
              {currentProgram && (
                <p className="text-sm text-slate-300 mt-1">
                  {currentProgram.title} • {formatTime(currentProgram.startTime)} - {formatTime(currentProgram.endTime)}
                </p>
              )}
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-slate-400 hover:text-white transition-colors ml-4"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Video Player */}
          <div className="flex-1 bg-black">
            {streamUrl ? (
              <VideoPlayer
                src={streamUrl}
                autoPlay={true}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onError={(error) => {
                  console.error('Player error:', error);
                  setPlayerError('Failed to load stream. Please check your connection or try another channel.');
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-white">
                <div className="text-center">
                  <div className="text-4xl mb-4">📺</div>
                  <p>No stream URL available</p>
                </div>
              </div>
            )}
            
            {/* Error overlay */}
            {playerError && (
              <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                <div className="text-center text-white max-w-md p-6">
                  <div className="text-red-400 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-semibold mb-2">Stream Error</h3>
                  <p className="text-slate-300 mb-4">{playerError}</p>
                  
                  {/* Compatibility info */}
                  <div className="text-left bg-slate-800 rounded-lg p-4 mb-4 text-sm">
                    <h4 className="font-semibold mb-2">Supported Formats:</h4>
                    <ul className="space-y-1 text-slate-300">
                      <li>• HLS (.m3u8) - ✅ Full support</li>
                      <li>• MP4 - ✅ Full support</li>
                      <li>• WebM - ✅ Full support</li>
                      <li>• H.264/H.265 - ✅ Hardware accelerated</li>
                      <li>• 4K/UHD - ✅ Supported</li>
                      <li>• MKV - ⚠️ Limited browser support</li>
                      <li>• RTMP - ❌ Not supported in browsers</li>
                    </ul>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setPlayerError(null)}
                      className="px-4 py-2 bg-sky-500 hover:bg-sky-600 rounded-lg transition-colors"
                    >
                      Try Again
                    </button>
                    <button
                      onClick={onClose}
                      className="px-4 py-2 bg-slate-600 hover:bg-slate-700 rounded-lg transition-colors"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Current Program Info */}
          {currentProgram && (
            <div className="p-4 bg-slate-800 border-t border-slate-700">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-white">{currentProgram.title}</h3>
                <div className="flex items-center text-sm text-green-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                  LIVE
                </div>
              </div>
              
              {/* Progress bar */}
              <div className="w-full bg-slate-600 rounded-full h-2 mb-2">
                <div
                  className="bg-sky-500 h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${currentProgram.progress}%` }}
                />
              </div>
              
              <div className="flex items-center justify-between text-sm text-slate-300">
                <span>{formatTime(currentProgram.startTime)} - {formatTime(currentProgram.endTime)}</span>
                <span>{formatDuration(currentProgram.startTime, currentProgram.endTime)}</span>
              </div>
              
              {currentProgram.description && (
                <p className="text-sm text-slate-400 mt-2 line-clamp-2">
                  {currentProgram.description}
                </p>
              )}
            </div>
          )}
        </div>

        {/* EPG Sidebar */}
        <div className="w-96 bg-slate-800 border-l border-slate-700 flex flex-col">
          {/* EPG Header */}
          <div className="p-4 border-b border-slate-700">
            <h3 className="text-lg font-semibold text-white mb-3">Program Guide</h3>
            
            {/* Date Selector */}
            <select
              value={selectedDate.toDateString()}
              onChange={(e) => setSelectedDate(new Date(e.target.value))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-sky-500"
            >
              {dateOptions.map((date, index) => (
                <option key={date.toDateString()} value={date.toDateString()}>
                  {index === 0 ? 'Today' : 
                   index === 1 ? 'Yesterday' : 
                   date.toLocaleDateString([], { weekday: 'long', month: 'short', day: 'numeric' })}
                </option>
              ))}
            </select>
          </div>

          {/* Programs List */}
          <div className="flex-1 overflow-y-auto">
            {isLoadingEPG ? (
              <div className="p-4 text-center">
                <div className="animate-spin w-6 h-6 border-2 border-sky-500 border-t-transparent rounded-full mx-auto mb-2" />
                <p className="text-sm text-slate-400">Loading EPG...</p>
              </div>
            ) : dayPrograms.length > 0 ? (
              <div className="p-4 space-y-3">
                {dayPrograms.map((program) => (
                  <div
                    key={program.id}
                    className={`p-3 rounded-lg border transition-all ${
                      program.isLive
                        ? 'bg-sky-500/20 border-sky-500/50'
                        : new Date() > program.endTime
                        ? 'bg-slate-700/30 border-slate-600/50 opacity-60'
                        : 'bg-slate-700/50 border-slate-600/50 hover:bg-slate-700'
                    }`}
                  >
                    {/* Time and status */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center text-xs text-slate-300">
                        <FiClock className="w-3 h-3 mr-1" />
                        {formatTime(program.startTime)} - {formatTime(program.endTime)}
                      </div>
                      
                      {program.isLive && (
                        <div className="flex items-center text-xs text-green-400">
                          <FiPlay className="w-3 h-3 mr-1" />
                          LIVE
                        </div>
                      )}
                      
                      {new Date() > program.endTime && (
                        <span className="text-xs text-slate-500">Ended</span>
                      )}
                    </div>

                    {/* Program title */}
                    <h4 className="font-medium text-white text-sm mb-1">
                      {program.title}
                    </h4>

                    {/* Progress bar for live program */}
                    {program.isLive && (
                      <div className="w-full bg-slate-600 rounded-full h-1 mb-2">
                        <div
                          className="bg-sky-500 h-1 rounded-full transition-all duration-1000"
                          style={{ width: `${program.progress}%` }}
                        />
                      </div>
                    )}

                    {/* Description */}
                    {program.description && (
                      <p className="text-xs text-slate-400 line-clamp-3 mb-2">
                        {program.description}
                      </p>
                    )}

                    {/* Category and duration */}
                    <div className="flex items-center justify-between">
                      {program.category && (
                        <span className="px-2 py-0.5 bg-slate-600 text-xs text-slate-300 rounded">
                          {program.category}
                        </span>
                      )}
                      <span className="text-xs text-slate-500">
                        {formatDuration(program.startTime, program.endTime)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center">
                <p className="text-sm text-slate-400">No EPG data available for this date</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default PlayerModal