import React, { useState } from 'react';
import { useLicense, useAllLicenses } from '../hooks/useLicense';
import { Box, Button, TextField, Typography, Card, CardContent, Alert, CircularProgress } from '@mui/material';

interface LicenseManagerProps {
  isAdmin?: boolean;
}

export const LicenseManager: React.FC<LicenseManagerProps> = ({ isAdmin = false }) => {
  const [licenseKey, setLicenseKey] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [clientName, setClientName] = useState('');
  const [iptvUrl, setIptvUrl] = useState('');

  const { isValid, isLoading: checkLoading, error: checkError, checkLicense } = useLicense();
  const { 
    licenses, 
    isLoading: adminLoading, 
    error: adminError, 
    generateLicense, 
    toggleLicense, 
    deleteLicense 
  } = useAllLicenses();

  const handleCheckLicense = async () => {
    if (!licenseKey || !deviceId) {
      alert('Por favor, preencha a chave da licença e o ID do dispositivo');
      return;
    }

    try {
      const result = await checkLicense(licenseKey, deviceId);
      if (result.valid) {
        alert(`Licença válida! URL IPTV: ${result.iptv_url || 'Não configurada'}`);
      } else {
        alert(`Licença inválida: ${result.message}`);
      }
    } catch (error) {
      console.error('Erro ao verificar licença:', error);
    }
  };

  const handleGenerateLicense = async () => {
    try {
      const result = await generateLicense(clientName || undefined, iptvUrl || undefined);
      alert(`Nova licença gerada: ${result.license_key}`);
      setClientName('');
      setIptvUrl('');
    } catch (error) {
      console.error('Erro ao gerar licença:', error);
    }
  };

  const handleToggleLicense = async (key: string) => {
    try {
      await toggleLicense(key);
      alert('Status da licença alterado com sucesso!');
    } catch (error) {
      console.error('Erro ao alterar licença:', error);
    }
  };

  const handleDeleteLicense = async (id: string) => {
    if (window.confirm('Tem certeza que deseja deletar esta licença?')) {
      try {
        await deleteLicense(id);
        alert('Licença deletada com sucesso!');
      } catch (error) {
        console.error('Erro ao deletar licença:', error);
      }
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Gerenciador de Licenças
      </Typography>

      {/* Seção de Verificação de Licença */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Verificar Licença
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              label="Chave da Licença"
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              placeholder="Ex: ABCD-1234-EFGH-5678"
              fullWidth
            />
            <TextField
              label="ID do Dispositivo"
              value={deviceId}
              onChange={(e) => setDeviceId(e.target.value)}
              placeholder="Ex: DEV-12345"
              fullWidth
            />
          </Box>

          <Button 
            variant="contained" 
            onClick={handleCheckLicense}
            disabled={checkLoading}
            startIcon={checkLoading ? <CircularProgress size={20} /> : null}
          >
            {checkLoading ? 'Verificando...' : 'Verificar Licença'}
          </Button>

          {checkError && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {checkError}
            </Alert>
          )}

          {isValid && (
            <Alert severity="success" sx={{ mt: 2 }}>
              Licença válida!
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Seção Admin */}
      {isAdmin && (
        <>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gerar Nova Licença
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <TextField
                  label="Nome do Cliente"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Ex: João Silva"
                  fullWidth
                />
                <TextField
                  label="URL IPTV"
                  value={iptvUrl}
                  onChange={(e) => setIptvUrl(e.target.value)}
                  placeholder="Ex: http://iptv.example.com"
                  fullWidth
                />
              </Box>

              <Button 
                variant="contained" 
                color="primary"
                onClick={handleGenerateLicense}
                disabled={adminLoading}
                startIcon={adminLoading ? <CircularProgress size={20} /> : null}
              >
                {adminLoading ? 'Gerando...' : 'Gerar Licença'}
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Licenças Existentes
              </Typography>

              {adminError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {adminError}
                </Alert>
              )}

              {adminLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {licenses.map((license) => (
                    <Card key={license.id} variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {license.license_key}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Cliente: {license.client_name || 'Não informado'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Status: {license.active ? 'Ativa' : 'Inativa'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Dispositivo: {license.device_id || 'Não vinculado'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          URL IPTV: {license.iptv_url || 'Não configurada'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Criada em: {new Date(license.created_at).toLocaleDateString('pt-BR')}
                        </Typography>

                        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            color={license.active ? 'warning' : 'success'}
                            onClick={() => handleToggleLicense(license.license_key)}
                          >
                            {license.active ? 'Desativar' : 'Ativar'}
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            color="error"
                            onClick={() => handleDeleteLicense(license.id)}
                          >
                            Deletar
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  ))}

                  {licenses.length === 0 && (
                    <Typography variant="body2" color="text.secondary" textAlign="center">
                      Nenhuma licença encontrada
                    </Typography>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </Box>
  );
};
