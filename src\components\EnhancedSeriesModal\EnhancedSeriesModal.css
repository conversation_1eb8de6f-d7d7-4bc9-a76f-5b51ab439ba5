/* Enhanced Series Modal - Improved Spacing and Layout */

/* FORÇA O MODAL A FICAR ACIMA DE TUDO, MAS ABAIXO DOS MODAIS DE PESQUISA */
.MuiDialog-root:not([aria-labelledby="search-results-modal"]) {
  z-index: 50000 !important;
}

.MuiDialog-container:not([aria-labelledby="search-results-modal"]) {
  z-index: 50000 !important;
}

.MuiDialog-paper:not([aria-labelledby="search-results-modal"]) {
  z-index: 50000 !important;
}

.MuiBackdrop-root:not([aria-labelledby="search-results-modal"]) {
  z-index: 49999 !important;
}

.enhanced-series-modal {
  /* Garantir que o modal tenha zoom adequado */
  transform: scale(0.95);
  transform-origin: center;
  transition: transform 0.3s ease;
  z-index: 99999 !important;
  position: relative;
}

.enhanced-series-modal.open {
  transform: scale(1);
}

/* Garantir que NENHUMA navegação interfira */
body:has(.enhanced-series-modal) .MuiAppBar-root,
body:has(.enhanced-series-modal) .neko-app-bar,
body:has(.enhanced-series-modal) nav,
body:has(.enhanced-series-modal) header,
body:has(.enhanced-series-modal) [role="navigation"] {
  z-index: 1000 !important;
}

/* Melhorar o scroll customizado */
.enhanced-series-modal .custom-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.3) transparent;
}

.enhanced-series-modal .custom-scroll::-webkit-scrollbar {
  width: 6px;
}

.enhanced-series-modal .custom-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.enhanced-series-modal .custom-scroll::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.enhanced-series-modal .custom-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.5);
}

/* Compactar layout geral */
.enhanced-series-modal .left-panel {
  padding: 0 0.5rem;
}

.enhanced-series-modal .series-poster {
  max-width: 200px !important;
  margin-bottom: 1rem;
}

.enhanced-series-modal .series-title {
  margin-bottom: 0.5rem !important;
  line-height: 1.1 !important;
}

/* Otimizar espaçamento em dispositivos móveis */
@media (max-width: 768px) {
  .enhanced-series-modal {
    transform: scale(0.98);
  }
  
  .enhanced-series-modal .series-poster {
    max-width: 180px !important;
  }
  
  .enhanced-series-modal .series-title {
    font-size: 1.3rem !important;
  }
}

/* Melhorar a responsividade do layout */
@media (max-width: 1200px) {
  .enhanced-series-modal .left-panel {
    max-width: 320px;
  }
}

/* Animação suave para elementos */
.enhanced-series-modal .fade-in {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Melhorar contraste e legibilidade */
.enhanced-series-modal .text-shadow {
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
}

/* Otimizar o backdrop blur */
.enhanced-series-modal .backdrop-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
/* FORÇA ABSOLUTA - MODAL ACIMA DE TUDO, MAS ABAIXO DOS MODAIS DE PESQUISA */
.enhanced-series-modal,
.enhanced-series-modal * {
  z-index: 50000 !important;
}

/* Sobrescrever qualquer z-index de navegação */
.MuiAppBar-root:not(.search-modal-appbar),
.neko-app-bar:not(.search-modal-appbar),
nav:not(.search-modal-nav),
header:not(.search-modal-header),
[role="navigation"]:not(.search-modal-navigation),
.main-navigation:not(.search-modal-navigation),
.app-bar:not(.search-modal-appbar) {
  z-index: 9999 !important;
}

/* Garantir que o modal seja sempre visível */
.enhanced-series-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 50000 !important;
}

/* Compactar ainda mais o Auto-Skip */
.enhanced-series-modal .auto-skip-panel {
  padding: 1rem !important;
  margin-top: 1rem !important;
}

/* Reduzir espaçamentos gerais */
.enhanced-series-modal .MuiStack-root {
  gap: 1rem !important;
}

.enhanced-series-modal .MuiChip-root {
  height: 24px !important;
  font-size: 0.7rem !important;
}