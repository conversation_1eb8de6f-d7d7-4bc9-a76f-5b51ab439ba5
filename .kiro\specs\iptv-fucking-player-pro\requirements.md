# Requirements Document

## Introduction

O IPTV FUCKING PLAYER PRO é um aplicativo Electron moderno e estiloso para reprodução de conteúdo IPTV. O aplicativo deve ser multilíngue (português, inglês e espanhol), responsivo e focado na experiência do usuário. O objetivo principal é criar um reprodutor limpo e funcional que permita aos usuários assistir canais ao vivo, filmes e séries através de serviços IPTV, com suporte completo ao EPG (Electronic Program Guide) XML.

## Requirements

### Requirement 1

**User Story:** <PERSON> usuário, quero me conectar ao meu serviço IPTV usando minhas credenciais através de um modal obrigatório elegante, para que eu possa acessar todo o conteúdo disponível de forma segura.

#### Acceptance Criteria

1. WHEN o aplicativo for iniciado pela primeira vez THEN o sistema SHALL exibir um modal obrigatório para inserção de credenciais IPTV
2. WHEN o usuário inserir URL do servidor, username e password THEN o sistema SHALL validar as credenciais através do endpoint `/player_api.php`
3. WHEN a autenticação for bem-sucedida THEN o sistema SHALL armazenar as credenciais criptografadas no banco de dados local
4. WHEN a autenticação falhar THEN o sistema SHALL exibir uma mensagem de erro clara no modal
5. WHEN não houver conexões salvas THEN o sistema SHALL bloquear o acesso ao aplicativo até que credenciais válidas sejam fornecidas
6. WHEN o modal for exibido THEN o sistema SHALL usar um design elegante e moderno com animações suaves

### Requirement 2

**User Story:** Como usuário, quero navegar por categorias de canais, filmes e séries, para que eu possa encontrar facilmente o conteúdo que desejo assistir.

#### Acceptance Criteria

1. WHEN o usuário acessar a seção de canais THEN o sistema SHALL buscar categorias através do endpoint `action=get_live_categories`
2. WHEN o usuário acessar a seção de filmes THEN o sistema SHALL buscar categorias através do endpoint `action=get_vod_categories`
3. WHEN o usuário acessar a seção de séries THEN o sistema SHALL buscar categorias através do endpoint `action=get_series_categories`
4. WHEN uma categoria for selecionada THEN o sistema SHALL exibir o conteúdo correspondente

### Requirement 3

**User Story:** Como usuário, quero visualizar listas de canais, filmes e séries por categoria, para que eu possa escolher o que assistir.

#### Acceptance Criteria

1. WHEN o usuário selecionar uma categoria de canais THEN o sistema SHALL buscar streams através do endpoint `action=get_live_streams&category_id={id}`
2. WHEN o usuário selecionar uma categoria de filmes THEN o sistema SHALL buscar streams através do endpoint `action=get_vod_streams&category_id={id}`
3. WHEN o usuário selecionar uma categoria de séries THEN o sistema SHALL buscar streams através do endpoint `action=get_series&category_id={id}`
4. WHEN os streams forem carregados THEN o sistema SHALL exibir thumbnails, títulos e informações básicas

### Requirement 4

**User Story:** Como usuário, quero reproduzir canais ao vivo, filmes e episódios de séries, para que eu possa assistir ao conteúdo desejado.

#### Acceptance Criteria

1. WHEN o usuário selecionar um canal THEN o sistema SHALL reproduzir usando a URL `{baseUrl}/live/{username}/{password}/{streamId}.m3u8`
2. WHEN o usuário selecionar um filme THEN o sistema SHALL reproduzir usando a URL `{baseUrl}/movie/{username}/{password}/{streamId}.mp4`
3. WHEN o usuário selecionar um episódio THEN o sistema SHALL reproduzir usando a URL `{baseUrl}/series/{username}/{password}/{streamId}.mp4`
4. WHEN a reprodução iniciar THEN o sistema SHALL usar um player moderno com controles completos

### Requirement 5

**User Story:** Como usuário, quero visualizar informações detalhadas sobre séries e seus episódios, para que eu possa navegar entre temporadas e episódios.

#### Acceptance Criteria

1. WHEN o usuário selecionar uma série THEN o sistema SHALL buscar informações através do endpoint `action=get_series_info&series_id={id}`
2. WHEN as informações da série forem carregadas THEN o sistema SHALL exibir temporadas e episódios
3. WHEN o usuário selecionar uma temporada THEN o sistema SHALL buscar episódios através do endpoint `action=get_series_episodes&series_id={id}&season={number}`
4. WHEN um episódio for selecionado THEN o sistema SHALL iniciar a reprodução

### Requirement 6

**User Story:** Como usuário, quero visualizar o guia de programação (EPG) dos canais, para que eu possa saber o que está passando e o que vai passar.

#### Acceptance Criteria

1. WHEN o usuário acessar um canal THEN o sistema SHALL buscar EPG através do endpoint `/xmltv.php?username={username}&password={password}`
2. WHEN o EPG for carregado THEN o sistema SHALL exibir programação atual e futura
3. WHEN um programa estiver em exibição THEN o sistema SHALL destacar visualmente
4. IF não houver EPG disponível THEN o sistema SHALL exibir uma mensagem informativa

### Requirement 7

**User Story:** Como usuário, quero que o aplicativo tenha um banco de dados local simples, para que minhas configurações e cache sejam armazenados localmente.

#### Acceptance Criteria

1. WHEN o aplicativo for iniciado THEN o sistema SHALL inicializar um banco SQLite local
2. WHEN dados forem salvos THEN o sistema SHALL armazenar no banco local
3. WHEN o aplicativo for fechado THEN o sistema SHALL manter os dados persistidos
4. WHEN necessário THEN o sistema SHALL permitir limpeza do cache

### Requirement 8

**User Story:** Como usuário, quero uma interface moderna e estilosa, para que eu tenha uma experiência visual agradável.

#### Acceptance Criteria

1. WHEN o aplicativo for aberto THEN o sistema SHALL exibir uma interface com design moderno
2. WHEN o usuário navegar THEN o sistema SHALL usar animações suaves e transições
3. WHEN em modo escuro THEN o sistema SHALL usar paleta de cores apropriada
4. WHEN redimensionado THEN o sistema SHALL manter responsividade

### Requirement 9

**User Story:** Como usuário, quero um menu lateral expansível, para que eu possa navegar facilmente entre as seções do aplicativo.

#### Acceptance Criteria

1. WHEN o aplicativo for carregado THEN o sistema SHALL exibir um menu lateral colapsível
2. WHEN o menu for expandido THEN o sistema SHALL mostrar ícones e labels das seções
3. WHEN o menu for colapsado THEN o sistema SHALL mostrar apenas ícones
4. WHEN uma seção for selecionada THEN o sistema SHALL destacar visualmente

### Requirement 10

**User Story:** Como usuário, quero que o aplicativo seja multilíngue, para que eu possa usar em português, inglês ou espanhol.

#### Acceptance Criteria

1. WHEN o aplicativo for iniciado THEN o sistema SHALL detectar o idioma do sistema
2. WHEN o usuário alterar o idioma THEN o sistema SHALL atualizar toda a interface
3. WHEN textos forem exibidos THEN o sistema SHALL usar o idioma selecionado
4. IF um idioma não estiver disponível THEN o sistema SHALL usar inglês como padrão

### Requirement 11

**User Story:** Como usuário, quero gerenciar múltiplas listas IPTV simultaneamente através de um sistema de abas, para que eu possa alternar facilmente entre diferentes provedores.

#### Acceptance Criteria

1. WHEN o usuário adicionar uma nova conexão IPTV THEN o sistema SHALL criar uma nova aba para essa conexão
2. WHEN múltiplas conexões estiverem ativas THEN o sistema SHALL exibir abas similares ao Google Chrome
3. WHEN o usuário clicar em uma aba THEN o sistema SHALL alternar para a conexão correspondente
4. WHEN uma aba for fechada THEN o sistema SHALL remover a conexão da sessão atual mas manter no banco de dados
5. WHEN houver apenas uma aba THEN o sistema SHALL ocultar a barra de abas
6. WHEN o usuário clicar no botão "+" THEN o sistema SHALL abrir o modal de adição de nova conexão

### Requirement 12

**User Story:** Como usuário, quero um gerenciador de conexões IPTV integrado, para que eu possa facilmente adicionar, editar, remover e alternar entre diferentes listas.

#### Acceptance Criteria

1. WHEN o usuário acessar o gerenciador de conexões THEN o sistema SHALL exibir todas as conexões salvas
2. WHEN o usuário editar uma conexão THEN o sistema SHALL atualizar os dados no banco de dados
3. WHEN o usuário remover uma conexão THEN o sistema SHALL excluir permanentemente do banco de dados
4. WHEN o usuário marcar uma conexão como favorita THEN o sistema SHALL priorizar na listagem
5. WHEN uma conexão for testada THEN o sistema SHALL exibir o status de conectividade
6. WHEN o usuário duplicar uma conexão THEN o sistema SHALL criar uma cópia com nome diferente

### Requirement 13

**User Story:** Como usuário, quero que o aplicativo seja testado regularmente, para que eu tenha uma experiência estável e confiável.

#### Acceptance Criteria

1. WHEN funcionalidades forem implementadas THEN o sistema SHALL incluir testes automatizados
2. WHEN endpoints forem chamados THEN o sistema SHALL validar respostas
3. WHEN erros ocorrerem THEN o sistema SHALL ter tratamento adequado
4. WHEN o aplicativo for atualizado THEN o sistema SHALL passar por testes de regressão