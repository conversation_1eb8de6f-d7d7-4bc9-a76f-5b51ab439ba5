import { corsProxyService } from '../services/corsProxyService';

/**
 * Utilitário para testar o proxy CORS
 */
export class CorsProxyTester {
  /**
   * Testa a conexão com o Pastebin usando proxy
   */
  public static async testPastebinConnection(pastebinUrl: string = 'https://pastebin.com/raw/qfZnbqc9?t=' + Date.now()): Promise<void> {
    console.log('🧪 Testando conexão com Pastebin via proxy...');
    console.log(`📌 URL: ${pastebinUrl}`);
    
    // Primeiro tenta acesso direto para diagnóstico
    console.log('🔍 Tentando acesso direto primeiro (para diagnóstico)...');
    try {
      const directResponse = await fetch(pastebinUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache',
        },
        cache: 'no-cache',
      });
      
      if (directResponse.ok) {
        console.log('✅ Acesso direto funcionou! Status:', directResponse.status);
        console.log('📋 Headers:', {
          'content-type': directResponse.headers.get('content-type'),
          'cors': directResponse.headers.get('access-control-allow-origin'),
        });
      } else {
        console.log('❌ Acesso direto falhou. Status:', directResponse.status, directResponse.statusText);
      }
    } catch (directError) {
      console.log('❌ Erro no acesso direto:', directError instanceof Error ? directError.message : 'Erro desconhecido');
    }
    
    // Agora tenta via proxy
    console.log('🔄 Tentando via proxy CORS...');
    try {
      const startTime = performance.now();
      const data = await corsProxyService.fetchText(pastebinUrl);
      const endTime = performance.now();
      
      console.log(`✅ Conexão com Pastebin funcionou! (${(endTime - startTime).toFixed(0)}ms)`);
      console.log('📄 Dados recebidos:', data.substring(0, 200) + (data.length > 200 ? '...' : ''));
      
      // Tentar fazer parse do JSON
      try {
        const json = JSON.parse(data);
        console.log('✅ JSON válido recebido!');
        console.log('📊 Estrutura:', {
          enabled: json.enabled,
          messagesCount: json.messages?.length || 0,
          defaultDuration: json.defaultDuration
        });
        return;
      } catch (parseError) {
        console.log('❌ Erro ao fazer parse do JSON:', parseError instanceof Error ? parseError.message : 'Erro desconhecido');
        console.log('📝 Conteúdo não é JSON válido. Primeiros 50 caracteres:', data.substring(0, 50));
      }
    } catch (error) {
      console.error('❌ Erro ao testar Pastebin via proxy:', error instanceof Error ? error.message : 'Erro desconhecido');
    }
  }
  
  /**
   * Testa todos os proxies disponíveis
   */
  public static async testAllProxies(): Promise<void> {
    console.log('🧪 Testando todos os proxies CORS...');
    
    const workingProxies = await corsProxyService.getWorkingProxies();
    
    console.log(`✅ Proxies funcionando: ${workingProxies.length}`);
    workingProxies.forEach((proxy: string, index: number) => {
      console.log(`${index + 1}. ${proxy}`);
    });
    
    if (workingProxies.length === 0) {
      console.log('❌ Nenhum proxy está funcionando!');
    }
  }
  
  /**
   * Executa todos os testes
   */
  public static async runAllTests(): Promise<void> {
    console.log('🚀 Iniciando testes do proxy CORS...');
    
    await this.testAllProxies();
    await this.testPastebinConnection();
    
    console.log('🏁 Testes concluídos!');
  }
}

// Disponibilizar globalmente para testes no console
if (typeof window !== 'undefined') {
  (window as any).testCorsProxy = CorsProxyTester;
  console.log('💡 Para testar o proxy CORS, execute no console:');
  console.log('testCorsProxy.runAllTests() - Testes completos');
  console.log('testCorsProxy.testPastebinConnection() - Apenas Pastebin');
  console.log('testCorsProxy.testAllProxies() - Apenas proxies');
}

export default CorsProxyTester;
