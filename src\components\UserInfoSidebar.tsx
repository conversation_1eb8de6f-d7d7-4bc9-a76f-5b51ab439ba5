import React, { useState, useEffect } from 'react';
import { UserInfo, ServerInfo, createIPTVService } from '../services/iptvService';
import { FaUser, FaServer, FaClock, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';

const UserInfoSidebar: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [serverInfo, setServerInfo] = useState<ServerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        setLoading(true);
        setError(null);
        const iptvService = await createIPTVService();
        const response = await iptvService.getUserInfo();
        
        if (response && typeof response === 'object') {
          // Safely access nested properties
          const userInfoData = response.user_info || null;
          const serverInfoData = response.server_info || null;
          
          setUserInfo(userInfoData);
          setServerInfo(serverInfoData);
        } else {
          throw new Error('Invalid response format from server');
        }
      } catch (err) {
        setError('Failed to load user information');
        console.error('Error loading user info:', err);
      } finally {
        setLoading(false);
      }
    };

    loadUserInfo();
  }, []);

  const formatDate = (timestamp: string) => {
    return new Date(parseInt(timestamp) * 1000).toLocaleDateString();
  };

  const formatTimeLeft = (expDate: string) => {
    const now = Math.floor(Date.now() / 1000);
    const exp = parseInt(expDate);
    const diff = exp - now;

    if (diff <= 0) return 'Expired';

    const days = Math.floor(diff / (24 * 60 * 60));
    const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60));

    return `${days}d ${hours}h remaining`;
  };

  if (loading) {
    return <div className="user-info-sidebar loading">Loading user info...</div>;
  }

  if (error) {
    return <div className="user-info-sidebar error">{error}</div>;
  }

  return (
    <div className="user-info-sidebar">
      <div className="sidebar-section">
        <h2>
          <FaUser className="section-icon" />
          User Information
        </h2>
        {userInfo && (
          <div className="info-content">
            <div className="info-item">
              <strong>Username:</strong>
              <span>{userInfo.username}</span>
            </div>
            <div className="info-item">
              <strong>Status:</strong>
              <span className={`status ${userInfo.status.toLowerCase()}`}>
                {userInfo.status === 'Active' ? (
                  <FaCheckCircle className="status-icon" />
                ) : (
                  <FaTimesCircle className="status-icon" />
                )}
                {userInfo.status}
              </span>
            </div>
            <div className="info-item">
              <strong>Expires:</strong>
              <span>{formatTimeLeft(userInfo.exp_date)}</span>
            </div>
            <div className="info-item">
              <strong>Created:</strong>
              <span>{formatDate(userInfo.created_at)}</span>
            </div>
            <div className="info-item">
              <strong>Active Connections:</strong>
              <span>{userInfo.active_cons} / {userInfo.max_connections}</span>
            </div>
            <div className="info-item">
              <strong>Trial Account:</strong>
              <span>{userInfo.is_trial === '1' ? 'Yes' : 'No'}</span>
            </div>
          </div>
        )}
      </div>

      <div className="sidebar-section">
        <h2>
          <FaServer className="section-icon" />
          Server Information
        </h2>
        {serverInfo && (
          <div className="info-content">
            <div className="info-item">
              <strong>URL:</strong>
              <span>{serverInfo.url}</span>
            </div>
            <div className="info-item">
              <strong>Protocol:</strong>
              <span>{serverInfo.server_protocol}</span>
            </div>
            <div className="info-item">
              <strong>Timezone:</strong>
              <span>{serverInfo.timezone}</span>
            </div>
            <div className="info-item">
              <strong>Server Time:</strong>
              <span>{serverInfo.time_now}</span>
            </div>
          </div>
        )}
      </div>

      <style>{`
        .user-info-sidebar {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 1rem;
          padding: 1.5rem;
          color: white;
          width: 300px;
          height: 100%;
        }

        .sidebar-section {
          margin-bottom: 2rem;
        }

        .sidebar-section:last-child {
          margin-bottom: 0;
        }

        .sidebar-section h2 {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #60a5fa;
        }

        .section-icon {
          font-size: 1.2rem;
        }

        .info-content {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem;
          background: rgba(255, 255, 255, 0.03);
          border-radius: 0.5rem;
        }

        .info-item strong {
          color: #94a3b8;
          font-size: 0.875rem;
        }

        .info-item span {
          font-size: 0.875rem;
        }

        .status {
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .status.active {
          color: #22c55e;
        }

        .status.expired {
          color: #ef4444;
        }

        .status-icon {
          font-size: 0.875rem;
        }

        .user-info-sidebar.loading,
        .user-info-sidebar.error {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 200px;
          font-size: 0.875rem;
        }

        .user-info-sidebar.error {
          color: #ef4444;
        }

        @media (max-width: 768px) {
          .user-info-sidebar {
            width: 100%;
            margin-bottom: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default UserInfoSidebar; 