export interface Connection {
  id: string;
  url: string;
  username: string;
  password: string;
  name?: string;
  lastUsed: number;
}

export interface Category {
  id: string;
  name: string;
  type: 'live' | 'movie' | 'series';
}

export interface Stream {
  id: string;
  name: string;
  thumbnail?: string;
  categoryId: string;
  type: 'live' | 'movie' | 'series';
  url?: string;
  description?: string;
  rating?: number;
  year?: string;
  genres?: string[];
  duration?: number;
}

export interface SeriesInfo extends Stream {
  seasons: Season[];
  episodeCount: number;
}

export interface Season {
  id: number;
  name: string;
  episodes: Episode[];
}

export interface Episode {
  id: string;
  title: string;
  season: number;
  episode: number;
  thumbnail?: string;
  description?: string;
  duration?: number;
  url: string;
}

export interface EPGChannel {
  id: string;
  name: string;
  programs: EPGProgram[];
}

export interface EPGProgram {
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  isLive?: boolean;
}

export interface AuthResult {
  success: boolean;
  userInfo?: UserInfo;
  serverInfo?: ServerInfo;
  error?: string;
}

export interface UserInfo {
  username: string;
  password: string;
  message: string;
  auth: number;
  status: string;
  exp_date: string;
  is_trial: string;
  active_cons: string;
  created_at: string;
  max_connections: string;
  allowed_output_formats: string[];
}

export interface ServerInfo {
  url: string;
  port: string;
  https_port: string;
  server_protocol: string;
  rtmp_port: string;
  timezone: string;
  timestamp_now: number;
  time_now: string;
}

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  STREAM_ERROR = 'STREAM_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

export interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: number;
}

export type StreamType = 'live' | 'movie' | 'series';